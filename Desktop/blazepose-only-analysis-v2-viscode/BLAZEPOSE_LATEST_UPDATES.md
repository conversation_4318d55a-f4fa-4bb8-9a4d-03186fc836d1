# BlazePose Keypoint Display Issue - Latest Updates & Troubleshooting

## Executive Summary

**Current Status**: BlazePose skeletal overlay keypoints are displaying as a single green dot in the bottom right corner instead of a proper skeletal overlay on the runner. Despite successful pose detection and auxiliary landmarks processing, the final keypoint visualization is not working correctly.

**Key Finding**: The BlazePose detection pipeline is working correctly through the detector phase, but there's still an issue in the coordinate processing or rendering pipeline that causes all keypoints to appear at the same location.

## Detailed Problem Description

### Visual Issue
- **Expected**: Skeletal overlay with 39 keypoints positioned on the runner's body parts (head, shoulders, elbows, wrists, hips, knees, ankles, etc.)
- **Actual**: Single green dot appearing in the bottom right corner of the video canvas
- **Environment**: Side View Analysis, 3D Running Analysis mode, BlazePose Full model

### Technical Symptoms
- Pose detection is successful (1 pose detected with 39 keypoints)
- Auxiliary landmarks are correctly detected (4 landmarks with varying coordinates)
- Console logs show coordinate processing is occurring
- Final keypoint coordinates all collapse to the same value (540, 960)

## Complete Fix History

### Fix #1: Auxiliary Landmarks Issue (COMPLETED ✅)
**File**: `src/blazepose_tfjs/detector.ts`
**Lines**: 645-646
**Problem**: Auxiliary landmarks array was empty, causing downstream processing issues
**Solution**: Added explicit auxiliary landmarks creation from main landmarks
```javascript
// FIXED: Create auxiliary landmarks from main landmarks using specific keypoints
const auxiliaryLandmarks = [
  roiLandmarks[11], // left_shoulder
  roiLandmarks[12], // right_shoulder  
  roiLandmarks[23], // left_hip
  roiLandmarks[24]  // right_hip
];
```
**Result**: ✅ Successfully fixed - auxiliary landmarks now contain 4 proper landmarks with varying coordinates

### Fix #2: Double Projection Removal (COMPLETED ✅)
**File**: `src/hooks/useBlazePoseDetection.ts`
**Lines**: 289-295, 329-342
**Problem**: Phase 5 processing was re-calculating ROI and re-projecting already-projected landmarks
**Solution**: Removed unnecessary ROI re-calculation and re-projection steps
```javascript
// REMOVED: Double projection causing coordinate collapse
// const currentROI = calculateROIFromLandmarks(processedKeypoints, imageSize, 1.3);
// processedKeypoints = calculateLandmarkProjection(processedKeypoints, smoothedROI);
```
**Result**: ✅ Eliminated double projection, but keypoints still not displaying correctly

## Technical Analysis

### Why Fix #1 Was Needed
The auxiliary landmarks were empty, which could cause issues in the landmark processing pipeline. The BlazePose model expects auxiliary landmarks for proper pose estimation and coordinate validation.

### Why Fix #2 Was Needed
Console logs showed that landmarks were being projected correctly by the detector, but then Phase 5 was:
1. Calculating a new ROI from already-projected landmarks
2. Re-projecting them using this ROI
3. This caused all landmarks to collapse to the ROI center point (540, 960)

The detector already provides correctly projected coordinates, so re-projection was unnecessary and harmful.

## Current State

### What's Working ✅
- BlazePose model initialization and loading
- Video playback and canvas setup
- Pose detection (1 pose found with 39 keypoints)
- Auxiliary landmarks detection (4 landmarks with proper coordinates)
- Coordinate validation and NaN handling
- Console logging and debugging infrastructure

### What's Still Broken ❌
- Final keypoint visualization (all keypoints appear at same location)
- Skeletal overlay rendering on the runner
- Coordinate system transformation from detection to canvas rendering

## Console Log References

### Console_log19.md
- Shows auxiliary landmarks fix working correctly
- Demonstrates 4 auxiliary landmarks with varying coordinates over time
- Shows double projection issue in Phase 5 processing
- Final coordinates all collapse to (540, 960)

### Console_log20.md
- Should contain results after double projection fix
- Use this to verify if coordinate collapse issue is resolved
- Check if keypoints now have varying coordinates in final output

## Next Steps for Investigation

### Priority 1: Coordinate System Analysis
1. **Verify coordinate flow**: Check if coordinates are preserved after Fix #2
2. **Canvas coordinate system**: Ensure canvas coordinates match video dimensions
3. **Scaling issues**: Verify pixel vs normalized coordinate handling

### Priority 2: Rendering Pipeline
1. **Canvas drawing**: Check if keypoints are being drawn at correct positions
2. **Coordinate transformation**: Verify video-to-canvas coordinate mapping
3. **Visibility thresholds**: Ensure keypoints meet visibility requirements for rendering

### Priority 3: Specific Code Areas to Investigate
1. **Canvas rendering**: `src/components/SideViewBlazePoseOverlay.tsx` drawing logic
2. **Coordinate scaling**: `src/shared/calculators/normalized_keypoints_to_keypoints.ts`
3. **Canvas positioning**: Canvas overlay positioning and sizing
4. **Final coordinate output**: Verify coordinates in ResultsPanel.tsx

## Code Locations - Modified Files

### Primary Fixes
- **detector.ts**: Lines 645-646 (auxiliary landmarks fix)
- **useBlazePoseDetection.ts**: Lines 289-295, 329-342 (double projection removal)

### Key Files for Further Investigation
- **SideViewBlazePoseOverlay.tsx**: Canvas rendering and keypoint drawing
- **canvas_debug.ts**: Coordinate system detection and debugging
- **normalized_keypoints_to_keypoints.ts**: Coordinate scaling logic
- **blazepose_tensor_processor.ts**: Coordinate validation and processing

### Console Log Files
- **Console_log19.md**: Pre-fix debugging information
- **Console_log20.md**: Post-fix verification (check this for current status)

## Development Environment Notes
- **Port**: http://localhost:8080/
- **Video**: Side view runner on treadmill
- **Model**: BlazePose Full (3D Running Analysis)
- **Canvas Size**: 570x1014
- **Video Dimensions**: 1080x1920

## Critical Debugging Information

### Console Log Evidence (Console_log19.md)
```
🔧 AUXILIARY LANDMARKS DEBUG: Auxiliary landmarks: (4) [{…}, {…}, {…}, {…}]
🔍 KEYPOINT DEBUG 0: {original: {x: 540, y: 960}, canvas: {x: 540, y: 960}, score: '0.500'}
🔍 KEYPOINT DEBUG 1: {original: {x: 540, y: 960}, canvas: {x: 540, y: 960}, score: '0.500'}
🔧 ROI PROCESSING: Calculated ROI: {xCenter: 0.5, yCenter: 0.5, width: 0, height: 0, rotation: 0}
```

### Key Observations
1. **Auxiliary landmarks working**: 4 landmarks detected with proper varying coordinates
2. **Final coordinates identical**: All keypoints collapse to (540, 960)
3. **ROI calculation failing**: Width=0, Height=0 indicates coordinate processing issue
4. **Canvas coordinate system**: Detected as "canvas" coordinates (not normalized)

### Coordinate Flow Analysis
```
Detector Output → Phase 5 Processing → Canvas Rendering
     ✅                    ❌                 ❌
(Working correctly)  (Fixed but may     (Not receiving
                     have other issues)  correct coords)
```

## Debugging Commands
```bash
# Start development server
npm run dev

# Check for TypeScript errors
npm run type-check

# View latest console logs
ls -la src/ConsoleLogs/ | tail -5

# Kill all development processes
pkill -f "vite" && pkill -f "npm"

# Check specific console log
cat src/ConsoleLogs/Console_log19.md | grep "KEYPOINT DEBUG"
```

## Immediate Action Items for Next AI Assistant

### 1. Verify Fix #2 Results
- Check Console_log20.md or latest log after double projection fix
- Look for coordinate variation in final keypoint output
- Confirm ROI calculation no longer returns width=0, height=0

### 2. Canvas Rendering Investigation
- Examine `SideViewBlazePoseOverlay.tsx` drawing functions
- Verify canvas overlay positioning matches video
- Check if keypoints are being filtered out by visibility thresholds

### 3. Coordinate System Validation
- Verify imageSize values (should be 1080x1920 for video, 570x1014 for canvas)
- Check coordinate scaling between video and canvas dimensions
- Ensure no additional coordinate transformations are corrupting values

---
**Last Updated**: Current session after auxiliary landmarks fix and double projection removal
**Status**: Keypoint display issue persists - need coordinate system and rendering analysis
**Next AI Assistant**: Start with verifying Fix #2 results, then focus on canvas rendering pipeline
