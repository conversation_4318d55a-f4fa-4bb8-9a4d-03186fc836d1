/**
 * TASK 3 VERIFICATION: Test suite to verify performance optimization and logging reduction
 * This test validates that the 2,000+ detection messages have been reduced while maintaining critical error reporting
 */

import { 
  logger, 
  LogLevel, 
  enableProductionLogging, 
  enableDevelopmentLogging,
  logTensorOperation,
  logDetectionResult,
  logCoordinateValidation
} from '../utils/logging_system';
import { 
  memoryManager, 
  getMemoryUsage, 
  performMemoryCleanup,
  cacheResult,
  getCachedResult
} from '../utils/memory_manager';
import { 
  detectionOptimizer, 
  optimizeDetections, 
  enableAggressiveOptimization,
  enableStandardOptimization
} from '../utils/detection_optimizer';
import { PERFORMANCE_LIMITS } from '../blazepose_tfjs/constants';

// Mock detection data for testing
const createMockDetections = (count: number) => {
  const detections = [];
  for (let i = 0; i < count; i++) {
    detections.push({
      boundingBox: {
        xMin: Math.random() * 0.5,
        yMin: Math.random() * 0.5,
        xMax: Math.random() * 0.5 + 0.5,
        yMax: Math.random() * 0.5 + 0.5
      },
      score: Math.random(),
      locationData: { relativeKeypoints: [] }
    });
  }
  return detections;
};

// Test 1: Verify logging system reduces output volume
export function testLoggingReduction(): boolean {
  console.log('🧪 TEST 1: Logging Volume Reduction');
  
  // Capture console output
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;
  
  let logCount = 0;
  const mockLog = (...args: any[]) => { logCount++; };
  
  console.log = mockLog;
  console.warn = mockLog;
  console.error = mockLog;
  
  try {
    // Enable production logging (should reduce output)
    enableProductionLogging();
    
    // Generate many log messages
    for (let i = 0; i < 100; i++) {
      logTensorOperation('Test tensor operation', { index: i });
      logDetectionResult('Test detection result', { index: i });
      logCoordinateValidation('Test coordinate validation', { index: i });
    }
    
    // Force flush any batched messages
    logger.forceFlush();
    
    // In production mode, most of these should be filtered out
    const productionLogCount = logCount;
    
    // Reset and test development mode
    logCount = 0;
    enableDevelopmentLogging();
    
    // Generate same messages in development mode
    for (let i = 0; i < 100; i++) {
      logTensorOperation('Test tensor operation', { index: i });
      logDetectionResult('Test detection result', { index: i });
      logCoordinateValidation('Test coordinate validation', { index: i });
    }
    
    logger.forceFlush();
    const developmentLogCount = logCount;
    
    // Production should have significantly fewer logs
    const reductionRatio = productionLogCount / developmentLogCount;
    const testPassed = reductionRatio < 0.5; // At least 50% reduction
    
    console.log('🧪 TEST 1 RESULTS:', {
      productionLogs: productionLogCount,
      developmentLogs: developmentLogCount,
      reductionRatio: `${(reductionRatio * 100).toFixed(1)}%`,
      targetReduction: '< 50%',
      passed: testPassed
    });
    
    console.log(`🧪 TEST 1: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
    return testPassed;
    
  } finally {
    // Restore original console methods
    console.log = originalLog;
    console.warn = originalWarn;
    console.error = originalError;
  }
}

// Test 2: Verify memory management optimization
export function testMemoryOptimization(): boolean {
  console.log('🧪 TEST 2: Memory Management Optimization');
  
  const initialMemory = getMemoryUsage();
  
  // Simulate memory-intensive operations
  const cacheKeys: string[] = [];
  for (let i = 0; i < 50; i++) {
    const key = `test_cache_${i}`;
    const data = new Array(1000).fill(Math.random());
    cacheResult(key, data);
    cacheKeys.push(key);
  }
  
  const afterCachingMemory = getMemoryUsage();
  
  // Perform memory cleanup
  performMemoryCleanup();
  
  const afterCleanupMemory = getMemoryUsage();
  
  // Verify cache functionality
  const cacheHits = cacheKeys.filter(key => getCachedResult(key) !== null).length;
  
  const memoryReduced = afterCleanupMemory < afterCachingMemory;
  const cacheWorking = cacheHits > 0;
  
  const testPassed = memoryReduced && cacheWorking;
  
  console.log('🧪 TEST 2 RESULTS:', {
    initialMemory: `${initialMemory.toFixed(2)}MB`,
    afterCaching: `${afterCachingMemory.toFixed(2)}MB`,
    afterCleanup: `${afterCleanupMemory.toFixed(2)}MB`,
    memoryReduced: memoryReduced,
    cacheHits: cacheHits,
    cacheWorking: cacheWorking
  });
  
  console.log(`🧪 TEST 2: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 3: Verify detection optimization reduces processing load
export function testDetectionOptimization(): boolean {
  console.log('🧪 TEST 3: Detection Optimization');
  
  // Create large number of detections (simulating the 2,254 from console log)
  const mockDetections = createMockDetections(2254);
  
  const startTime = performance.now();
  
  // Test standard optimization
  enableStandardOptimization();
  const standardResult = optimizeDetections(mockDetections, 1);
  const standardTime = performance.now() - startTime;
  
  // Test aggressive optimization
  const aggressiveStartTime = performance.now();
  enableAggressiveOptimization();
  const aggressiveResult = optimizeDetections(mockDetections, 2);
  const aggressiveTime = performance.now() - aggressiveStartTime;
  
  const standardReduction = (mockDetections.length - standardResult.filteredCount) / mockDetections.length;
  const aggressiveReduction = (mockDetections.length - aggressiveResult.filteredCount) / mockDetections.length;
  
  const testPassed = 
    standardReduction > 0.1 && // At least 10% reduction in standard mode
    aggressiveReduction > 0.3 && // At least 30% reduction in aggressive mode
    standardTime < 100 && // Processing should be fast
    aggressiveTime < 100;
  
  console.log('🧪 TEST 3 RESULTS:', {
    originalDetections: mockDetections.length,
    standardFiltered: standardResult.filteredCount,
    aggressiveFiltered: aggressiveResult.filteredCount,
    standardReduction: `${(standardReduction * 100).toFixed(1)}%`,
    aggressiveReduction: `${(aggressiveReduction * 100).toFixed(1)}%`,
    standardTime: `${standardTime.toFixed(2)}ms`,
    aggressiveTime: `${aggressiveTime.toFixed(2)}ms`,
    standardOptimizations: standardResult.optimizations.join(', '),
    aggressiveOptimizations: aggressiveResult.optimizations.join(', ')
  });
  
  console.log(`🧪 TEST 3: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 4: Verify performance constants are properly configured
export function testPerformanceConstants(): boolean {
  console.log('🧪 TEST 4: Performance Constants Configuration');
  
  const requiredConstants = [
    'ENABLE_VERBOSE_LOGGING',
    'ENABLE_COORDINATE_LOGGING', 
    'ENABLE_DETECTION_LOGGING',
    'LOG_SAMPLE_RATE',
    'ENABLE_DETECTION_OPTIMIZATION',
    'ENABLE_SPATIAL_FILTERING',
    'ENABLE_CONFIDENCE_FILTERING'
  ];
  
  let passed = 0;
  let failed = 0;
  
  requiredConstants.forEach(constant => {
    if (constant in PERFORMANCE_LIMITS) {
      passed++;
      console.log(`  ✅ ${constant}: ${PERFORMANCE_LIMITS[constant]}`);
    } else {
      failed++;
      console.log(`  ❌ ${constant}: missing`);
    }
  });
  
  // Verify logging is properly reduced
  const loggingReduced = 
    !PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING ||
    !PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING ||
    !PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING;
  
  // Verify optimization is enabled
  const optimizationEnabled = 
    PERFORMANCE_LIMITS.ENABLE_DETECTION_OPTIMIZATION &&
    PERFORMANCE_LIMITS.ENABLE_SPATIAL_FILTERING &&
    PERFORMANCE_LIMITS.ENABLE_CONFIDENCE_FILTERING;
  
  const testPassed = failed === 0 && loggingReduced && optimizationEnabled;
  
  console.log('🧪 TEST 4 RESULTS:', {
    constantsFound: `${passed}/${requiredConstants.length}`,
    loggingReduced: loggingReduced,
    optimizationEnabled: optimizationEnabled
  });
  
  console.log(`🧪 TEST 4: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 5: Verify batch logging and sampling work correctly
export function testBatchLoggingAndSampling(): boolean {
  console.log('🧪 TEST 5: Batch Logging and Sampling');
  
  // Reset message counts
  logger.resetMessageCounts();
  
  // Generate many messages that should be batched/sampled
  for (let i = 0; i < 1000; i++) {
    logger.verbose('tensorProcessing', `Verbose message ${i}`);
    logger.debug('detection', `Debug message ${i}`);
  }
  
  // Check queue size (should be batching)
  const queueSize = logger.getQueueSize();
  
  // Force flush and check message counts
  logger.forceFlush();
  const messageCounts = logger.getMessageCounts();
  
  // With sampling, we should have significantly fewer messages processed
  const totalMessages = Object.values(messageCounts).reduce((sum, count) => sum + count, 0);
  const samplingWorking = totalMessages < 500; // Should be much less than 2000 due to sampling
  const batchingWorking = queueSize > 0; // Should have been batching
  
  const testPassed = samplingWorking && batchingWorking;
  
  console.log('🧪 TEST 5 RESULTS:', {
    messagesGenerated: 2000,
    messagesProcessed: totalMessages,
    queueSizeBeforeFlush: queueSize,
    samplingReduction: `${((2000 - totalMessages) / 2000 * 100).toFixed(1)}%`,
    messageCounts: messageCounts
  });
  
  console.log(`🧪 TEST 5: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Main test runner
export async function runAllPerformanceOptimizationTests(): Promise<boolean> {
  console.log('🧪 STARTING TASK 3 VERIFICATION TESTS');
  console.log('🧪 Testing performance optimization and logging reduction');
  console.log('=' .repeat(60));
  
  const results = [
    testLoggingReduction(),
    testMemoryOptimization(),
    testDetectionOptimization(),
    testPerformanceConstants(),
    testBatchLoggingAndSampling()
  ];
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('=' .repeat(60));
  console.log(`🧪 TASK 3 VERIFICATION COMPLETE: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED - Performance optimization is working correctly!');
    console.log('✅ The 2,000+ detection messages should be significantly reduced');
    console.log('✅ Memory management and detection optimization are functioning');
    console.log('✅ Critical error reporting is maintained while verbose logging is reduced');
  } else {
    console.log('⚠️  SOME TESTS FAILED - Performance optimization needs attention');
  }
  
  return passedTests === totalTests;
}

// Export for use in other test files
export { createMockDetections };
