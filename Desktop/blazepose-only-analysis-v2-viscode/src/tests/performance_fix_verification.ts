/**
 * TASK 1 VERIFICATION: Test suite to verify the 14.3-second requestAnimationFrame violation fix
 * This test simulates the conditions that caused the original freeze and verifies the fix
 */

import { PERFORMANCE_LIMITS } from '../blazepose_tfjs/constants';
import { 
  PerformanceMonitor, 
  isValidCoordinate, 
  isValidDetection,
  processDetectionsWithLimits,
  globalPerformanceMonitor 
} from '../blazepose_tfjs/performance_monitor';

// Mock detection data that simulates the problematic scenario from console log
const createMockDetections = (count: number, includeInvalid: boolean = false) => {
  const detections = [];
  
  for (let i = 0; i < count; i++) {
    if (includeInvalid && i % 10 === 0) {
      // Create invalid detections similar to those in the console log
      detections.push({
        yCenter: -373448.96875, // Extreme value from console log
        xCenter: -567170.125,   // Extreme value from console log
        h: NaN,
        w: Infinity
      });
    } else {
      // Create valid detections
      detections.push({
        yCenter: Math.random() * 100 - 50,
        xCenter: Math.random() * 100 - 50,
        h: Math.random() * 50 + 10,
        w: Math.random() * 50 + 10
      });
    }
  }
  
  return detections;
};

// Test 1: Verify detection limiting prevents freeze
export async function testDetectionLimiting(): Promise<boolean> {
  console.log('🧪 TEST 1: Detection Limiting');
  
  const startTime = performance.now();
  
  // Create 2254 detections (same as in console log that caused freeze)
  const mockDetections = createMockDetections(2254, true);
  
  const monitor = new PerformanceMonitor();
  
  const result = await processDetectionsWithLimits(
    mockDetections,
    (detection, index) => ({ ...detection, processed: true }),
    monitor
  );
  
  const processingTime = performance.now() - startTime;
  
  console.log('🧪 TEST 1 RESULTS:', {
    originalDetections: mockDetections.length,
    processedDetections: result.detectionsProcessed,
    skippedDetections: result.detectionsSkipped,
    processingTime: `${processingTime.toFixed(2)}ms`,
    wasLimited: result.wasLimited,
    withinTimeLimit: processingTime < PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS * 10 // Allow 10x buffer for test
  });
  
  // Test passes if:
  // 1. Processing time is reasonable (not 14+ seconds)
  // 2. Detection count was limited
  // 3. Some detections were processed successfully
  const testPassed = 
    processingTime < 1000 && // Should be under 1 second
    result.wasLimited && 
    result.detectionsProcessed > 0 &&
    result.detectionsProcessed <= PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME;
  
  console.log(`🧪 TEST 1: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 2: Verify coordinate validation
export function testCoordinateValidation(): boolean {
  console.log('🧪 TEST 2: Coordinate Validation');
  
  const testCases = [
    { value: 0, expected: true, name: 'zero' },
    { value: 100, expected: true, name: 'positive normal' },
    { value: -100, expected: true, name: 'negative normal' },
    { value: 1001, expected: false, name: 'too large' },
    { value: -1001, expected: false, name: 'too small' },
    { value: NaN, expected: false, name: 'NaN' },
    { value: Infinity, expected: false, name: 'Infinity' },
    { value: -373448.96875, expected: false, name: 'extreme negative (from log)' },
    { value: -567170.125, expected: false, name: 'extreme negative 2 (from log)' }
  ];
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach(testCase => {
    const result = isValidCoordinate(testCase.value);
    if (result === testCase.expected) {
      passed++;
      console.log(`  ✅ ${testCase.name}: ${testCase.value} -> ${result}`);
    } else {
      failed++;
      console.log(`  ❌ ${testCase.name}: ${testCase.value} -> ${result} (expected ${testCase.expected})`);
    }
  });
  
  const testPassed = failed === 0;
  console.log(`🧪 TEST 2: ${testPassed ? '✅ PASSED' : '❌ FAILED'} (${passed}/${testCases.length})`);
  return testPassed;
}

// Test 3: Verify detection validation
export function testDetectionValidation(): boolean {
  console.log('🧪 TEST 3: Detection Validation');
  
  const testDetections = [
    { detection: { yCenter: 0, xCenter: 0, h: 10, w: 10 }, expected: true, name: 'valid detection' },
    { detection: { yCenter: NaN, xCenter: 0, h: 10, w: 10 }, expected: false, name: 'NaN yCenter' },
    { detection: { yCenter: 0, xCenter: -373448.96875, h: 10, w: 10 }, expected: false, name: 'extreme xCenter' },
    { detection: { yCenter: 0, xCenter: 0, h: -10, w: 10 }, expected: false, name: 'negative height' },
    { detection: null, expected: false, name: 'null detection' },
    { detection: { yCenter: 0 }, expected: false, name: 'incomplete detection' }
  ];
  
  let passed = 0;
  let failed = 0;
  
  testDetections.forEach(testCase => {
    const result = isValidDetection(testCase.detection);
    if (result === testCase.expected) {
      passed++;
      console.log(`  ✅ ${testCase.name}: ${result}`);
    } else {
      failed++;
      console.log(`  ❌ ${testCase.name}: ${result} (expected ${testCase.expected})`);
    }
  });
  
  const testPassed = failed === 0;
  console.log(`🧪 TEST 3: ${testPassed ? '✅ PASSED' : '❌ FAILED'} (${passed}/${testDetections.length})`);
  return testPassed;
}

// Test 4: Verify performance monitoring
export function testPerformanceMonitoring(): boolean {
  console.log('🧪 TEST 4: Performance Monitoring');
  
  const monitor = new PerformanceMonitor();
  
  // Simulate frame processing
  monitor.startFrame();
  
  // Simulate some processing time
  const start = performance.now();
  while (performance.now() - start < 5) {
    // Busy wait for 5ms
  }
  
  monitor.recordDetectionProcessed();
  monitor.recordDetectionProcessed();
  monitor.recordInvalidDetection();
  
  const frameTime = monitor.endFrame();
  const metrics = monitor.getMetrics();
  
  const testPassed = 
    frameTime >= 5 && // Should have taken at least 5ms
    metrics.frameCount === 1 &&
    metrics.detectionsProcessed === 2 &&
    metrics.invalidDetections === 1;
  
  console.log('🧪 TEST 4 RESULTS:', {
    frameTime: `${frameTime.toFixed(2)}ms`,
    frameCount: metrics.frameCount,
    detectionsProcessed: metrics.detectionsProcessed,
    invalidDetections: metrics.invalidDetections
  });
  
  console.log(`🧪 TEST 4: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 5: Verify constants are properly configured
export function testPerformanceConstants(): boolean {
  console.log('🧪 TEST 5: Performance Constants');
  
  const requiredConstants = [
    'MAX_DETECTIONS_PER_FRAME',
    'MAX_PROCESSING_TIME_MS',
    'MAX_COORDINATE_VALUE',
    'MIN_COORDINATE_VALUE',
    'MIN_DETECTION_CONFIDENCE',
    'MAX_CONSECUTIVE_INVALID_DETECTIONS'
  ];
  
  let passed = 0;
  let failed = 0;
  
  requiredConstants.forEach(constant => {
    if (constant in PERFORMANCE_LIMITS && typeof PERFORMANCE_LIMITS[constant] === 'number') {
      passed++;
      console.log(`  ✅ ${constant}: ${PERFORMANCE_LIMITS[constant]}`);
    } else {
      failed++;
      console.log(`  ❌ ${constant}: missing or invalid`);
    }
  });
  
  // Verify reasonable values
  const reasonableValues = 
    PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME > 0 &&
    PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME <= 100 &&
    PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS > 0 &&
    PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS <= 100;
  
  if (!reasonableValues) {
    failed++;
    console.log('  ❌ Some constants have unreasonable values');
  } else {
    passed++;
    console.log('  ✅ All constants have reasonable values');
  }
  
  const testPassed = failed === 0;
  console.log(`🧪 TEST 5: ${testPassed ? '✅ PASSED' : '❌ FAILED'} (${passed}/${requiredConstants.length + 1})`);
  return testPassed;
}

// Main test runner
export async function runAllPerformanceTests(): Promise<boolean> {
  console.log('🧪 STARTING TASK 1 VERIFICATION TESTS');
  console.log('🧪 Testing fix for 14.3-second requestAnimationFrame violation');
  console.log('=' .repeat(60));
  
  const results = [
    await testDetectionLimiting(),
    testCoordinateValidation(),
    testDetectionValidation(),
    testPerformanceMonitoring(),
    testPerformanceConstants()
  ];
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('=' .repeat(60));
  console.log(`🧪 TASK 1 VERIFICATION COMPLETE: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED - Performance fix is working correctly!');
    console.log('✅ The 14.3-second requestAnimationFrame violation should be resolved');
  } else {
    console.log('⚠️  SOME TESTS FAILED - Performance fix needs attention');
  }
  
  return passedTests === totalTests;
}

// Export for use in other test files
export { createMockDetections };
