/**
 * TASK 2 VERIFICATION: Test suite to verify skeletal overlay rendering fix
 * This test validates that pose keypoints and skeleton connections are properly drawn on canvas
 */

import { globalPoseDrawer, DEFAULT_DRAWING_CONFIG, PoseDrawingResult } from '../utils/pose_drawing';
import { globalCanvasDebugger, CanvasDebugger } from '../utils/canvas_debug';

// Mock canvas and context for testing
class MockCanvasRenderingContext2D {
  public fillStyle: string = '#000000';
  public strokeStyle: string = '#000000';
  public lineWidth: number = 1;
  public font: string = '10px sans-serif';
  public globalCompositeOperation: string = 'source-over';
  public lineCap: string = 'butt';
  public lineJoin: string = 'miter';

  private operations: string[] = [];

  beginPath(): void { this.operations.push('beginPath'); }
  arc(x: number, y: number, radius: number, startAngle: number, endAngle: number): void {
    this.operations.push(`arc(${x.toFixed(1)}, ${y.toFixed(1)}, ${radius})`);
  }
  fill(): void { this.operations.push('fill'); }
  stroke(): void { this.operations.push('stroke'); }
  fillRect(x: number, y: number, width: number, height: number): void {
    this.operations.push(`fillRect(${x}, ${y}, ${width}, ${height})`);
  }
  fillText(text: string, x: number, y: number): void {
    this.operations.push(`fillText("${text}", ${x.toFixed(1)}, ${y.toFixed(1)})`);
  }
  strokeText(text: string, x: number, y: number): void {
    this.operations.push(`strokeText("${text}", ${x.toFixed(1)}, ${y.toFixed(1)})`);
  }
  moveTo(x: number, y: number): void {
    this.operations.push(`moveTo(${x.toFixed(1)}, ${y.toFixed(1)})`);
  }
  lineTo(x: number, y: number): void {
    this.operations.push(`lineTo(${x.toFixed(1)}, ${y.toFixed(1)})`);
  }
  save(): void { this.operations.push('save'); }
  restore(): void { this.operations.push('restore'); }

  getOperations(): string[] { return [...this.operations]; }
  clearOperations(): void { this.operations = []; }
}

class MockCanvas {
  public width: number = 640;
  public height: number = 480;
  
  getContext(type: string): MockCanvasRenderingContext2D | null {
    if (type === '2d') return new MockCanvasRenderingContext2D();
    return null;
  }
}

class MockVideo {
  public videoWidth: number = 1920;
  public videoHeight: number = 1080;
}

// Create mock pose data with different coordinate systems
const createMockPoseNormalized = () => ({
  keypoints: [
    { name: 'nose', x: 0.5, y: 0.3, score: 0.9 },
    { name: 'left_eye', x: 0.45, y: 0.25, score: 0.8 },
    { name: 'right_eye', x: 0.55, y: 0.25, score: 0.8 },
    { name: 'left_shoulder', x: 0.4, y: 0.5, score: 0.7 },
    { name: 'right_shoulder', x: 0.6, y: 0.5, score: 0.7 },
    { name: 'left_elbow', x: 0.35, y: 0.65, score: 0.6 },
    { name: 'right_elbow', x: 0.65, y: 0.65, score: 0.6 },
    { name: 'left_wrist', x: 0.3, y: 0.8, score: 0.5 },
    { name: 'right_wrist', x: 0.7, y: 0.8, score: 0.5 }
  ]
});

const createMockPoseCanvas = () => ({
  keypoints: [
    { name: 'nose', x: 320, y: 144, score: 0.9 },
    { name: 'left_eye', x: 288, y: 120, score: 0.8 },
    { name: 'right_eye', x: 352, y: 120, score: 0.8 },
    { name: 'left_shoulder', x: 256, y: 240, score: 0.7 },
    { name: 'right_shoulder', x: 384, y: 240, score: 0.7 }
  ]
});

const createMockPoseLowScore = () => ({
  keypoints: [
    { name: 'nose', x: 0.5, y: 0.3, score: 0.1 }, // Below threshold
    { name: 'left_eye', x: 0.45, y: 0.25, score: 0.05 }, // Below threshold
    { name: 'right_eye', x: 0.55, y: 0.25, score: 0.8 }, // Above threshold
  ]
});

// Test 1: Verify coordinate system detection
export function testCoordinateSystemDetection(): boolean {
  console.log('🧪 TEST 1: Coordinate System Detection');
  
  const debugger = new CanvasDebugger();
  const canvas = new MockCanvas();
  const video = new MockVideo();
  
  debugger.updateCanvasInfo(canvas as any, video as any);
  
  // Test normalized coordinates
  const normalizedPose = createMockPoseNormalized();
  const normalizedSystem = debugger.detectCoordinateSystem(normalizedPose.keypoints);
  
  // Test canvas coordinates
  const canvasPose = createMockPoseCanvas();
  const canvasSystem = debugger.detectCoordinateSystem(canvasPose.keypoints);
  
  const testPassed = normalizedSystem === 'normalized' && canvasSystem === 'canvas';
  
  console.log('🧪 TEST 1 RESULTS:', {
    normalizedDetection: normalizedSystem,
    canvasDetection: canvasSystem,
    passed: testPassed
  });
  
  console.log(`🧪 TEST 1: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 2: Verify keypoint drawing with normalized coordinates
export function testKeypointDrawingNormalized(): boolean {
  console.log('🧪 TEST 2: Keypoint Drawing (Normalized Coordinates)');
  
  const canvas = new MockCanvas();
  const video = new MockVideo();
  const ctx = canvas.getContext('2d') as MockCanvasRenderingContext2D;
  const pose = createMockPoseNormalized();
  
  const result = globalPoseDrawer.drawPose(ctx as any, pose, canvas as any, video as any);
  
  const operations = ctx.getOperations();
  const arcOperations = operations.filter(op => op.startsWith('arc'));
  const fillOperations = operations.filter(op => op === 'fill');
  
  // Should have drawn circles for keypoints with score > threshold
  const expectedKeypoints = pose.keypoints.filter(kp => kp.score > DEFAULT_DRAWING_CONFIG.scoreThreshold).length;
  
  const testPassed = 
    result.coordinateSystem === 'normalized' &&
    result.keypointsDrawn === expectedKeypoints &&
    arcOperations.length >= expectedKeypoints &&
    fillOperations.length >= expectedKeypoints;
  
  console.log('🧪 TEST 2 RESULTS:', {
    coordinateSystem: result.coordinateSystem,
    keypointsDrawn: result.keypointsDrawn,
    expectedKeypoints: expectedKeypoints,
    arcOperations: arcOperations.length,
    fillOperations: fillOperations.length,
    sampleOperations: operations.slice(0, 10)
  });
  
  console.log(`🧪 TEST 2: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 3: Verify connection drawing
export function testConnectionDrawing(): boolean {
  console.log('🧪 TEST 3: Connection Drawing');
  
  const canvas = new MockCanvas();
  const video = new MockVideo();
  const ctx = canvas.getContext('2d') as MockCanvasRenderingContext2D;
  const pose = createMockPoseNormalized();
  
  const result = globalPoseDrawer.drawPose(ctx as any, pose, canvas as any, video as any);
  
  const operations = ctx.getOperations();
  const moveToOperations = operations.filter(op => op.startsWith('moveTo'));
  const lineToOperations = operations.filter(op => op.startsWith('lineTo'));
  const strokeOperations = operations.filter(op => op === 'stroke');
  
  const testPassed = 
    result.connectionsDrawn > 0 &&
    moveToOperations.length > 0 &&
    lineToOperations.length > 0 &&
    strokeOperations.length > 0;
  
  console.log('🧪 TEST 3 RESULTS:', {
    connectionsDrawn: result.connectionsDrawn,
    moveToOperations: moveToOperations.length,
    lineToOperations: lineToOperations.length,
    strokeOperations: strokeOperations.length
  });
  
  console.log(`🧪 TEST 3: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 4: Verify score threshold filtering
export function testScoreThresholdFiltering(): boolean {
  console.log('🧪 TEST 4: Score Threshold Filtering');
  
  const canvas = new MockCanvas();
  const video = new MockVideo();
  const ctx = canvas.getContext('2d') as MockCanvasRenderingContext2D;
  const pose = createMockPoseLowScore();
  
  const result = globalPoseDrawer.drawPose(ctx as any, pose, canvas as any, video as any);
  
  // Only keypoints with score > threshold should be drawn
  const expectedKeypoints = pose.keypoints.filter(kp => kp.score > DEFAULT_DRAWING_CONFIG.scoreThreshold).length;
  
  const testPassed = result.keypointsDrawn === expectedKeypoints;
  
  console.log('🧪 TEST 4 RESULTS:', {
    totalKeypoints: pose.keypoints.length,
    expectedKeypoints: expectedKeypoints,
    actualKeypointsDrawn: result.keypointsDrawn,
    threshold: DEFAULT_DRAWING_CONFIG.scoreThreshold
  });
  
  console.log(`🧪 TEST 4: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 5: Verify coordinate conversion accuracy
export function testCoordinateConversion(): boolean {
  console.log('🧪 TEST 5: Coordinate Conversion Accuracy');
  
  const canvas = new MockCanvas();
  const video = new MockVideo();
  const ctx = canvas.getContext('2d') as MockCanvasRenderingContext2D;
  
  // Test with known normalized coordinates
  const pose = {
    keypoints: [
      { name: 'center', x: 0.5, y: 0.5, score: 0.9 } // Should convert to (320, 240)
    ]
  };
  
  globalPoseDrawer.drawPose(ctx as any, pose, canvas as any, video as any);
  
  const operations = ctx.getOperations();
  const arcOperation = operations.find(op => op.startsWith('arc'));
  
  // Expected: arc(320.0, 240.0, 6) - center of 640x480 canvas
  const expectedArc = 'arc(320.0, 240.0, 6)';
  const testPassed = arcOperation === expectedArc;
  
  console.log('🧪 TEST 5 RESULTS:', {
    inputCoordinates: { x: 0.5, y: 0.5 },
    canvasSize: { width: canvas.width, height: canvas.height },
    expectedArc: expectedArc,
    actualArc: arcOperation,
    conversionCorrect: testPassed
  });
  
  console.log(`🧪 TEST 5: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return testPassed;
}

// Test 6: Verify error handling
export function testErrorHandling(): boolean {
  console.log('🧪 TEST 6: Error Handling');
  
  const canvas = new MockCanvas();
  const video = new MockVideo();
  const ctx = canvas.getContext('2d') as MockCanvasRenderingContext2D;
  
  // Test with invalid pose data
  const invalidPoses = [
    null,
    {},
    { keypoints: null },
    { keypoints: [] },
    { keypoints: [{ x: NaN, y: NaN, score: 0.9 }] }
  ];
  
  let allTestsPassed = true;
  
  invalidPoses.forEach((pose, index) => {
    try {
      const result = globalPoseDrawer.drawPose(ctx as any, pose, canvas as any, video as any);
      
      // Should handle gracefully without throwing
      const testPassed = result.keypointsDrawn === 0 && result.connectionsDrawn === 0;
      
      if (!testPassed) {
        allTestsPassed = false;
        console.log(`  ❌ Invalid pose ${index} not handled correctly:`, result);
      } else {
        console.log(`  ✅ Invalid pose ${index} handled correctly`);
      }
      
    } catch (error) {
      allTestsPassed = false;
      console.log(`  ❌ Invalid pose ${index} threw error:`, error.message);
    }
  });
  
  console.log(`🧪 TEST 6: ${allTestsPassed ? '✅ PASSED' : '❌ FAILED'}`);
  return allTestsPassed;
}

// Main test runner
export async function runAllSkeletalOverlayTests(): Promise<boolean> {
  console.log('🧪 STARTING TASK 2 VERIFICATION TESTS');
  console.log('🧪 Testing skeletal overlay rendering fix');
  console.log('=' .repeat(60));
  
  const results = [
    testCoordinateSystemDetection(),
    testKeypointDrawingNormalized(),
    testConnectionDrawing(),
    testScoreThresholdFiltering(),
    testCoordinateConversion(),
    testErrorHandling()
  ];
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('=' .repeat(60));
  console.log(`🧪 TASK 2 VERIFICATION COMPLETE: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED - Skeletal overlay rendering is working correctly!');
    console.log('✅ Pose keypoints and skeleton connections should now be visible on canvas');
  } else {
    console.log('⚠️  SOME TESTS FAILED - Skeletal overlay rendering needs attention');
  }
  
  return passedTests === totalTests;
}

// Export for use in other test files
export { MockCanvas, MockVideo, createMockPoseNormalized };
