
import { useState, useEffect } from 'react';
import * as tf from '@tensorflow/tfjs';
import { load as loadBlazePose } from '@/blazepose_tfjs/detector';
import { PoseDetector } from '@/pose_detector';
import { normalizedKeypointsToKeypoints } from '@/shared/calculators/normalized_keypoints_to_keypoints';
import { calculateLandmarkProjection } from '@/shared/calculators/calculate_landmark_projection';
import { calculateWorldLandmarkProjection } from '@/shared/calculators/calculate_world_landmark_projection';
import { getImageSize } from '@/shared/calculators/image_utils';
import { convertImageToTensor } from '@/shared/calculators/convert_image_to_tensor';
import { calculateROIFromLandmarks, expandROI, ROISmoothingFilter, validateROI } from '@/shared/calculators/roi_processing';
import { safeTensorDispose, validateTensor, filterNaNValues } from '@/shared/calculators/tensor_utils';
import { validateAndCleanPoseCoordinates } from '@/shared/calculators/blazepose_tensor_processor';
import { KeypointsSmoothingFilter } from '@/shared/filters/keypoints_smoothing';
import { LowPassVisibilityFilter } from '@/shared/filters/visibility_smoothing';
import { PoseStabilityFilter } from '@/shared/filters/pose_stability_filter';
import { BLAZEPOSE_KEYPOINTS } from '@/shared/calculators/blazepose_constants';
import { BLAZEPOSE_MODEL_VERSIONS, checkModelVersions, PERFORMANCE_LIMITS } from '@/blazepose_tfjs/constants'; // TASK 6: Import versioned URLs and version checker
import { logger, logDetectionResult, enableProductionLogging } from '@/utils/logging_system';
import { memoryManager } from '@/utils/memory_manager';

export const useBlazePoseDetection = (modelQuality: 'Full' | 'Heavy' = 'Full') => {
  const [detector, setDetector] = useState<PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');

  // TASK 3: Initialize optimized logging
  useEffect(() => {
    if (!PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING) {
      enableProductionLogging();
    }
  }, []);
  
  // PHASE 4: Enhanced Smoothing and Filtering Pipeline
  const [keypointsFilter] = useState(() => new KeypointsSmoothingFilter({
    frequency: 30,
    minCutOff: 1,
    beta: 0.007,
    derivateCutOff: 1,
    thresholdCutOff: 0.001,
    thresholdBeta: 0.1,
    disableValueScaling: true,
    velocitySmoothing: 0.15,
    accelerationDamping: 0.1
  }));
  
  const [visibilityFilter] = useState(() => new LowPassVisibilityFilter(0.1, 0.3, true));
  const [roiFilter] = useState(() => new ROISmoothingFilter(0.2));
  const [stabilityFilter] = useState(() => new PoseStabilityFilter({
    stabilityThreshold: 0.75,
    minStableFrames: 3,
    maxInstabilityFrames: 8,
    positionTolerance: 40.0
  }));

  useEffect(() => {
    const initBlazePose = async () => {
      // TASK 3: Reduced initialization logging
      if (PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING) {
        logDetectionResult('Initializing BlazePose', { modelQuality });
      }

      // TESTING: Log BlazePose initialization for end-to-end validation
      console.log('🧪 E2E TEST: BlazePose detection hook initializing with quality:', modelQuality);
      
      // TASK 6: Check model versions at startup
      checkModelVersions();
      
      try {
        await tf.ready();
        
        try {
          await tf.setBackend('webgl');
          await tf.ready();
          console.log('✅ WebGL backend ready for Phase 4 BlazePose');
        } catch (webglError) {
          console.warn('⚠️ WebGL failed, falling back to CPU:', webglError);
          await tf.setBackend('cpu');
          await tf.ready();
          console.log('✅ CPU backend ready for Phase 4 BlazePose');
        }

        console.log('📊 TensorFlow.js Backend Info:', {
          backend: tf.getBackend(),
          memory: tf.memory(),
          platform: tf.env().platform
        });
        
        // PHASE 5: Use our custom BlazePose detector implementation
        // TASK 1: Full model optimization with enhanced settings
        // TASK 6: Use versioned model URLs with validation
        const blazePoseDetector = await loadBlazePose({
          runtime: 'tfjs' as const,
          modelType: modelQuality.toLowerCase() as 'full' | 'heavy',
          enableSmoothing: modelQuality === 'Full', // Enable smoothing for Full model accuracy
          enableSegmentation: false,
          smoothSegmentation: false,
          // TASK 6: Use versioned URLs from constants
          detectorModelUrl: BLAZEPOSE_MODEL_VERSIONS.detector.url,
          landmarkModelUrl: BLAZEPOSE_MODEL_VERSIONS.landmark[modelQuality.toLowerCase() as 'full' | 'heavy'].url
        });
        
        // TASK 3: Reduced detector loading logging
        if (PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING) {
          logDetectionResult('BlazePose detector loaded successfully', { modelQuality });
        }

        setDetector(blazePoseDetector);
        setIsInitialized(true);
        
        setDebugInfo(`Phase 5: Custom BlazePose (${modelQuality}) - Complete Pipeline - ${tf.getBackend()}`);
        
      } catch (error) {
        logger.error('detection', 'BlazePose initialization failed', { error: error.message });
        setDebugInfo(`BlazePose Init Error: ${error.message}`);
        setIsInitialized(false);
        setDetector(null);
      }
    };

    initBlazePose();
    
    return () => {
      if (detector) {
        console.log('🧹 PHASE 5: Cleaning up custom BlazePose detector');
        try {
          detector.dispose();
        } catch (disposeError) {
          console.warn('⚠️ Error disposing custom BlazePose detector:', disposeError);
        }
      }
    };
  }, [modelQuality]);

  const detectPoses = async (video: HTMLVideoElement) => {
    // Always log the first few detection attempts
    const shouldLog = Math.random() < 0.01; // 1% sampling for ongoing logging

    if (shouldLog) {
      console.log('🔍 POSE DETECTION: detectPoses called', {
        hasDetector: !!detector,
        hasVideo: !!video,
        isInitialized,
        videoReadyState: video?.readyState,
        videoDimensions: video ? `${video.videoWidth}x${video.videoHeight}` : 'N/A'
      });
    }

    if (!detector || !video || !isInitialized) {
      if (shouldLog) {
        console.warn('⚠️ POSE DETECTION: Not ready', {
          detector: !!detector,
          video: !!video,
          initialized: isInitialized
        });
      }
      return [];
    }

    if (video.readyState < 2 || video.videoWidth === 0 || video.videoHeight === 0) {
      if (shouldLog) {
        console.warn('⚠️ POSE DETECTION: Video not ready for processing', {
          readyState: video.readyState,
          dimensions: `${video.videoWidth}x${video.videoHeight}`
        });
      }
      return [];
    }

    try {
      // TASK 3: Reduced detection process logging
      const imageSize = getImageSize(video);

      if (PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING) {
        logDetectionResult('Starting pose estimation', { videoDimensions: imageSize });
      }

      // Use our custom BlazePose detector
      const estimationConfig = {
        maxPoses: 1,
        flipHorizontal: false
      };

      const poses = await detector.estimatePoses(video, estimationConfig);

      // Always log successful detection results
      if (shouldLog || poses.length > 0) {
        console.log('✅ POSE DETECTION: Detection completed', {
          posesDetected: poses.length,
          firstPoseKeypoints: poses[0]?.keypoints?.length || 0,
          firstPose3D: poses[0]?.keypoints3D?.length || 0
        });
      }
      
      if (poses.length > 0) {
        const pose = poses[0];
        
        // PHASE 5: Apply enhanced processing with complete pipeline
        const processedPose = await processBlazePoseWithCompletePipeline(pose, imageSize);
        
        // TASK 3: Heavily reduced keypoint audit logging
        if (PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING && processedPose.keypoints && processedPose.keypoints.length > 0) {
          const sampleKeypoint = processedPose.keypoints[0];
          logDetectionResult('Pose processing completed', {
            keypoints2D: processedPose.keypoints.length,
            sampleKeypoint: {
              name: sampleKeypoint.name || 'unknown',
              x: sampleKeypoint.x?.toFixed(2),
              y: sampleKeypoint.y?.toFixed(2),
              score: sampleKeypoint.score?.toFixed(3),
              isValid: !isNaN(sampleKeypoint.x) && !isNaN(sampleKeypoint.y)
            }
          });
        }
        
        // TASK 3: Reduced 3D keypoint logging
        if (PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING && processedPose.keypoints3D && processedPose.keypoints3D.length > 0) {
          const sample3D = processedPose.keypoints3D[0];
          logDetectionResult('3D keypoints processed', {
            keypoints3D: processedPose.keypoints3D.length,
            sample3D: {
              name: sample3D.name || 'unknown',
              x: sample3D.x?.toFixed(4),
              y: sample3D.y?.toFixed(4),
              z: sample3D.z?.toFixed(4),
              isValid: !isNaN(sample3D.x) && !isNaN(sample3D.y) && !isNaN(sample3D.z)
            }
          });
        }

        // TASK 3: Memory cleanup
        memoryManager.performCleanup();

        return [processedPose];
      } else {
        // TASK 3: Reduced no-pose logging
        if (PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING) {
          logDetectionResult('No poses detected');
        }
        return [];
      }

    } catch (error) {
      logger.error('detection', 'Pose detection error', { error: error.message });
      setDebugInfo(`Pose Detection Error: ${error.message}`);
      return [];
    }
  };

  // PHASE 5: Complete pipeline processing with proper keypoint naming
  const processBlazePoseWithCompletePipeline = async (pose: any, imageSize: any) => {
    console.log('🔧 PHASE 5: Applying complete pipeline processing...');
    
    try {
      let processedKeypoints = pose.keypoints;
      let processedKeypoints3D = pose.keypoints3D;
      
      // Step 1: Ensure proper keypoint naming
      if (processedKeypoints) {
        processedKeypoints = processedKeypoints.map((kp: any, index: number) => ({
          ...kp,
          name: kp.name || BLAZEPOSE_KEYPOINTS[index] || `landmark_${index}`
        }));
      }
      
      if (processedKeypoints3D) {
        processedKeypoints3D = processedKeypoints3D.map((kp: any, index: number) => ({
          ...kp,
          name: kp.name || BLAZEPOSE_KEYPOINTS[index] || `world_landmark_${index}`
        }));
      }
      
      // Step 2: Apply coordinate validation and cleaning
      if (processedKeypoints || processedKeypoints3D) {
        const { landmarks, worldLandmarks } = validateAndCleanPoseCoordinates(
          processedKeypoints || [],
          processedKeypoints3D || []
        );
        
        processedKeypoints = landmarks;
        processedKeypoints3D = worldLandmarks;
        console.log('✅ PHASE 5: Applied complete coordinate validation and cleaning');
      }
      
      // Step 3: Enhanced keypoint processing with proper scaling
      if (processedKeypoints && processedKeypoints.length > 0) {
        console.log('🔧 PHASE 5: Complete keypoint processing...');

        // FIXED: Remove double projection - detector already provides correctly projected coordinates
        // The landmarks from detector.ts are already projected to image coordinates
        // No need to re-calculate ROI and re-project, this was causing all landmarks to collapse to center

        // Check if coordinates need scaling (0-1 range to pixels)
        const needsScaling = processedKeypoints.some((kp: any) =>
          kp.x <= 1.0 && kp.y <= 1.0 && kp.x >= 0.0 && kp.y >= 0.0
        );

        if (needsScaling) {
          processedKeypoints = normalizedKeypointsToKeypoints(processedKeypoints, imageSize);
          console.log('✅ PHASE 5: Keypoints scaled to pixel coordinates');
        }
        
        // Apply visibility threshold filtering - PHASE 5 enhancement
        processedKeypoints = processedKeypoints.map((kp: any) => ({
          ...kp,
          score: Math.max(0, Math.min(1, kp.score || 0)),
          visible: (kp.score || 0) > 0.2 // Enhanced visibility threshold
        }));
        
        // Apply enhanced smoothing
        processedKeypoints = keypointsFilter.apply(processedKeypoints);
        console.log('✅ PHASE 5: Applied complete keypoints smoothing with visibility');
        
        // Apply stability filter
        const stabilityResult = stabilityFilter.apply(processedKeypoints);
        processedKeypoints = stabilityResult.keypoints;
        
        console.log('✅ PHASE 5: Applied pose stability filter:', {
          isStable: stabilityResult.isStable,
          confidence: stabilityResult.confidence.toFixed(3),
          visibleKeypoints: processedKeypoints.filter((kp: any) => kp.visible).length
        });
      }
      
      // Step 4: Complete 3D world landmarks processing
      if (processedKeypoints3D && processedKeypoints3D.length > 0) {
        console.log('🔧 PHASE 5: Complete 3D world landmarks processing...');

        // FIXED: Remove double projection for 3D landmarks as well
        // The 3D world landmarks from detector.ts are already properly projected
        // No need to re-calculate ROI and re-project
        console.log('✅ PHASE 5: Using original 3D world landmark coordinates (no re-projection)');
        
        // Apply visibility thresholding to 3D landmarks
        processedKeypoints3D = processedKeypoints3D.map((kp: any) => ({
          ...kp,
          score: Math.max(0, Math.min(1, kp.score || 0)),
          visible: (kp.score || 0) > 0.1 // Lower threshold for 3D landmarks
        }));
        
        processedKeypoints3D = visibilityFilter.apply(processedKeypoints3D);
        console.log('✅ PHASE 5: Applied complete visibility smoothing to 3D landmarks');
      }
      
      const processedPose = {
        ...pose,
        keypoints: processedKeypoints,
        keypoints3D: processedKeypoints3D
      };
      
      console.log('✅ PHASE 5: Complete pipeline processing finished');
      return processedPose;
      
    } catch (processingError) {
      console.error('❌ PHASE 5 PROCESSING: Complete pipeline processing error:', processingError);
      return pose;
    }
  };

  const resetDetector = () => {
    if (detector && typeof detector.reset === 'function') {
      console.log('🔄 PHASE 5: Resetting custom BlazePose detector and filters');
      try {
        detector.reset();
        keypointsFilter.reset();
        visibilityFilter.reset();
        roiFilter.reset();
        stabilityFilter.reset();
        console.log('✅ Custom BlazePose detector and filters reset successful');
      } catch (resetError) {
        console.warn('⚠️ Error resetting custom BlazePose detector:', resetError);
      }
    }
  };

  return {
    detector,
    isInitialized,
    debugInfo,
    setDebugInfo,
    detectPoses,
    resetDetector
  };
};
