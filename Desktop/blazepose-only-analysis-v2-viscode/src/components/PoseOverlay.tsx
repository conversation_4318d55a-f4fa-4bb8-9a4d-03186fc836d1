
import React, { useMemo, useCallback } from 'react';
import SideViewBlazePoseOverlay from './SideViewBlazePoseOverlay';

interface PoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  analysisType?: 'running';
  analysisMode?: '3D';
  viewType?: 'side' | 'rear';
  overlayStyle?: string;
  videoSetup?: 'Treadmill';
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const PoseOverlay: React.FC<PoseOverlayProps> = React.memo(({
  videoRef,
  analysisType = 'running',
  analysisMode = '3D',
  viewType = 'side',
  overlayStyle = 'Medical',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 },
  onPoseData
}) => {
  // Memoize stable userHeight object to prevent re-renders
  const stableUserHeight = useMemo(() => userHeight, [userHeight.feet, userHeight.inches]);

  // Memoize onPoseData callback to prevent re-renders
  const stableOnPoseData = useCallback((data: any) => {
    if (onPoseData) {
      onPoseData(data);
    }
  }, [onPoseData]);

  // Reduce console logging - only log when props actually change
  useMemo(() => {
    console.log('=== BLAZEPOSE FULL MODEL ===');
    console.log('✅ 3D Running Analysis - BlazePose Full');
    console.log('Props:', { analysisType, viewType, videoSetup, userHeight: stableUserHeight });
    console.log('============================');
  }, [analysisType, viewType, videoSetup, stableUserHeight]);
  
  // For now, only implement Side View
  if (viewType === 'side') {
    return (
      <>
        <SideViewBlazePoseOverlay
          videoRef={videoRef}
          userHeight={stableUserHeight}
          onPoseData={stableOnPoseData}
        />
        <DebugInfo
          viewType={viewType}
          userHeight={stableUserHeight}
        />
      </>
    );
  }

  // Rear view placeholder for now
  if (viewType === 'rear') {
    return (
      <div 
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0,0,0,0.8)',
          color: 'yellow',
          padding: '20px',
          borderRadius: '8px',
          textAlign: 'center',
          zIndex: 20
        }}
      >
        <h3>Rear View Coming Soon</h3>
        <p>Focusing on Side View first</p>
      </div>
    );
  }

  return null;
});

PoseOverlay.displayName = 'PoseOverlay';

const DebugInfo: React.FC<{
  viewType?: string;
  userHeight?: { feet: number; inches: number };
}> = React.memo(({
  viewType = 'side',
  userHeight = { feet: 5, inches: 10 }
}) => {
  return (
    <div 
      style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'lime',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 11
      }}
    >
      🔒 BLAZEPOSE FULL MODEL - {viewType} view - {userHeight.feet}'{userHeight.inches}"
    </div>
  );
});

DebugInfo.displayName = 'DebugInfo';

export default PoseOverlay;
