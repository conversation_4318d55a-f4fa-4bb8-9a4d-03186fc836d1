
import React, { useRef, useEffect, useState } from 'react';
import { useBlazePoseDetection } from '@/hooks/useBlazePoseDetection';
import { PERFORMANCE_LIMITS } from '@/blazepose_tfjs/constants';
import { globalPerformanceMonitor } from '@/blazepose_tfjs/performance_monitor';
import { globalPoseDrawer } from '@/utils/pose_drawing';
import { logger, logOverlayOperation, initializeOptimalLogging } from '@/utils/logging_system';
import { memoryManager } from '@/utils/memory_manager';

interface SideViewBlazePoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
  userHeight?: { feet: number; inches: number };
}

const SideViewBlazePoseOverlay: React.FC<SideViewBlazePoseOverlayProps> = ({ 
  videoRef, 
  onPoseData,
  userHeight = { feet: 5, inches: 10 }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [frameCount, setFrameCount] = useState(0);
  const [poseDataCount, setPoseDataCount] = useState(0);
  const [detectionActive, setDetectionActive] = useState(false);
  
  // Circuit breaker to prevent infinite loops
  const errorCountRef = useRef(0);
  const consecutiveErrorsRef = useRef(0);
  const MAX_CONSECUTIVE_ERRORS = 5;
  const [errorState, setErrorState] = useState<string | null>(null);
  
  const { isInitialized, debugInfo, detectPoses } = useBlazePoseDetection('Full');

  // TASK 1: Performance-aware logging configuration
  const ENABLE_DETAILED_LOGGING = PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING;

  // TASK 2: Configure enhanced drawing for better skeletal overlay visibility
  // TASK 5: Initialize optimal logging system for production performance
  useEffect(() => {
    // TASK 5: Initialize optimal logging configuration automatically
    initializeOptimalLogging();

    globalPoseDrawer.updateConfig({
      keypointRadius: 8,
      keypointColor: '#00FF00',
      keypointStrokeColor: '#FFFFFF',
      keypointStrokeWidth: 3,
      connectionColor: '#00FFFF',
      connectionWidth: 4,
      scoreThreshold: 0.1, // TASK 2: Further lowered for debugging visibility issues
      showLabels: true,
      showScores: false,
      enableDebugMode: ENABLE_DETAILED_LOGGING
    });
  }, [ENABLE_DETAILED_LOGGING]);

  useEffect(() => {
    console.log('🚀 DETECTION LOOP INITIALIZATION - Starting useEffect');
    const loopState = {
      isInitialized,
      hasVideo: !!videoRef.current,
      hasCanvas: !!canvasRef.current,
      videoReadyState: videoRef.current?.readyState,
      videoDimensions: videoRef.current ? `${videoRef.current.videoWidth}x${videoRef.current.videoHeight}` : 'N/A',
      videoSrc: videoRef.current?.src ? 'Has source' : 'No source'
    };
    console.log('📊 DETECTION LOOP STATE: ' + JSON.stringify(loopState));

    // TESTING: Log component initialization for end-to-end validation
    console.log('🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully');

    if (!isInitialized) {
      console.log('⏸️ DETECTION LOOP: BlazePose not initialized yet');
      return;
    }

    if (!videoRef.current) {
      console.log('⏸️ DETECTION LOOP: No video element available');
      return;
    }

    if (!canvasRef.current) {
      console.log('⏸️ DETECTION LOOP: No canvas element available');
      return;
    }

    console.log('✅ DETECTION LOOP: All prerequisites met, proceeding with initialization');

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error('❌ DETECTION LOOP: Failed to get canvas context');
      return;
    }

    console.log('✅ DETECTION LOOP: All components ready, initializing detection loop');
    const videoState = {
      readyState: video.readyState,
      currentTime: video.currentTime,
      duration: video.duration,
      paused: video.paused,
      ended: video.ended,
      videoWidth: video.videoWidth,
      videoHeight: video.videoHeight,
      src: video.src ? video.src.substring(0, 50) + '...' : 'No source'
    };
    console.log('📹 VIDEO STATE: ' + JSON.stringify(videoState));

    // Add video event listeners to track state changes
    const handleVideoStateChange = (event: Event) => {
      const eventData = {
        readyState: video.readyState,
        currentTime: video.currentTime,
        paused: video.paused,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight
      };
      console.log('📹 VIDEO EVENT: ' + event.type + ' ' + JSON.stringify(eventData));
    };

    video.addEventListener('loadstart', handleVideoStateChange);
    video.addEventListener('loadedmetadata', handleVideoStateChange);
    video.addEventListener('loadeddata', handleVideoStateChange);
    video.addEventListener('canplay', handleVideoStateChange);
    video.addEventListener('canplaythrough', handleVideoStateChange);
    video.addEventListener('play', handleVideoStateChange);
    video.addEventListener('pause', handleVideoStateChange);

    const updateCanvasSize = () => {
      if (!video || !canvas) return false;
      
      // Wait for video to have actual dimensions
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        return false;
      }
      
      // Get the video element's current display size
      const videoRect = video.getBoundingClientRect();
      
      if (videoRect.width === 0 || videoRect.height === 0) {
        return false;
      }
      
      // Set canvas size to match video display exactly
      canvas.width = videoRect.width;
      canvas.height = videoRect.height;
      
      // Position canvas to overlay video perfectly
      canvas.style.position = 'absolute';
      canvas.style.top = '0';
      canvas.style.left = '0';
      canvas.style.width = `${videoRect.width}px`;
      canvas.style.height = `${videoRect.height}px`;
      canvas.style.pointerEvents = 'none';
      canvas.style.zIndex = '10';
      canvas.style.background = 'transparent';
      
      const canvasPositioning = {
        canvasSize: `${canvas.width}x${canvas.height}`,
        videoRect: `${videoRect.width}x${videoRect.height}`,
        position: `${canvas.style.top}, ${canvas.style.left}`,
        zIndex: canvas.style.zIndex
      };
      console.log('🎨 CANVAS POSITIONING: ' + JSON.stringify(canvasPositioning));
      
      return true;
    };

    // TASK 4: Frame rate limiting and performance optimization
    const TARGET_FPS = 30;
    const FRAME_INTERVAL = 1000 / TARGET_FPS; // 33.33ms
    const MAX_PROCESSING_TIME = 25; // Leave 8ms buffer for rendering
    let lastFrameTime = 0;

    const detectPose = async () => {
      // Define currentFrame and processingStartTime outside try block so they're accessible in finally block
      let currentFrame = frameCount + 1;
      const processingStartTime = performance.now();

      try {
        // TASK 4: Frame rate limiting - Skip frame if not enough time has passed
        const currentTime = performance.now();
        if (currentTime - lastFrameTime < FRAME_INTERVAL) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }
        lastFrameTime = currentTime;
        globalPerformanceMonitor.startFrame();

        // Check if video is ready to play - use less restrictive check
        if (!video || video.readyState < 2) {
          if (currentFrame % 30 === 0) { // Log every 30 frames to avoid spam
            console.log('⏳ DETECTION LOOP: Video not ready', {
              readyState: video?.readyState,
              frame: currentFrame
            });
          }
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Check if video is paused or ended
        if (video.paused || video.ended) {
          if (currentFrame % 60 === 0) { // Log every 60 frames to avoid spam
            console.log('⏸️ DETECTION LOOP: Video paused/ended', {
              paused: video.paused,
              ended: video.ended,
              frame: currentFrame
            });
          }
          setDetectionActive(false);
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Update currentFrame and frameCount
        currentFrame = frameCount + 1;
        setFrameCount(currentFrame);
        setDetectionActive(true);

        // TASK 4: Check processing time budget early
        let processingTime = performance.now() - processingStartTime;
        if (processingTime > MAX_PROCESSING_TIME) {
          if (ENABLE_DETAILED_LOGGING && currentFrame % 30 === 0) {
            console.warn('🔧 PERFORMANCE: Skipping frame due to time budget exceeded', {
              processingTime: processingTime.toFixed(2),
              budget: MAX_PROCESSING_TIME
            });
          }
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // TASK 4: Enhanced performance monitoring check
        if (globalPerformanceMonitor.shouldLimitProcessing()) {
          if (ENABLE_DETAILED_LOGGING && currentFrame % 30 === 0) {
            console.warn('🔧 PERFORMANCE: Skipping frame due to performance monitor limit');
          }
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }
        
        // Update canvas size - skip frame if not ready
        if (!updateCanvasSize()) {
          if (currentFrame % 30 === 0) { // Log every 30 frames to avoid spam
            console.log('📐 DETECTION LOOP: Canvas sizing failed', {
              videoDimensions: `${video.videoWidth}x${video.videoHeight}`,
              videoRect: video.getBoundingClientRect(),
              frame: currentFrame
            });
          }
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // TASK 3: CANVAS RENDERING FIX - Clear canvas only when we have poses to draw
        // Don't clear the canvas here - wait until we have pose data to draw

        // TASK 3: CANVAS TEST - Always draw test indicators to verify canvas is working
        ctx.save();
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // TESTING: Log canvas test rendering for end-to-end validation
        const testIndicators = {
          canvasSize: `${canvas.width}x${canvas.height}`,
          frame: currentFrame
        };
        console.log('🧪 E2E TEST: Drawing canvas test indicators ' + JSON.stringify(testIndicators));

        // Draw test pattern to confirm canvas is visible
        ctx.fillStyle = 'rgba(255, 0, 255, 0.7)'; // Magenta test color
        ctx.fillRect(50, 50, 100, 100); // Test rectangle

        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 16px Arial';
        ctx.fillText('CANVAS TEST', 60, 110);

        // CRITICAL FIX: Always draw red "+" indicator as baseline test (regardless of pose detection)
        ctx.strokeStyle = 'rgba(255, 0, 0, 1.0)'; // Bright red, full opacity
        ctx.lineWidth = 4; // Thicker line for better visibility
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        ctx.beginPath();
        ctx.moveTo(centerX - 30, centerY);
        ctx.lineTo(centerX + 30, centerY);
        ctx.moveTo(centerX, centerY - 30);
        ctx.lineTo(centerX, centerY + 30);
        ctx.stroke();

        // Add center dot for better visibility
        ctx.fillStyle = 'rgba(255, 0, 0, 1.0)';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 5, 0, 2 * Math.PI);
        ctx.fill();

        // Add bright border around entire canvas for maximum visibility
        ctx.strokeStyle = 'rgba(0, 255, 0, 1.0)'; // Bright green border
        ctx.lineWidth = 8;
        ctx.strokeRect(4, 4, canvas.width - 8, canvas.height - 8);

        console.log('🔴 BASELINE TEST: Red "+" indicator drawn at center', JSON.stringify({
          centerX: centerX,
          centerY: centerY,
          canvasSize: `${canvas.width}x${canvas.height}`
        }));

        ctx.restore();

        // TASK 4: Check processing time budget before expensive pose detection
        processingTime = performance.now() - processingStartTime;
        if (processingTime > MAX_PROCESSING_TIME * 0.5) { // Use half budget for pre-detection checks
          if (ENABLE_DETAILED_LOGGING && currentFrame % 30 === 0) {
            console.warn('🔧 PERFORMANCE: Skipping pose detection due to time budget', {
              processingTime: processingTime.toFixed(2),
              halfBudget: (MAX_PROCESSING_TIME * 0.5).toFixed(2)
            });
          }
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Always log the first few pose detections to confirm it's working
        if (currentFrame <= 5 || currentFrame % 100 === 0) {
          console.log('🎯 DETECTION LOOP: Starting pose detection', {
            frame: currentFrame,
            videoTime: video.currentTime.toFixed(2),
            canvasSize: `${canvas.width}x${canvas.height}`
          });
        }

        // Detect poses using ENHANCED BlazePose Custom
        const poses = await detectPoses(video);

        // Always log the first few detection results
        if (currentFrame <= 5 || currentFrame % 100 === 0) {
          const detectionResult = {
            frame: currentFrame,
            posesFound: poses.length,
            firstPoseKeypoints: poses[0]?.keypoints?.length || 0
          };
          console.log('📊 DETECTION LOOP: Pose detection result ' + JSON.stringify(detectionResult));
        }

        // TASK 4: Check processing time budget after pose detection
        processingTime = performance.now() - processingStartTime;
        if (processingTime > MAX_PROCESSING_TIME) {
          if (ENABLE_DETAILED_LOGGING && currentFrame % 30 === 0) {
            console.warn('🔧 PERFORMANCE: Frame processing exceeded budget after detection', {
              processingTime: processingTime.toFixed(2),
              budget: MAX_PROCESSING_TIME
            });
          }
          // Continue with reduced processing
        }

        // TASK 4: Reduced pose detection logging
        if (ENABLE_DETAILED_LOGGING && currentFrame % 100 === 0) { // Reduced frequency
          logOverlayOperation('Pose detection result', { frame: currentFrame, poses: poses.length });
        }
        
        if (poses && poses.length > 0) {
          const pose = poses[0];
          setPoseDataCount(prev => prev + 1);
          
          // TASK 3: Heavily reduced keypoint logging
          if (ENABLE_DETAILED_LOGGING && currentFrame % 100 === 0) { // Log every 100 frames
            logOverlayOperation('Pose detected', {
              frame: currentFrame,
              keypoints2D: pose.keypoints?.length || 0,
              keypoints3D: pose.keypoints3D?.length || 0,
              sampleKeypoint: pose.keypoints?.[0] ? {
                name: pose.keypoints[0].name,
                x: pose.keypoints[0].x?.toFixed(3),
                y: pose.keypoints[0].y?.toFixed(3),
                score: pose.keypoints[0].score?.toFixed(3)
              } : null
            });
          }

          // TASK 3: CANVAS RENDERING FIX - Clear canvas and draw pose in correct order
          try {
            // Clear canvas right before drawing pose
            // TASK 1: Replace console.log with conditional logging
            if (ENABLE_DETAILED_LOGGING && currentFrame <= 5) {
              logOverlayOperation('Clearing canvas before drawing pose', {
                frame: currentFrame,
                canvasSize: `${canvas.width}x${canvas.height}`
              });
            }

            ctx.save();
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // TASK 3: Add visual debugging indicators to confirm canvas is working
            if (currentFrame <= 10 || currentFrame % 30 === 0) {
              // Draw frame indicator
              ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
              ctx.font = 'bold 14px Arial';
              ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
              ctx.lineWidth = 2;
              ctx.strokeText(`Frame ${currentFrame}`, 10, 25);
              ctx.fillText(`Frame ${currentFrame}`, 10, 25);

              // Draw canvas test indicator (corner markers)
              ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
              ctx.fillRect(0, 0, 20, 20); // Top-left
              ctx.fillRect(canvas.width - 20, 0, 20, 20); // Top-right
              ctx.fillRect(0, canvas.height - 20, 20, 20); // Bottom-left
              ctx.fillRect(canvas.width - 20, canvas.height - 20, 20, 20); // Bottom-right

              // Draw center crosshair
              ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
              ctx.lineWidth = 3;
              const centerX = canvas.width / 2;
              const centerY = canvas.height / 2;
              ctx.beginPath();
              ctx.moveTo(centerX - 20, centerY);
              ctx.lineTo(centerX + 20, centerY);
              ctx.moveTo(centerX, centerY - 20);
              ctx.lineTo(centerX, centerY + 20);
              ctx.stroke();
            }
            ctx.restore();

            // TASK 1: Replace console.log with conditional logging
            if (ENABLE_DETAILED_LOGGING && currentFrame <= 5) {
              logOverlayOperation('Starting pose drawing', {
                frame: currentFrame,
                poseKeypoints: pose.keypoints?.length || 0
              });
            }

            const drawingResult = globalPoseDrawer.drawPose(ctx, pose, canvas, video);

            // TASK 1: Replace console.log with conditional logging - reduced frequency
            if (ENABLE_DETAILED_LOGGING && (currentFrame <= 3 || currentFrame % 100 === 0)) {
              logOverlayOperation('Pose drawing completed', {
                frame: currentFrame,
                keypointsDrawn: drawingResult.keypointsDrawn,
                connectionsDrawn: drawingResult.connectionsDrawn,
                coordinateSystem: drawingResult.coordinateSystem,
                errors: drawingResult.errors.length,
                boundingBox: drawingResult.boundingBox
              });
            }

            // Log drawing errors with reduced frequency
            if (drawingResult.errors.length > 0 && Math.random() < 0.1) { // 10% sampling
              logger.warn('overlay', 'Pose drawing errors detected', {
                errorCount: drawingResult.errors.length,
                sampleError: drawingResult.errors[0]
              });
            }

          } catch (visualizationError) {
            console.warn('⚠️ Pose visualization error (non-critical):', visualizationError);
            // Continue processing - visualization errors should not stop pose detection
          }
          
          // Pass enhanced pose data to parent
          if (onPoseData) {
            onPoseData({
              frame: currentFrame,
              pose: pose,
              pipeline: 'BlazePose-Custom-Enhanced',
              userHeight: userHeight,
              coordinatesCount: {
                keypoints2D: pose.keypoints?.length || 0,
                keypoints3D: pose.keypoints3D?.length || 0
              },
              enhancement: 'tensor-processing-applied'
            });
          }
          
        } else {
          // TASK 1: Replace console.log with conditional logging - reduced frequency
          if (ENABLE_DETAILED_LOGGING && (currentFrame <= 3 || currentFrame % 200 === 0)) {
            logOverlayOperation('No poses detected, drawing fallback indicator', {
              frame: currentFrame
            });

            // Clear canvas and draw "no pose" indicator
            ctx.save();
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw "No Pose" indicator
            ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
            ctx.font = 'bold 20px Arial';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.lineWidth = 2;
            const text = 'No Pose Detected';
            const textWidth = ctx.measureText(text).width;
            const x = (canvas.width - textWidth) / 2;
            const y = canvas.height / 2;
            ctx.strokeText(text, x, y);
            ctx.fillText(text, x, y);

            // CRITICAL FIX: Always draw red "+" baseline indicator even when no poses detected
            ctx.strokeStyle = 'rgba(255, 0, 0, 1.0)'; // Bright red, full opacity
            ctx.lineWidth = 4; // Thicker line for better visibility
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            ctx.beginPath();
            ctx.moveTo(centerX - 30, centerY);
            ctx.lineTo(centerX + 30, centerY);
            ctx.moveTo(centerX, centerY - 30);
            ctx.lineTo(centerX, centerY + 30);
            ctx.stroke();

            // Add center dot for better visibility
            ctx.fillStyle = 'rgba(255, 0, 0, 1.0)';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 5, 0, 2 * Math.PI);
            ctx.fill();

            console.log('🔴 NO POSE BASELINE: Red "+" indicator drawn at center', JSON.stringify({
              centerX: centerX,
              centerY: centerY,
              canvasSize: `${canvas.width}x${canvas.height}`
            }));

            ctx.restore();
          }
        }
        
        // Reset consecutive errors on successful frame
        consecutiveErrorsRef.current = 0;
        setErrorState(null);
        
      } catch (error) {
        errorCountRef.current += 1;
        consecutiveErrorsRef.current += 1;

        console.error(`❌ Enhanced detection loop error (${consecutiveErrorsRef.current}/${MAX_CONSECUTIVE_ERRORS}):`, error);

        // Circuit breaker: stop after too many consecutive errors
        if (consecutiveErrorsRef.current >= MAX_CONSECUTIVE_ERRORS) {
          console.error('🚨 CIRCUIT BREAKER: Too many consecutive errors, stopping detection loop');
          setErrorState(`Circuit breaker triggered after ${MAX_CONSECUTIVE_ERRORS} consecutive errors`);
          setDetectionActive(false);
          return; // Stop the loop
        }

        // Add delay before retrying after error
        await new Promise(resolve => setTimeout(resolve, 100));
      } finally {
        // TASK 4: Calculate total processing time for this frame
        const totalProcessingTime = performance.now() - processingStartTime;

        // FIXED: Removed processLandmarksInTime call - function does not exist
        // Landmark processing with timing is handled by KeypointsSmoothingFilter.apply()
        // which is already implemented in the pose detection pipeline.

        // TASK 4: End performance monitoring for this frame
        const frameTime = globalPerformanceMonitor.endFrame();

        // TASK 4: Enhanced performance monitoring with budget tracking
        if (totalProcessingTime > MAX_PROCESSING_TIME) {
          if (ENABLE_DETAILED_LOGGING && currentFrame % 10 === 0) { // Log every 10 budget violations
            console.warn('🔧 PERFORMANCE: Frame processing exceeded budget', {
              frame: currentFrame,
              actualTime: totalProcessingTime.toFixed(2) + 'ms',
              budget: MAX_PROCESSING_TIME + 'ms',
              overrun: (totalProcessingTime - MAX_PROCESSING_TIME).toFixed(2) + 'ms'
            });
          }
        }

        // TASK 4: Optimized performance monitoring - reduced frequency
        if (frameTime > PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS && ENABLE_DETAILED_LOGGING && currentFrame % 30 === 0) {
          logger.warn('performance', 'Frame processing time exceeded limit', {
            frame: currentFrame,
            actualTime: `${frameTime.toFixed(2)}ms`,
            limit: `${PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS}ms`,
            budgetTime: `${totalProcessingTime.toFixed(2)}ms`
          });
        }

        // TASK 3: Periodic memory cleanup
        if (currentFrame % PERFORMANCE_LIMITS.MEMORY_CLEANUP_INTERVAL === 0) {
          memoryManager.performCleanup();
        }
      }

      // Continue animation loop only if no circuit breaker
      if (consecutiveErrorsRef.current < MAX_CONSECUTIVE_ERRORS) {
        // TASK 1: Use async scheduling to prevent blocking
        if (PERFORMANCE_LIMITS.USE_ASYNC_PROCESSING) {
          setTimeout(() => {
            animationFrameRef.current = requestAnimationFrame(detectPose);
          }, 0);
        } else {
          animationFrameRef.current = requestAnimationFrame(detectPose);
        }
      }
    };

    // Start detection with a small delay
    console.log('⏰ DETECTION LOOP: Setting up 1-second delay before starting detection');
    const startDelay = setTimeout(() => {
      console.log('🚀 DETECTION LOOP: Starting pose detection loop now!');
      detectPose();
    }, 1000);

    return () => {
      // Cleaning up ENHANCED Side View BlazePose detection
      console.log('🧹 DETECTION LOOP: Cleaning up detection loop');
      clearTimeout(startDelay);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      setDetectionActive(false);

      // Clean up video event listeners
      if (video) {
        video.removeEventListener('loadstart', handleVideoStateChange);
        video.removeEventListener('loadedmetadata', handleVideoStateChange);
        video.removeEventListener('loadeddata', handleVideoStateChange);
        video.removeEventListener('canplay', handleVideoStateChange);
        video.removeEventListener('canplaythrough', handleVideoStateChange);
        video.removeEventListener('play', handleVideoStateChange);
        video.removeEventListener('pause', handleVideoStateChange);
      }
    };
  }, [isInitialized, videoRef, detectPoses, onPoseData, userHeight]);

  // TASK 2: Old drawing functions removed - replaced with enhanced pose drawing system
  // The new globalPoseDrawer handles coordinate detection, validation, and proper canvas layering



  // Custom BlazePose detector handles errors internally - no error UI needed

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 15, // Higher z-index to ensure it's on top
          border: '3px solid lime', // More visible border
          // TASK 3: Enhanced canvas styling for maximum visibility
          mixBlendMode: 'normal', // Ensure proper blending
          opacity: 1, // Full opacity for pose overlay
          backgroundColor: 'rgba(0, 0, 0, 0.1)' // Slight background to make it visible
        }}
      />
      
      <div 
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          background: 'rgba(0,0,0,0.9)',
          color: 'lime',
          padding: '12px',
          borderRadius: '6px',
          fontSize: '14px',
          zIndex: 11,
          border: '2px solid lime'
        }}
      >
        <div>🎯 BlazePose Full - ENHANCED</div>
        <div>📊 Frames: {frameCount}</div>
        <div>📊 Poses: {poseDataCount}</div>
        <div>🔧 Status: {detectionActive ? 'DETECTING' : 'WAITING'}</div>
        <div>🔧 {debugInfo || 'Initializing...'}</div>
        <div>👤 Height: {userHeight.feet}'{userHeight.inches}"</div>
        <div>📐 Canvas: {canvasRef.current ? `${canvasRef.current.width}x${canvasRef.current.height}` : 'Not ready'}</div>
        <div>🎥 Video: {videoRef.current ? `${videoRef.current.videoWidth}x${videoRef.current.videoHeight}` : 'Not ready'}</div>
        <div>⚡ Mode: Enhanced Tensor Processing</div>
        {errorState && (
          <div style={{ color: 'red', fontWeight: 'bold' }}>
            🚨 {errorState}
          </div>
        )}
      </div>
    </>
  );
};

export default SideViewBlazePoseOverlay;
