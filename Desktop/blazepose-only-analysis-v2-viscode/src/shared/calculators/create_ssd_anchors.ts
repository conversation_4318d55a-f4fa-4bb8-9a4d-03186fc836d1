
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * PHASE 1: SSD anchor generation for BlazePose detection phase.
 */

export interface SsdAnchorConfig {
  inputSizeWidth: number;
  inputSizeHeight: number;
  minScale: number;
  maxScale: number;
  anchorOffsetX: number;
  anchorOffsetY: number;
  numLayers: number;
  featureMapWidth: number[];
  featureMapHeight: number[];
  strides: number[];
  aspectRatios: number[];
  reduceBoxesInLowestLayer: boolean;
  interpolatedScaleAspectRatio: number;
  fixedAnchorSize: boolean;
}

export interface Anchor {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
}

/**
 * Creates SSD anchors for pose detection.
 */
export function createSsdAnchors(config: SsdAnchorConfig): Anchor[] {
  console.log('🔧 SSD ANCHORS: Creating anchors with config:', config);
  console.log('🔧 SSD ANCHORS: Feature map dimensions:', {
    heights: config.featureMapHeight,
    widths: config.featureMapWidth,
    numLayers: config.numLayers,
    aspectRatios: config.aspectRatios
  });
  
  // CRITICAL DEBUG: Check anchor offset values
  console.log('🔧 SSD ANCHORS: CRITICAL DEBUG - Anchor offsets:', {
    anchorOffsetX: config.anchorOffsetX,
    anchorOffsetY: config.anchorOffsetY,
    potentialForNegativeX: config.anchorOffsetX < 0,
    potentialForNegativeY: config.anchorOffsetY < 0
  });
  
  const anchors: Anchor[] = [];
  let layerIndex = 0;

  while (layerIndex < config.numLayers) {
    // CRITICAL: Dynamic calculation like reference implementation
    let featureMapHeight = 0;
    let featureMapWidth = 0;
    
    if (config.featureMapHeight.length > 0) {
      featureMapHeight = config.featureMapHeight[layerIndex];
      featureMapWidth = config.featureMapWidth[layerIndex];
    } else {
      // Dynamic calculation based on input size and stride (reference pattern)
      const stride = config.strides[layerIndex];
      featureMapHeight = Math.ceil(config.inputSizeHeight / stride);
      featureMapWidth = Math.ceil(config.inputSizeWidth / stride);
    }
    
    console.log(`🔧 SSD ANCHORS: Layer ${layerIndex}: ${featureMapWidth}x${featureMapHeight} (stride: ${config.strides[layerIndex]})`);
    
    for (let y = 0; y < featureMapHeight; y++) {
      for (let x = 0; x < featureMapWidth; x++) {
        for (let aspectRatioIndex = 0; aspectRatioIndex < config.aspectRatios.length; aspectRatioIndex++) {
          const aspectRatio = config.aspectRatios[aspectRatioIndex];
          
          // Calculate anchor dimensions
          const stride = config.strides[layerIndex];
          const xCenter = (x + config.anchorOffsetX) / featureMapWidth;
          const yCenter = (y + config.anchorOffsetY) / featureMapHeight;
          
          // Scale calculation
          const scale = config.minScale + (config.maxScale - config.minScale) * layerIndex / (config.numLayers - 1);
          
          const width = scale * Math.sqrt(aspectRatio);
          const height = scale / Math.sqrt(aspectRatio);
          
          // CRITICAL DEBUG: Log first few anchors to check coordinate ranges
          if (anchors.length < 5) {
            console.log(`🔧 SSD ANCHOR ${anchors.length + 1}: Coordinates check`, {
              layer: layerIndex,
              gridPosition: { x, y },
              featureMapSize: { width: featureMapWidth, height: featureMapHeight },
              offsets: { x: config.anchorOffsetX, y: config.anchorOffsetY },
              calculated: { xCenter, yCenter, width, height },
              isNegative: { x: xCenter < 0, y: yCenter < 0 },
              isOutOfRange: { x: xCenter < 0 || xCenter > 1, y: yCenter < 0 || yCenter > 1 }
            });
          }
          
          // CRITICAL DEBUG: Warn about any negative or out-of-range coordinates
          if (xCenter < 0 || xCenter > 1 || yCenter < 0 || yCenter > 1) {
            console.warn(`🔧 SSD ANCHOR OUT OF RANGE: Layer ${layerIndex}, Grid (${x},${y})`, {
              xCenter, yCenter, width, height,
              calculation: `(${x} + ${config.anchorOffsetX}) / ${featureMapWidth} = ${xCenter}`
            });
          }

          anchors.push({
            xCenter,
            yCenter,
            width,
            height
          });
        }
      }
    }
    layerIndex++;
  }

  console.log(`🔧 SSD ANCHORS: Created ${anchors.length} anchors`);
  return anchors;
}
