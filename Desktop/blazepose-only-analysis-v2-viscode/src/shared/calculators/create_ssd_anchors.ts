
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * PHASE 1: SSD anchor generation for BlazePose detection phase.
 */

export interface SsdAnchorConfig {
  inputSizeWidth: number;
  inputSizeHeight: number;
  minScale: number;
  maxScale: number;
  anchorOffsetX: number;
  anchorOffsetY: number;
  numLayers: number;
  featureMapWidth: number[];
  featureMapHeight: number[];
  strides: number[];
  aspectRatios: number[];
  reduceBoxesInLowestLayer: boolean;
  interpolatedScaleAspectRatio: number;
  fixedAnchorSize: boolean;
}

export interface Anchor {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
}

/**
 * Creates SSD anchors for pose detection.
 * FIXED: Now includes interpolatedScaleAspectRatio implementation matching TensorFlow.js reference
 */
export function createSsdAnchors(config: SsdAnchorConfig): Anchor[] {
  console.log('🔧 SSD ANCHORS: Creating anchors with config:', JSON.stringify({
    numLayers: config.numLayers,
    aspectRatios: config.aspectRatios,
    interpolatedScaleAspectRatio: config.interpolatedScaleAspectRatio,
    strides: config.strides
  }));

  // Set defaults like reference implementation
  if (config.interpolatedScaleAspectRatio == null) {
    config.interpolatedScaleAspectRatio = 1.0;
  }

  const anchors: Anchor[] = [];
  let layerId = 0;

  while (layerId < config.numLayers) {
    const anchorHeight: number[] = [];
    const anchorWidth: number[] = [];
    const aspectRatios: number[] = [];
    const scales: number[] = [];

    // CRITICAL FIX: Group layers by same stride (reference pattern)
    let lastSameStrideLayer = layerId;
    while (lastSameStrideLayer < config.strides.length &&
           config.strides[lastSameStrideLayer] === config.strides[layerId]) {

      const scale = calculateScale(config.minScale, config.maxScale,
                                 lastSameStrideLayer, config.strides.length);

      // Add standard aspect ratios
      for (let aspectRatioId = 0; aspectRatioId < config.aspectRatios.length; ++aspectRatioId) {
        aspectRatios.push(config.aspectRatios[aspectRatioId]);
        scales.push(scale);
      }

      // CRITICAL FIX: Add interpolated scale anchor
      if (config.interpolatedScaleAspectRatio > 0.0) {
        const scaleNext = lastSameStrideLayer === config.strides.length - 1 ?
          1.0 :
          calculateScale(config.minScale, config.maxScale,
                        lastSameStrideLayer + 1, config.strides.length);
        const interpolatedScale = Math.sqrt(scale * scaleNext);
        scales.push(interpolatedScale);
        aspectRatios.push(config.interpolatedScaleAspectRatio);

        console.log(`🔧 SSD ANCHORS: Added interpolated anchor - Layer ${lastSameStrideLayer}, scale: ${scale.toFixed(4)}, scaleNext: ${scaleNext.toFixed(4)}, interpolated: ${interpolatedScale.toFixed(4)}`);
      }

      lastSameStrideLayer++;
    }

    // Generate anchor dimensions
    for (let i = 0; i < aspectRatios.length; ++i) {
      const ratioSqrts = Math.sqrt(aspectRatios[i]);
      anchorHeight.push(scales[i] / ratioSqrts);
      anchorWidth.push(scales[i] * ratioSqrts);
    }

    console.log(`🔧 SSD ANCHORS: Layer ${layerId} - Generated ${aspectRatios.length} anchor types (${config.aspectRatios.length} standard + ${config.interpolatedScaleAspectRatio > 0 ? 1 : 0} interpolated)`);

    // Calculate feature map dimensions
    let featureMapHeight = 0;
    let featureMapWidth = 0;

    if (config.featureMapHeight.length > 0) {
      featureMapHeight = config.featureMapHeight[layerId];
      featureMapWidth = config.featureMapWidth[layerId];
    } else {
      const stride = config.strides[layerId];
      featureMapHeight = Math.ceil(config.inputSizeHeight / stride);
      featureMapWidth = Math.ceil(config.inputSizeWidth / stride);
    }

    console.log(`🔧 SSD ANCHORS: Layer ${layerId}: ${featureMapWidth}x${featureMapHeight} (stride: ${config.strides[layerId]})`);

    // Generate spatial anchors
    for (let y = 0; y < featureMapHeight; ++y) {
      for (let x = 0; x < featureMapWidth; ++x) {
        for (let anchorId = 0; anchorId < anchorHeight.length; ++anchorId) {
          const xCenter = (x + config.anchorOffsetX) / featureMapWidth;
          const yCenter = (y + config.anchorOffsetY) / featureMapHeight;

          const newAnchor: Anchor = {
            xCenter,
            yCenter,
            width: config.fixedAnchorSize ? 1.0 : anchorWidth[anchorId],
            height: config.fixedAnchorSize ? 1.0 : anchorHeight[anchorId]
          };

          // CRITICAL DEBUG: Log first few anchors to verify coordinates
          if (anchors.length < 3) {
            console.log(`🔧 SSD ANCHOR ${anchors.length + 1}: ${JSON.stringify({
              layer: layerId,
              position: { x, y },
              center: { xCenter: xCenter.toFixed(4), yCenter: yCenter.toFixed(4) },
              size: { width: newAnchor.width.toFixed(4), height: newAnchor.height.toFixed(4) },
              anchorType: anchorId < config.aspectRatios.length ? 'standard' : 'interpolated'
            })}`);
          }

          anchors.push(newAnchor);
        }
      }
    }

    layerId = lastSameStrideLayer;
  }

  console.log(`🔧 SSD ANCHORS: FIXED IMPLEMENTATION - Created ${anchors.length} anchors (expected: 2254 with interpolated)`);
  return anchors;
}

/**
 * Calculate scale for a given stride index (reference implementation)
 */
function calculateScale(minScale: number, maxScale: number,
                       strideIndex: number, numStrides: number): number {
  if (numStrides === 1) {
    return (minScale + maxScale) * 0.5;
  } else {
    return minScale + (maxScale - minScale) * strideIndex / (numStrides - 1);
  }
}
