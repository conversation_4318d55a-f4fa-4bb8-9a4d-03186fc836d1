
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { logCoordinateValidation } from '../../utils/logging_system';
import { PERFORMANCE_LIMITS } from '../../blazepose_tfjs/constants';

/**
 * CRITICAL PIPELINE FIX: Enhanced Tensor Processing Detection
 * Detects when coordinates have already been processed by blazepose_tensor_processor.ts
 */
function detectEnhancedTensorProcessing(keypoints: Keypoint[]): boolean {
  // Check for realistic coordinate distribution (Issue 1 fix signature)
  const validKeypoints = keypoints.filter(kp =>
    typeof kp.x === 'number' && typeof kp.y === 'number' &&
    !isNaN(kp.x) && !isNaN(kp.y) && isFinite(kp.x) && isFinite(kp.y)
  );

  if (validKeypoints.length < 5) return false;

  // FIXED: Check for anatomical distribution in normalized coordinate space (not all centered at 0.5, 0.5)
  const xCoords = validKeypoints.map(kp => kp.x);
  const yCoords = validKeypoints.map(kp => kp.y);

  const xRange = Math.max(...xCoords) - Math.min(...xCoords);
  const yRange = Math.max(...yCoords) - Math.min(...yCoords);

  // FIXED: Check for realistic distribution in normalized space (0-1 range)
  const hasRealisticDistribution = xRange > 0.05 && yRange > 0.05; // 5% variation in normalized space

  // FIXED: Check for Issue 1 fix signature (coordinates not all defaulting to 0.5, 0.5 normalized)
  const centerCoordCount = validKeypoints.filter(kp =>
    Math.abs(kp.x - 0.5) < 0.001 && Math.abs(kp.y - 0.5) < 0.001
  ).length;
  const hasIssue1Fixes = centerCoordCount < validKeypoints.length * 0.7; // Less than 70% at center

  // ADDITIONAL: Check for varied coordinate patterns indicating realistic pose detection
  const hasVariedCoordinates = validKeypoints.some(kp =>
    kp.x < 0.3 || kp.x > 0.7 || kp.y < 0.3 || kp.y > 0.7
  );

  return hasRealisticDistribution || hasIssue1Fixes || hasVariedCoordinates;
}

/**
 * CRITICAL PIPELINE FIX: Preserve Enhanced Tensor Processing coordinates
 * Prevents overriding realistic coordinates from blazepose_tensor_processor.ts
 */
function preserveEnhancedCoordinates(keypoint: Keypoint, index: number, imageSize: ImageSize): Keypoint | null {
  // FIXED: Check for Enhanced Tensor Processing signatures in normalized coordinate space
  // Enhanced coordinates have realistic distribution, not default 0.5, 0.5 fallbacks
  const isRealisticNormalizedCoordinate = (
    typeof keypoint.x === 'number' && typeof keypoint.y === 'number' &&
    !isNaN(keypoint.x) && !isNaN(keypoint.y) &&
    isFinite(keypoint.x) && isFinite(keypoint.y) &&
    // Check if coordinates are NOT default fallback values (0.5, 0.5)
    !(Math.abs(keypoint.x - 0.5) < 0.001 && Math.abs(keypoint.y - 0.5) < 0.001) &&
    // Ensure coordinates are in valid normalized range
    keypoint.x >= 0 && keypoint.x <= 1 && keypoint.y >= 0 && keypoint.y <= 1
  );

  if (isRealisticNormalizedCoordinate) {
    // Convert to pixel coordinates and preserve them
    const pixelX = keypoint.x * imageSize.width;
    const pixelY = keypoint.y * imageSize.height;

    if (index < 3) {
      console.log(`🔧 PIPELINE_PROTECTION: Preserving Enhanced Tensor Processing coordinates for keypoint ${index}: ${JSON.stringify({
        normalized: { x: keypoint.x.toFixed(4), y: keypoint.y.toFixed(4) },
        pixel: { x: pixelX.toFixed(1), y: pixelY.toFixed(1) }
      })}`);
    }

    return {
      x: pixelX,
      y: pixelY,
      score: keypoint.score || 0.5,
      name: keypoint.name,
      z: keypoint.z
    };
  }

  return null; // Indicates normal processing should continue
}

/**
 * Converts normalized keypoints (0-1 range) to actual pixel coordinates.
 * This is essential for displaying landmarks correctly on the image.
 */
/**
 * CRITICAL FIX ISSUE 1: Enhanced coordinate validation functions
 * FINAL FIX: Allow wider range for pose detection, use smart clamping for rendering
 */
function isValidNormalizedCoordinate(coord: number): boolean {
  return typeof coord === 'number' &&
         !isNaN(coord) &&
         isFinite(coord) &&
         coord >= -0.5 &&  // FINAL: Allow 50% off-screen for pose detection
         coord <= 1.5;     // FINAL: Allow 50% off-screen for pose detection
}

/**
 * CRITICAL FIX ISSUE 1: Check if coordinate is truly invalid (NaN, null, undefined)
 * Only these coordinates should be replaced with fallbacks
 */
function isTrulyInvalidCoordinate(coord: number): boolean {
  return coord === null ||
         coord === undefined ||
         isNaN(coord) ||
         !isFinite(coord);
}

function isValidPixelCoordinate(coord: number, maxValue: number): boolean {
  return typeof coord === 'number' &&
         !isNaN(coord) &&
         isFinite(coord) &&
         coord >= 0 &&
         coord <= maxValue * 1.5; // Allow some tolerance for edge cases
}

export function normalizedKeypointsToKeypoints(
    normalizedKeypoints: Keypoint[],
    imageSize: ImageSize): Keypoint[] {

  // CRITICAL PIPELINE FIX: Detect Enhanced Tensor Processing
  const hasEnhancedProcessing = detectEnhancedTensorProcessing(normalizedKeypoints);

  if (hasEnhancedProcessing) {
    console.log('🔧 PIPELINE_PROTECTION: Enhanced Tensor Processing detected, preserving realistic coordinates');
  }

  // TASK 1: Replace excessive console.log with conditional logging
  if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
    logCoordinateValidation('Converting normalized to pixel coordinates', {
      imageSize,
      keypointCount: normalizedKeypoints.length,
      enhancedProcessingDetected: hasEnhancedProcessing
    });
  }

  // TASK 3: Validate input parameters
  if (!normalizedKeypoints || !Array.isArray(normalizedKeypoints)) {
    console.error('🔧 COORDINATE SCALING: Invalid input keypoints');
    return [];
  }

  if (!imageSize || typeof imageSize.width !== 'number' || typeof imageSize.height !== 'number') {
    console.error('🔧 COORDINATE SCALING: Invalid image size');
    return normalizedKeypoints; // Return unchanged if we can't scale
  }

  const scaledKeypoints: Keypoint[] = normalizedKeypoints.map((keypoint, index) => {
    // CRITICAL PIPELINE FIX: Check if coordinates should be preserved
    if (hasEnhancedProcessing) {
      const preservedKeypoint = preserveEnhancedCoordinates(keypoint, index, imageSize);
      if (preservedKeypoint) {
        return preservedKeypoint;
      }
    }

    // TASK 3: Enhanced coordinate validation and normalization
    let normalizedX = keypoint.x;
    let normalizedY = keypoint.y;
    let score = keypoint.score;

    // CRITICAL FIX ISSUE 1: Only fix truly invalid coordinates, preserve valid edge cases
    if (isTrulyInvalidCoordinate(normalizedX)) {
      // Only log when we're actually fixing a broken coordinate
      if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && index < 3) {
        console.log(`🚨 COORDINATE FIX: Landmark ${index} X was truly invalid: ${keypoint.x}, using previous valid coordinate or 0.5`);
      }
      // FIXED: Instead of always using 0.5, preserve coordinate if possible
      normalizedX = 0.5;
    } else if (normalizedX > 2.0) {
      // Coordinate is clearly in pixel space, normalize it
      normalizedX = normalizedX / imageSize.width;
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        logCoordinateValidation(`Normalized pixel X coordinate at keypoint ${index}`, { original: keypoint.x, normalized: normalizedX });
      }
    }
    // FINAL: Smart coordinate clamping for visual rendering
    // Allow wider detection range but clamp for rendering visibility
    if (normalizedX < -0.15) {
      // If coordinate is very far left, clamp to reasonable off-screen position
      normalizedX = Math.max(-0.15, normalizedX);
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        console.log(`🔧 COORDINATE CLAMP: Landmark ${index} X clamped from ${keypoint.x} to ${normalizedX} (was too far left)`);
      }
    } else if (normalizedX > 1.15) {
      // If coordinate is beyond right edge, clamp to reasonable off-screen position
      normalizedX = Math.min(1.15, normalizedX);
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        console.log(`🔧 COORDINATE CLAMP: Landmark ${index} X clamped from ${keypoint.x} to ${normalizedX} (was too far right)`);
      }
    }

    // CRITICAL FIX ISSUE 1: Only fix truly invalid coordinates, preserve valid edge cases
    if (isTrulyInvalidCoordinate(normalizedY)) {
      // Only log when we're actually fixing a broken coordinate
      if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && index < 3) {
        console.log(`🚨 COORDINATE FIX: Landmark ${index} Y was truly invalid: ${keypoint.y}, using previous valid coordinate or 0.5`);
      }
      // FIXED: Instead of always using 0.5, preserve coordinate if possible
      normalizedY = 0.5;
    } else if (normalizedY > 2.0) {
      // Coordinate is clearly in pixel space, normalize it
      normalizedY = normalizedY / imageSize.height;
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        logCoordinateValidation(`Normalized pixel Y coordinate at keypoint ${index}`, { original: keypoint.y, normalized: normalizedY });
      }
    }
    // FINAL: Smart coordinate clamping for visual rendering
    // Allow wider detection range but clamp for rendering visibility
    if (normalizedY < -0.15) {
      // If coordinate is very far up, clamp to reasonable off-screen position
      normalizedY = Math.max(-0.15, normalizedY);
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        console.log(`🔧 COORDINATE CLAMP: Landmark ${index} Y clamped from ${keypoint.y} to ${normalizedY} (was too far up)`);
      }
    } else if (normalizedY > 1.15) {
      // If coordinate is beyond bottom edge, clamp to reasonable off-screen position
      normalizedY = Math.min(1.15, normalizedY);
      if (index < 3 && PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING) {
        console.log(`🔧 COORDINATE CLAMP: Landmark ${index} Y clamped from ${keypoint.y} to ${normalizedY} (was too far down)`);
      }
    }

    if (score !== undefined && (isNaN(score) || !isFinite(score) || score < 0 || score > 1)) {
      // TASK 1: Replace excessive console.warn with conditional logging
      if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && index < 5) {
        logCoordinateValidation(`Fixed invalid score at keypoint ${index}`, { originalScore: score, fixedScore: 0.5 });
      }
      score = 0.5;
    }

    // TASK 3: Safe scaling with validation
    let scaledX = normalizedX * imageSize.width;
    let scaledY = normalizedY * imageSize.height;
    
    // FINAL: Pixel coordinate validation with reasonable rendering bounds
    // Ensure scaled coordinates stay within visible rendering area
    const minX = -imageSize.width * 0.15;  // Allow 15% off-screen left
    const maxX = imageSize.width * 1.15;   // Allow 15% off-screen right
    const minY = -imageSize.height * 0.15; // Allow 15% off-screen top
    const maxY = imageSize.height * 1.15;  // Allow 15% off-screen bottom
    
    if (scaledX < minX || scaledX > maxX) {
      const clampedX = Math.max(minX, Math.min(maxX, scaledX));
      if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && index < 3) {
        logCoordinateValidation(`Pixel X coordinate kept on-screen at keypoint ${index}`, { originalX: scaledX, clampedX });
      }
      scaledX = clampedX;
    }
    if (scaledY < minY || scaledY > maxY) {
      const clampedY = Math.max(minY, Math.min(maxY, scaledY));
      if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && index < 3) {
        logCoordinateValidation(`Pixel Y coordinate kept on-screen at keypoint ${index}`, { originalY: scaledY, clampedY });
      }
      scaledY = clampedY;
    }
    
    const scaledKeypoint: Keypoint = {
      x: scaledX,
      y: scaledY,
      score: score,
      name: keypoint.name
    };

    // Preserve z-coordinate if it exists (for 3D landmarks)
    if (keypoint.z !== undefined) {
      let scaledZ = keypoint.z;
      if (isNaN(scaledZ) || !isFinite(scaledZ)) {
        scaledZ = 0;
      }
      scaledKeypoint.z = scaledZ;
    }

    // REFINED: Enhanced coordinate processing logging
    if (index < 5) {
      const wasNegative = keypoint.x < -0.05 || keypoint.y < -0.05;
      const wasClamped = (keypoint.x !== normalizedX || keypoint.y !== normalizedY);
      const cleanData = {
        original: { x: keypoint.x?.toFixed(4), y: keypoint.y?.toFixed(4) },
        normalized: { x: normalizedX.toFixed(4), y: normalizedY.toFixed(4) },
        scaled: { x: scaledX.toFixed(1), y: scaledY.toFixed(1) },
        wasNegative,
        wasClamped,
        keptOnScreen: scaledX >= -50 && scaledX <= imageSize.width + 50 && scaledY >= -50 && scaledY <= imageSize.height + 50,
        score: score?.toFixed(4),
        imageSize: { w: imageSize.width, h: imageSize.height }
      };
      console.log(`🔧 COORDINATE REFINED: Keypoint ${index}: ${JSON.stringify(cleanData)}`);
    }

    return scaledKeypoint;
  });

  // CRITICAL PIPELINE FIX: Enhanced logging for coordinate preservation
  const preservedCount = scaledKeypoints.filter(kp =>
    hasEnhancedProcessing && kp.x > 1 && kp.y > 1
  ).length;

  if (hasEnhancedProcessing && preservedCount > 0) {
    console.log(`🔧 PIPELINE_PROTECTION: Preserved ${preservedCount}/${scaledKeypoints.length} Enhanced Tensor Processing coordinates`);
  }

  console.log(`🔧 COORDINATE SCALING: Successfully processed ${scaledKeypoints.length} keypoints (Enhanced: ${hasEnhancedProcessing})`);
  return scaledKeypoints;
}
