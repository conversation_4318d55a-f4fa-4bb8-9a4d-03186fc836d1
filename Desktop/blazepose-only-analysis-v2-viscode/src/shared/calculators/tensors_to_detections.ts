
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Detection } from './interfaces/common_interfaces';
import { Anchor } from './create_ssd_anchors';
import { PERFORMANCE_LIMITS } from '../../blazepose_tfjs/constants';
import {
  PerformanceMonitor,
  isValidCoordinate,
  isValidDetection,
  processDetectionsWithLimits
} from '../../blazepose_tfjs/performance_monitor';
import { logger, logTensorOperation, logDetectionResult } from '../../utils/logging_system';
import { memoryManager, disposeTensor, registerTensor } from '../../utils/memory_manager';
import { detectionOptimizer, optimizeDetections } from '../../utils/detection_optimizer';

/**
 * Extracts and transforms keypoints from tensor data using the reference TensorFlow.js method
 */
function extractKeypointsFromTensor(
    tensorData: Float32Array, 
    baseIndex: number, 
    config: TensorsToDetectionsConfig,
    anchor: any): Array<{x: number, y: number}> {
  
  const keypoints: Array<{x: number, y: number}> = [];
  
  // Get transformation scales (same as used for bounding box)
  const xScale = config.xScale || 224.0;
  const yScale = config.yScale || 224.0;
  
  // Extract and transform keypoints from tensor following reference implementation
  for (let k = 0; k < config.numKeypoints; k++) {
    const keypointOffset = config.keypointCoordOffset + k * config.numValuesPerKeypoint;
    const keypointIndex = baseIndex + keypointOffset;
    
    // Bounds check
    if (keypointIndex + 1 >= tensorData.length) {
      console.warn(`🔧 KEYPOINT EXTRACTION: Index out of bounds for keypoint ${k}`);
      break;
    }
    
    // Extract raw keypoint coordinates
    const rawX = tensorData[keypointIndex + 0];
    const rawY = tensorData[keypointIndex + 1];
    
    // CRITICAL FIX: Apply same coordinate transformation as bounding box
    // Reference formula: (raw / scale) * anchor.size + anchor.center
    const transformedX = (rawX / xScale) * anchor.width + anchor.xCenter;
    const transformedY = (rawY / yScale) * anchor.height + anchor.yCenter;
    
    const keypoint = {
      x: transformedX,
      y: transformedY
    };
    
    // Log first few keypoints for debugging
    if (k < 2) {
      console.log(`🔧 KEYPOINT EXTRACTION: Keypoint ${k} (tensor indices ${keypointIndex}-${keypointIndex+1}):`, {
        rawX: rawX.toFixed(4),
        rawY: rawY.toFixed(4),
        transformedX: transformedX.toFixed(4),
        transformedY: transformedY.toFixed(4),
        expectedType: k === 0 ? 'nose' : k === 1 ? 'left_eye_inner' : `keypoint_${k}`,
        anchorInfo: {
          xCenter: anchor.xCenter.toFixed(4),
          yCenter: anchor.yCenter.toFixed(4),
          width: anchor.width.toFixed(4),
          height: anchor.height.toFixed(4)
        }
      });
    }
    
    keypoints.push(keypoint);
  }
  
  return keypoints;
}

export interface TensorsToDetectionsConfig {
  numClasses: number;
  numBoxes: number;
  numCoords: number;
  boxCoordOffset: number;
  keypointCoordOffset: number;
  numKeypoints: number;
  numValuesPerKeypoint: number;
  sigmoidScore: boolean;
  scoreClippingThresh: number;
  reverseOutputOrder: boolean;
  xScale: number;
  yScale: number;
  wScale: number;
  hScale: number;
  minScoreThresh: number;
}

/**
 * PHASE 1: Converts raw detection tensors to detection objects.
 * TASK 1: Enhanced with performance monitoring and detection limiting
 */
export async function tensorsToDetections(
    detectionTensor: tf.Tensor,
    anchors: Anchor[],
    config: TensorsToDetectionsConfig): Promise<Detection[]> {

  // TASK 1: Initialize performance monitoring
  const monitor = new PerformanceMonitor();
  monitor.startFrame();

  // TASK 3: Optimized logging system
  logTensorOperation('Starting tensor to detections conversion');

  const detections: Detection[] = [];

  if (!detectionTensor || anchors.length === 0) {
    logger.warn('tensorProcessing', 'No tensor or anchors provided for detection conversion');
    return detections;
  }

  // Validate tensor dimensions
  if (detectionTensor.shape.length !== 3) {
    console.error('🔧 TENSORS TO DETECTIONS: Expected 3D tensor, got shape:', detectionTensor.shape);
    return detections;
  }

  const [batchSize, numBoxes, numCoords] = detectionTensor.shape;
  // Tensor dimensions logged only if needed

  // TASK 1: BlazePose-specific tensor shape validation
  if (batchSize !== 1) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects batch size 1, got:', batchSize);
    return detections;
  }

  if (numCoords !== 13) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects 13 coordinates per box, got:', numCoords);
    console.error('🔧 TENSORS TO DETECTIONS: Expected format: [xCenter, yCenter, height, width, score, ...8 additional values]');
    return detections;
  }

  // Validation passed

  try {
    // TASK 5: Comprehensive tensor debugging starts here
    const startTime = performance.now();
    // Starting tensor data extraction
    
    // Tensor metadata logging disabled for performance
    
    // Get tensor data as Float32Array for proper indexing
    const tensorData = await detectionTensor.data();
    const extractionTime = performance.now() - startTime;
    // Extraction completed
    
    // Validate tensor data extraction
    if (!tensorData || tensorData.length === 0) {
      console.error('🔧 TENSORS TO DETECTIONS: Tensor data extraction failed - empty or null data');
      return detections;
    }
    
    const expectedDataLength = batchSize * numBoxes * numCoords;
    if (tensorData.length !== expectedDataLength) {
      console.error(`🔧 TENSORS TO DETECTIONS: Tensor data length mismatch. Expected: ${expectedDataLength}, Got: ${tensorData.length}`);
      return detections;
    }
    
    // Tensor data extracted successfully
    
    // TASK 5: Statistical analysis of tensor data
    const tensorDataArray = Array.from(tensorData);
    const tensorStats = {
      min: Math.min(...tensorDataArray),
      max: Math.max(...tensorDataArray),
      mean: tensorDataArray.reduce((sum, val) => sum + val, 0) / tensorDataArray.length,
      nonZeroCount: tensorDataArray.filter(v => v !== 0).length,
      infiniteCount: tensorDataArray.filter(v => !isFinite(v)).length
    };
    
    // Tensor statistics disabled for performance
    
    // TASK 1: Calculate number of detections to process with performance limits
    const maxDetections = Math.min(numBoxes, anchors.length, PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME);
    const numDetections = maxDetections;

    // TASK 3: Optimized performance limiting logging
    if (numBoxes > PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME) {
      logger.warn('tensorProcessing', 'Detection count limited for performance', {
        original: numBoxes,
        limited: PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME
      });
    }
    
    // TASK 5: Log anchor information summary
    const anchorStats = {
      total: anchors.length,
      validAnchors: 0,
      avgWidth: 0,
      avgHeight: 0,
      minWidth: Number.MAX_VALUE,
      maxWidth: Number.MIN_VALUE,
      minHeight: Number.MAX_VALUE,
      maxHeight: Number.MIN_VALUE
    };
    
    let validAnchorSum = { width: 0, height: 0 };
    anchors.slice(0, numDetections).forEach((anchor, i) => {
      if (anchor && typeof anchor.width === 'number' && typeof anchor.height === 'number' && 
          anchor.width > 0 && anchor.height > 0) {
        anchorStats.validAnchors++;
        validAnchorSum.width += anchor.width;
        validAnchorSum.height += anchor.height;
        anchorStats.minWidth = Math.min(anchorStats.minWidth, anchor.width);
        anchorStats.maxWidth = Math.max(anchorStats.maxWidth, anchor.width);
        anchorStats.minHeight = Math.min(anchorStats.minHeight, anchor.height);
        anchorStats.maxHeight = Math.max(anchorStats.maxHeight, anchor.height);
      }
    });
    
    if (anchorStats.validAnchors > 0) {
      anchorStats.avgWidth = validAnchorSum.width / anchorStats.validAnchors;
      anchorStats.avgHeight = validAnchorSum.height / anchorStats.validAnchors;
    }
    
    // Anchor statistics and sample data logging disabled for performance

    // TASK 1: Process detections with performance monitoring and limits
    let consecutiveInvalidDetections = 0;
    let detectionsProcessed = 0;
    let detectionsSkipped = 0;

    for (let i = 0; i < numDetections; i++) {
      // TASK 1: Check performance limits
      if (monitor.shouldLimitProcessing()) {
        detectionsSkipped = numDetections - i;
        console.warn(`🔧 PERFORMANCE LIMIT: Stopping processing at detection ${i} due to time limit`);
        break;
      }

      // TASK 1: Early termination for consecutive invalid detections
      if (consecutiveInvalidDetections >= PERFORMANCE_LIMITS.MAX_CONSECUTIVE_INVALID_DETECTIONS) {
        detectionsSkipped = numDetections - i;
        console.warn(`🔧 PERFORMANCE LIMIT: Stopping processing due to ${consecutiveInvalidDetections} consecutive invalid detections`);
        break;
      }

      const anchor = anchors[i];

      // TASK 3: Validate anchor data integrity
      if (!anchor || typeof anchor.xCenter !== 'number' || typeof anchor.yCenter !== 'number' ||
          typeof anchor.width !== 'number' || typeof anchor.height !== 'number') {
        monitor.recordInvalidDetection();
        consecutiveInvalidDetections++;
        if (PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING) {
          console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at index ${i}:`, anchor);
        }
        continue;
      }

      if (anchor.width <= 0 || anchor.height <= 0) {
        monitor.recordInvalidDetection();
        consecutiveInvalidDetections++;
        if (PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING) {
          console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor dimensions at index ${i}: width=${anchor.width}, height=${anchor.height}`);
        }
        continue;
      }
      
      // Correct 3D tensor indexing: batch=0, detection=i, values=0-12
      // Formula: tensorData[batch * numBoxes * numCoords + detection * numCoords + valueIndex]
      const baseIndex = 0 * numBoxes * numCoords + i * numCoords;
      
      // TASK 4: Bounds checking for tensor access
      if (baseIndex < 0 || baseIndex + 12 >= tensorData.length) {
        console.error(`🔧 TENSORS TO DETECTIONS: Tensor access out of bounds at detection ${i}. BaseIndex: ${baseIndex}, TensorLength: ${tensorData.length}`);
        continue;
      }
      
      // Extract box coordinates (first 4 values)
      // CRITICAL FIX: BlazePose format is [xCenter, yCenter, height, width, score, ...]
      const xCenter = tensorData[baseIndex + 0];  // FIXED: X comes first in tensor
      const yCenter = tensorData[baseIndex + 1];  // FIXED: Y comes second in tensor 
      const h = tensorData[baseIndex + 2];
      const w = tensorData[baseIndex + 3];
      
      // PIPELINE TRACE STEP 3: Log raw coordinates before anchor transformation
      if (i < 3) { // Log first 3 detections for verification
        console.log(`🔍 PIPELINE TRACE STEP 3: Detection ${i} raw coordinates before anchor transform:`, {
          xCenter: xCenter.toFixed(6),
          yCenter: yCenter.toFixed(6),
          height: h.toFixed(6),
          width: w.toFixed(6),
          baseIndex: baseIndex,
          anchorInfo: {
            xCenter: anchor.xCenter.toFixed(6),
            yCenter: anchor.yCenter.toFixed(6),
            width: anchor.width.toFixed(6),
            height: anchor.height.toFixed(6)
          }
        });
      }

      // Detailed detection logging disabled for performance

      // TASK 1: Enhanced coordinate validation with performance monitoring
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w) ||
          !isValidCoordinate(yCenter) || !isValidCoordinate(xCenter) ||
          !isValidCoordinate(h) || !isValidCoordinate(w)) {
        monitor.recordInvalidDetection();
        consecutiveInvalidDetections++;
        // TASK 3: Reduced coordinate logging - only sample
        if (PERFORMANCE_LIMITS.ENABLE_COORDINATE_LOGGING && Math.random() < PERFORMANCE_LIMITS.LOG_SAMPLE_RATE) {
          logger.warn('coordinates', 'Invalid detection coordinates', {
            index: i,
            yCenter,
            xCenter,
            h,
            w
          });
        }
        continue;
      }
      
      // Extract and process score (5th value, index 4)
      let score = tensorData[baseIndex + 4];
      if (config.sigmoidScore) {
        score = 1.0 / (1.0 + Math.exp(-score));
      }

      // Validate score
      if (!isFinite(score) || score < 0 || score > 1) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid score at detection ${i}: ${score}`);
        continue;
      }

      // Apply score threshold early
      if (score < config.minScoreThresh) {
        continue;
      }

      // TASK 3: FIXED - BlazePose coordinate transformation with proper scaling
      // BlazePose outputs normalized coordinates that need proper scaling
      
      // Validate input values first
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w)) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid raw coordinates at detection ${i}:`, {
          yCenter, xCenter, h, w
        });
        continue;
      }
      
      // Validate anchor values
      if (!isFinite(anchor.xCenter) || !isFinite(anchor.yCenter) || 
          !isFinite(anchor.width) || !isFinite(anchor.height) ||
          anchor.width <= 0 || anchor.height <= 0) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at detection ${i}:`, anchor);
        continue;
      }
      
      // FIXED: Use correct BlazePose coordinate transformation without scaling issues
      // BlazePose coordinates are already normalized relative to the input image
      let anchorCenterX, anchorCenterY, boxWidth, boxHeight;
      
      try {
        // TASK 3: Optimized coordinate transformation with minimal logging
        if (PERFORMANCE_LIMITS.ENABLE_VERBOSE_LOGGING && i < 3) { // Only log first 3 detections
          logTensorOperation('Coordinate transformation', {
            index: i,
            rawValues: { yCenter, xCenter, h, w },
            anchor: { xCenter: anchor.xCenter, yCenter: anchor.yCenter }
          });
        }

        // CRITICAL FIX: Use reference TensorFlow.js formula with division by scale
        // Reference implementation divides by scale (224.0) and multiplies by anchor size
        const xScale = config.xScale || 224.0; // Default to 224.0 like reference
        const yScale = config.yScale || 224.0; // Default to 224.0 like reference
        const wScale = config.wScale || 224.0;
        const hScale = config.hScale || 224.0;

        // Apply transformation using reference formula: (raw / scale) * anchor.size + anchor.center
        const rawAnchorX = (xCenter / xScale) * anchor.width + anchor.xCenter;
        const rawAnchorY = (yCenter / yScale) * anchor.height + anchor.yCenter;
        
        // PIPELINE TRACE STEP 4: Log transformed coordinates after anchor application
        if (i < 3) { // Log first 3 detections for verification
          console.log(`🔍 PIPELINE TRACE STEP 4: Detection ${i} after anchor transformation:`, {
            rawInputs: {
              xCenter: xCenter.toFixed(6),
              yCenter: yCenter.toFixed(6),
              width: w.toFixed(6),
              height: h.toFixed(6)
            },
            scales: {
              xScale: xScale,
              yScale: yScale
            },
            anchor: {
              xCenter: anchor.xCenter.toFixed(6),
              yCenter: anchor.yCenter.toFixed(6),
              width: anchor.width.toFixed(6),
              height: anchor.height.toFixed(6)
            },
            transformedResult: {
              rawAnchorX: rawAnchorX.toFixed(6),
              rawAnchorY: rawAnchorY.toFixed(6)
            },
            formula: '(raw / scale) * anchor.size + anchor.center'
          });
        }
        
        // Allow reasonable coordinate range for pose detection at frame edges
        // With coordinate order fix, these should be much more reasonable
        anchorCenterX = rawAnchorX;
        anchorCenterY = rawAnchorY;
        
        // Log coordinate clamping for debugging
        if (rawAnchorX !== anchorCenterX || rawAnchorY !== anchorCenterY) {
          console.log(`🔧 COORDINATE_CLAMP: Detection ${i} - Raw: (${rawAnchorX.toFixed(4)}, ${rawAnchorY.toFixed(4)}) → Clamped: (${anchorCenterX.toFixed(4)}, ${anchorCenterY.toFixed(4)})`);
        }
        
        // Use reference formula for size: (raw / scale) * anchor.size
        boxWidth = Math.max(0.001, (w / wScale) * anchor.width);
        boxHeight = Math.max(0.001, (h / hScale) * anchor.height);
        
        // Additional safety checks
        if (!isFinite(anchorCenterX)) anchorCenterX = anchor.xCenter;
        if (!isFinite(anchorCenterY)) anchorCenterY = anchor.yCenter;
        if (!isFinite(boxWidth) || boxWidth <= 0) boxWidth = anchor.width * 0.1;
        if (!isFinite(boxHeight) || boxHeight <= 0) boxHeight = anchor.height * 0.1;
        
      } catch (transformError) {
        logger.warn('tensorProcessing', 'Coordinate transformation error', {
          index: i,
          error: transformError.message
        });
        // Use safe fallback values
        anchorCenterX = anchor.xCenter;
        anchorCenterY = anchor.yCenter;
        boxWidth = anchor.width * 0.1;
        boxHeight = anchor.height * 0.1;
      }

      // REASONABLE validation bounds for corrected coordinate scaling
      // Allow reasonable off-screen detection but prevent extreme outliers
      if (!isFinite(anchorCenterX) || !isFinite(anchorCenterY) ||
          !isFinite(boxWidth) || !isFinite(boxHeight) ||
          boxWidth <= 0 || boxHeight <= 0 ||
          Math.abs(anchorCenterX) > 2.0 || Math.abs(anchorCenterY) > 2.0 ||
          boxWidth > 2.0 || boxHeight > 2.0) {
        // Only log extreme rejections to reduce bandwidth
        if (Math.abs(anchorCenterY) > 10000 || boxWidth > 20 || boxHeight > 20) {
          console.warn(`🔧 REJECT DETECTION ${i}: Extreme bounds`, {
            anchorCenterX: anchorCenterX?.toFixed(2) || 'NaN',
            anchorCenterY: anchorCenterY?.toFixed(2) || 'NaN',
            boxWidth: boxWidth?.toFixed(2) || 'NaN',
            boxHeight: boxHeight?.toFixed(2) || 'NaN'
          });
        }
        continue;
      }

      // Calculate bounding box corners
      const xMin = anchorCenterX - boxWidth / 2;
      const yMin = anchorCenterY - boxHeight / 2;
      const xMax = anchorCenterX + boxWidth / 2;
      const yMax = anchorCenterY + boxHeight / 2;

      // Ensure coordinates are normalized (0-1 range) and positive
      const normalizedXMin = Math.max(0, Math.min(1, xMin));
      const normalizedYMin = Math.max(0, Math.min(1, yMin));
      const normalizedXMax = Math.max(0, Math.min(1, xMax));
      const normalizedYMax = Math.max(0, Math.min(1, yMax));
      const normalizedWidth = normalizedXMax - normalizedXMin;
      const normalizedHeight = normalizedYMax - normalizedYMin;

      // SAFE detection object creation with validation
      // Create relativeBoundingBox for removeDetectionLetterbox compatibility
      const relativeBoundingBoxForLetterbox = {
        xMin: Math.max(0, Math.min(1, normalizedXMin)),
        yMin: Math.max(0, Math.min(1, normalizedYMin)),
        xMax: Math.max(0, Math.min(1, normalizedXMax)),
        yMax: Math.max(0, Math.min(1, normalizedYMax)),
        width: Math.max(0.001, Math.min(1, normalizedWidth)),
        height: Math.max(0.001, Math.min(1, normalizedHeight))
      };

      const detection: Detection = {
        boundingBox: {
          xMin: Math.max(0, Math.min(1, normalizedXMin)),
          yMin: Math.max(0, Math.min(1, normalizedYMin)),
          xMax: Math.max(0, Math.min(1, normalizedXMax)),
          yMax: Math.max(0, Math.min(1, normalizedYMax)),
          width: Math.max(0.001, Math.min(1, normalizedWidth)),
          height: Math.max(0.001, Math.min(1, normalizedHeight))
        },
        locationData: {
          format: 'RELATIVE_BOUNDING_BOX',
          relativeBoundingBox: relativeBoundingBoxForLetterbox,
          // FIXED: Extract actual keypoints from tensor data with coordinate transformation
          relativeKeypoints: extractKeypointsFromTensor(tensorData, baseIndex, config, anchor)
        },
        relativeBoundingBox: {
          xCenter: Math.max(-50, Math.min(50, anchorCenterX)),
          yCenter: Math.max(-5000, Math.min(5000, anchorCenterY)),
          width: Math.max(0.001, Math.min(10, boxWidth)),
          height: Math.max(0.001, Math.min(10, boxHeight)),
          rotation: 0
        },
        score: Math.max(0, Math.min(1, score))
      };
      
      // PIPELINE TRACE STEP 5: Log final detection with face keypoints
      if (i < 3) { // Log first 3 detections for verification
        const keypoints = detection.locationData?.relativeKeypoints;
        console.log(`🔍 PIPELINE TRACE STEP 5: Final Detection ${i} with face keypoints:`, {
          boundingBox: {
            xMin: detection.boundingBox.xMin.toFixed(6),
            yMin: detection.boundingBox.yMin.toFixed(6),
            xMax: detection.boundingBox.xMax.toFixed(6),
            yMax: detection.boundingBox.yMax.toFixed(6)
          },
          relativeBoundingBox: {
            xCenter: detection.relativeBoundingBox.xCenter.toFixed(6),
            yCenter: detection.relativeBoundingBox.yCenter.toFixed(6),
            width: detection.relativeBoundingBox.width.toFixed(6),
            height: detection.relativeBoundingBox.height.toFixed(6)
          },
          faceKeypoints: keypoints ? {
            nose: keypoints[0] ? `(${keypoints[0].x?.toFixed(6)}, ${keypoints[0].y?.toFixed(6)})` : 'undefined',
            leftEyeInner: keypoints[1] ? `(${keypoints[1].x?.toFixed(6)}, ${keypoints[1].y?.toFixed(6)})` : 'undefined',
            leftEye: keypoints[2] ? `(${keypoints[2].x?.toFixed(6)}, ${keypoints[2].y?.toFixed(6)})` : 'undefined',
            leftEyeOuter: keypoints[3] ? `(${keypoints[3].x?.toFixed(6)}, ${keypoints[3].y?.toFixed(6)})` : 'undefined'
          } : 'no keypoints',
          score: detection.score.toFixed(6)
        });
      }
      
      // TASK 3: Optimized detection creation logging
      if (PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING && i === 0) {
        logDetectionResult('Detection object created successfully', {
          hasLocationData: !!detection.locationData,
          hasRelativeKeypoints: !!(detection.locationData && detection.locationData.relativeKeypoints),
          relativeKeypointsLength: detection.locationData?.relativeKeypoints?.length
        });
      }

      // TASK 1: Record successful detection processing
      monitor.recordDetectionProcessed();
      consecutiveInvalidDetections = 0; // Reset consecutive invalid counter
      detectionsProcessed++;

      // TASK 3: Minimal detection logging with sampling
      if (PERFORMANCE_LIMITS.ENABLE_DETECTION_LOGGING && i < 3) {
        logDetectionResult('Detection created', {
          index: i,
          score: detection.score.toFixed(3),
          bbox: detection.boundingBox
        });
      }

      detections.push(detection);
    }

    // TASK 3: Apply detection optimization if enabled
    let finalDetections = detections;
    if (PERFORMANCE_LIMITS.ENABLE_DETECTION_OPTIMIZATION) {
      const optimizationResult = optimizeDetections(detections);
      finalDetections = optimizationResult.data;

      if (optimizationResult.optimizations.length > 0) {
        logDetectionResult('Detection optimization applied', {
          originalCount: optimizationResult.originalCount,
          optimizedCount: optimizationResult.filteredCount,
          optimizations: optimizationResult.optimizations.join(', ')
        });
      }
    }

    // TASK 3: Enhanced processing summary with optimized logging
    const totalProcessingTime = monitor.endFrame();
    const performanceMetrics = monitor.getMetrics();

    // TASK 3: Only log summary periodically to reduce log volume
    const shouldLogSummary = PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING &&
                            (detectionsProcessed % PERFORMANCE_LIMITS.PERFORMANCE_LOG_INTERVAL === 0);

    if (shouldLogSummary) {
      logDetectionResult('Tensor processing summary', {
        totalTime: `${totalProcessingTime.toFixed(2)}ms`,
        detectionsProcessed: detectionsProcessed,
        validDetections: finalDetections.length,
        successRate: detectionsProcessed > 0 ? `${(finalDetections.length / detectionsProcessed * 100).toFixed(1)}%` : '0%',
        wasOptimized: finalDetections.length !== detections.length
      });
    }

    // TASK 3: Memory cleanup
    disposeTensor(detectionTensor);

    return finalDetections;

  } catch (error) {
    logger.error('tensorProcessing', 'Tensor conversion failed', {
      error: error.message,
      tensorShape: detectionTensor?.shape,
      configNumBoxes: config.numBoxes
    });
    return [];
  }
}
