
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';

/**
 * Configuration for tensors to landmarks conversion
 */
export interface TensorsToLandmarksConfig {
  numLandmarks: number;
  inputImageWidth: number;
  inputImageHeight: number;
  normalizeCoordinates: boolean;
  flipHorizontally?: boolean;
  flipVertically?: boolean;
}

/**
 * Converts pose landmark tensors to keypoints array.
 * This is the critical missing function from BlazePose pipeline.
 */
export async function tensorsToLandmarks(
    landmarkTensor: tf.Tensor,
    config: TensorsToLandmarksConfig): Promise<Keypoint[]> {
  
  // Removed excessive debug logging for production performance

  const landmarkTensor2d = landmarkTensor.squeeze() as tf.Tensor2D;

  // Extract coordinates - BlazePose outputs [x, y, z, visibility, presence] per landmark
  const numLandmarks = config.numLandmarks;
  const landmarks: Keypoint[] = [];

  // CRITICAL FIX: Use reference implementation approach - flat tensor data access
  const numValues = landmarkTensor2d.size;
  const numDimensions = Math.floor(numValues / numLandmarks);

  // CRITICAL DEBUG: Log tensor processing details
  console.log('🚨 TENSOR EXTRACTION DEBUG (Reference Method):', JSON.stringify({
    tensorSize: numValues,
    numLandmarks,
    numDimensions,
    expectedTotalElements: numLandmarks * numDimensions
  }));

  // Validate dimensions
  if (isNaN(numDimensions) || numDimensions < 3) {
    console.error('🔧 TENSORS_TO_LANDMARKS: Invalid dimensions calculation:', {
      numValues,
      numLandmarks,
      numDimensions
    });
    return [];
  }

  // REFERENCE IMPLEMENTATION: Use flat tensor data access (not reshape)
  const rawLandmarks = await landmarkTensor2d.data();

  // CRITICAL DEBUG: Log first few raw tensor values to identify clustering issue
  console.log('🚨 RAW TENSOR DATA (flat array, first 15 values):', JSON.stringify({
    first15Values: Array.from(rawLandmarks.slice(0, 15)),
    totalLength: rawLandmarks.length,
    expectedLength: numLandmarks * numDimensions
  }));

  // REFERENCE IMPLEMENTATION: Process landmarks using offset-based indexing
  for (let ld = 0; ld < numLandmarks; ld++) {
    const offset = ld * numDimensions;

    // REFERENCE IMPLEMENTATION: Extract coordinates using flat array indexing
    let x = rawLandmarks[offset];
    let y = numDimensions > 1 ? rawLandmarks[offset + 1] : 0;
    const z = numDimensions > 2 ? rawLandmarks[offset + 2] : 0;

    // CRITICAL DEBUG: Log raw coordinates for first few landmarks to identify clustering
    if (ld < 5) {
      console.log(`🚨 LANDMARK ${ld} RAW COORDS (Reference Method):`, JSON.stringify({
        offset,
        rawX: x,
        rawY: y,
        rawZ: z,
        rawValues: Array.from(rawLandmarks.slice(offset, offset + Math.min(5, numDimensions)))
      }));
    }

    // REFERENCE IMPLEMENTATION: Extract visibility and presence using offset indexing
    let visibility = 0.5; // Fallback
    if (numDimensions > 3 && typeof rawLandmarks[offset + 3] === 'number' && !isNaN(rawLandmarks[offset + 3])) {
      // Apply sigmoid activation to raw visibility (reference implementation)
      visibility = 1.0 / (1.0 + Math.exp(-rawLandmarks[offset + 3]));
    }

    let presence = 0.5; // Fallback
    if (numDimensions > 4 && typeof rawLandmarks[offset + 4] === 'number' && !isNaN(rawLandmarks[offset + 4])) {
      // Apply sigmoid activation to raw presence
      presence = 1.0 / (1.0 + Math.exp(-rawLandmarks[offset + 4]));
    }
    
    // CRITICAL FIX: Intelligent coordinate system detection
    // BlazePose can output two different types of tensors:
    // 1. Detection tensors with pixel coordinates (>1.0) that need normalization
    // 2. Landmark tensors with normalized coordinates (<1.0) that are already normalized

    const isPixelCoordinates = Math.abs(x) > 1.0 || Math.abs(y) > 1.0;

    if (isPixelCoordinates) {
      // Type 1: Pixel coordinates - normalize to [0,1] range
      x = x / config.inputImageWidth;
      y = y / config.inputImageHeight;

      if (ld < 3) {
        console.log(`🔧 COORDINATE SYSTEM: Landmark ${ld} - PIXEL coordinates normalized`, JSON.stringify({
          originalX: rawLandmarks[offset],
          originalY: rawLandmarks[offset + 1],
          normalizedX: x,
          normalizedY: y
        }));
      }
    } else {
      // Type 2: Already normalized coordinates - use as-is
      if (ld < 3) {
        console.log(`🔧 COORDINATE SYSTEM: Landmark ${ld} - NORMALIZED coordinates preserved`, JSON.stringify({
          preservedX: x,
          preservedY: y
        }));
      }
    }
    
    // Apply flipping if configured
    if (config.flipHorizontally) {
      x = 1.0 - x;
    }
    if (config.flipVertically) {
      y = 1.0 - y;
    }
    
    // Validate coordinates (silent fix for performance)
    if (isNaN(x) || isNaN(y)) {
      x = isNaN(x) ? 0.5 : x;
      y = isNaN(y) ? 0.5 : y;
    }
    
    // Use visibility as the primary score (reference implementation approach)
    const finalScore = visibility;
    
    // DEBUG: Log first few landmarks with actual scores
    if (ld < 3) {
      console.log(`🔧 LANDMARK_SCORE_DEBUG ${ld}:`, {
        rawVisibility: numDimensions > 3 ? rawLandmarks[offset + 3]?.toFixed(4) : 'missing',
        rawPresence: numDimensions > 4 ? rawLandmarks[offset + 4]?.toFixed(4) : 'missing',
        processedVisibility: visibility.toFixed(4),
        processedPresence: presence.toFixed(4),
        finalScore: finalScore.toFixed(4),
        numDimensions: numDimensions
      });
    }
    
    landmarks.push({
      x: Math.max(0, Math.min(1, x)),
      y: Math.max(0, Math.min(1, y)),
      z: z,
      score: finalScore,
      name: `landmark_${ld}`
    });
  }

  // Clean up tensor (no reshaping was done, so clean up the original)
  landmarkTensor2d.dispose();

  // Only log on successful conversion with landmarks
  if (landmarks.length > 0) {
    console.log('✅ TENSORS_TO_LANDMARKS: Converted', landmarks.length, 'landmarks');
  }
  return landmarks;
}

/**
 * Converts world landmark tensors to 3D keypoints.
 */
export async function tensorsToWorldLandmarks(
    worldLandmarkTensor: tf.Tensor,
    config: Omit<TensorsToLandmarksConfig, 'normalizeCoordinates'>): Promise<Keypoint[]> {
  
  console.log('🔧 TENSORS_TO_WORLD_LANDMARKS: Converting tensor to world landmarks');
  console.log('🔧 TENSORS_TO_WORLD_LANDMARKS: Tensor shape:', worldLandmarkTensor.shape);

  const worldTensor2d = worldLandmarkTensor.squeeze() as tf.Tensor2D;
  
  const data: number[][] = await worldTensor2d.array();
  const worldLandmarks: Keypoint[] = [];
  
  for (let i = 0; i < Math.min(config.numLandmarks, data.length); i++) {
    const landmarkData = data[i];
    
    if (!landmarkData || landmarkData.length < 3) {
      continue;
    }
    
    // CRITICAL FIX: Don't use || 0 as it treats valid 0 coordinates as falsy
    // Only use 0 fallback for truly invalid values (null, undefined, NaN)
    let x = (landmarkData[0] !== null && landmarkData[0] !== undefined && !isNaN(landmarkData[0])) ? landmarkData[0] : 0;
    let y = (landmarkData[1] !== null && landmarkData[1] !== undefined && !isNaN(landmarkData[1])) ? landmarkData[1] : 0;
    let z = (landmarkData[2] !== null && landmarkData[2] !== undefined && !isNaN(landmarkData[2])) ? landmarkData[2] : 0;
    
    // CRITICAL FIX: BlazePose outputs 5 dimensions per landmark: [x, y, z, visibility, presence]
    // Extract visibility score (4th dimension) and apply sigmoid activation
    let score = 0.5; // Fallback for malformed data
    if (landmarkData.length >= 4) {
      const rawVisibility = landmarkData[3];
      if (typeof rawVisibility === 'number' && !isNaN(rawVisibility)) {
        // Apply sigmoid activation to visibility score (reference implementation)
        score = 1.0 / (1.0 + Math.exp(-rawVisibility));
      }
    }
    
    // DEBUG: Log first few world landmarks with actual scores
    if (i < 3) {
      console.log(`🔧 WORLD_TENSOR_DEBUG ${i}:`, {
        rawData: `[${landmarkData.slice(0, 5).join(', ')}]`,
        parsed: { x: x.toFixed(4), y: y.toFixed(4), z: z.toFixed(4) },
        visibility: landmarkData[3]?.toFixed(4) || 'missing',
        score: score.toFixed(4),
        dataLength: landmarkData.length
      });
    }
    
    // Validate world coordinates
    if (isNaN(x)) x = 0;
    if (isNaN(y)) y = 0;
    if (isNaN(z)) z = 0;
    
    worldLandmarks.push({
      x: x,
      y: y,
      z: z,
      score: score,
      name: `world_landmark_${i}`
    });
  }
  
  console.log('✅ TENSORS_TO_WORLD_LANDMARKS: Converted', worldLandmarks.length, 'world landmarks');
  return worldLandmarks;
}
