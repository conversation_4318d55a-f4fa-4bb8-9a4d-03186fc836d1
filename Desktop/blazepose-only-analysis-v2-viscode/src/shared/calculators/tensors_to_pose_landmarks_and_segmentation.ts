/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';
import { tensorsToLandmarks, tensorsToWorldLandmarks } from './tensors_to_landmarks';
import { tensorsToSegmentation } from './tensors_to_segmentation';
import { refineLandmarksFromHeatmap } from './refine_landmarks_from_heatmap';
import * as constants from '../../blazepose_tfjs/constants';

/**
 * Result from tensor to pose landmarks and segmentation conversion
 */
export interface TensorsToPoseLandmarksAndSegmentationResult {
  landmarks: Keypoint[];
  auxiliaryLandmarks: Keypoint[];
  worldLandmarks: Keypoint[];
  poseScore: number;
  segmentationMask: tf.Tensor2D | null;
}

/**
 * Enhanced tensor processing pipeline for BlazePose landmarks and segmentation.
 * Phase 4: Complete implementation with proper coordinate scaling and projection.
 */
export async function tensorsToPoseLandmarksAndSegmentation(
    tensors: tf.Tensor[],
    enableSegmentation: boolean = false): Promise<TensorsToPoseLandmarksAndSegmentationResult | null> {
  
  console.log('🔧 PHASE 4 TENSOR PROCESSING: Converting tensors to pose landmarks and segmentation');
  console.log('🔧 PHASE 4 TENSOR PROCESSING: Input tensor count:', tensors.length);
  console.log('🔧 PHASE 4 TENSOR PROCESSING: Segmentation enabled:', enableSegmentation);

  try {
    // Extract tensors based on expected BlazePose output format
    const landmarkTensor = tensors[0] as tf.Tensor2D;
    const poseFlagTensor = tensors[1] as tf.Tensor2D;
    const heatmapTensor = tensors[2] as tf.Tensor4D;
    const worldLandmarkTensor = tensors[3] as tf.Tensor2D;
    const segmentationTensor = enableSegmentation ? tensors[4] as tf.Tensor4D : null;

    console.log('🔧 PHASE 4 TENSOR PROCESSING: Tensor shapes:', {
      landmarks: landmarkTensor.shape,
      poseFlag: poseFlagTensor.shape,
      heatmap: heatmapTensor.shape,
      worldLandmarks: worldLandmarkTensor.shape,
      segmentation: segmentationTensor?.shape || 'disabled'
    });

    // -------------------------------------------------------------------------
    // ---------------------------- Pose Score --------------------------------
    // -------------------------------------------------------------------------

    // Extract pose confidence score
    const poseScore = (await poseFlagTensor.data())[0];
    console.log('🔧 PHASE 4 TENSOR PROCESSING: Pose confidence score:', poseScore.toFixed(4));

    // Apply threshold to determine if pose is present
    if (poseScore < constants.BLAZEPOSE_POSE_PRESENCE_SCORE) {
      console.log('❌ PHASE 4 TENSOR PROCESSING: Pose score below threshold, no pose detected');
      return null;
    }

    // -------------------------------------------------------------------------
    // ---------------------------- Pose Landmarks ----------------------------
    // -------------------------------------------------------------------------

    // Convert landmark tensors to normalized keypoints
    const rawLandmarks = await tensorsToLandmarks(
        landmarkTensor, 
        constants.BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG);

    console.log('🔧 PHASE 4 TENSOR PROCESSING: Raw landmarks extracted:', rawLandmarks.length);

    // Refine landmarks using heatmap for sub-pixel accuracy
    const allLandmarks = await refineLandmarksFromHeatmap(
        rawLandmarks, 
        heatmapTensor,
        constants.BLAZEPOSE_REFINE_LANDMARKS_FROM_HEATMAP_CONFIG);

    console.log('🔧 PHASE 4 TENSOR PROCESSING: Landmarks refined:', allLandmarks.length);

    // Split landmarks into main pose landmarks and auxiliary landmarks
    const landmarks = allLandmarks.slice(0, constants.BLAZEPOSE_NUM_KEYPOINTS);

    // CRITICAL FIX: For BlazePose Full model, auxiliary landmarks are a subset of main landmarks
    // used for pose tracking and ROI calculation, not additional landmarks from the tensor
    // Use specific keypoints that are most stable for tracking (shoulders, hips, etc.)
    const auxiliaryLandmarks = [
      landmarks[11], // left_shoulder
      landmarks[12], // right_shoulder
      landmarks[23], // left_hip
      landmarks[24], // right_hip
    ].filter(landmark => landmark && typeof landmark.x === 'number' && typeof landmark.y === 'number');

    console.log('🔧 PHASE 4 TENSOR PROCESSING: Split landmarks:', {
      main: landmarks.length,
      auxiliary: auxiliaryLandmarks.length,
      auxiliaryIndices: [11, 12, 23, 24]
    });

    // CRITICAL DEBUG: Log auxiliary landmark coordinates to verify fix
    console.log('🔧 AUXILIARY LANDMARKS FIX: Auxiliary landmark coordinates:',
      auxiliaryLandmarks.map((landmark, i) => ({
        index: i,
        x: landmark.x?.toFixed(3),
        y: landmark.y?.toFixed(3),
        score: landmark.score?.toFixed(3)
      }))
    );

    // -------------------------------------------------------------------------
    // ------------------------- World Landmarks ------------------------------
    // -------------------------------------------------------------------------

    // Convert world landmark tensors to 3D keypoints
    const allWorldLandmarks = await tensorsToWorldLandmarks(
        worldLandmarkTensor,
        constants.BLAZEPOSE_TENSORS_TO_WORLD_LANDMARKS_CONFIG);

    // Extract main world landmarks (same count as main landmarks)
    const worldLandmarks = allWorldLandmarks.slice(0, constants.BLAZEPOSE_NUM_KEYPOINTS);

    console.log('🔧 PHASE 4 TENSOR PROCESSING: World landmarks extracted:', worldLandmarks.length);

    // -------------------------------------------------------------------------
    // -------------------------- Segmentation Mask --------------------------
    // -------------------------------------------------------------------------

    let segmentationMask: tf.Tensor2D | null = null;

    if (enableSegmentation && segmentationTensor) {
      console.log('🔧 PHASE 4 TENSOR PROCESSING: Processing segmentation mask');
      
      segmentationMask = tensorsToSegmentation(
          segmentationTensor,
          { activation: 'sigmoid' as const });

      console.log('🔧 PHASE 4 TENSOR PROCESSING: Segmentation mask shape:', segmentationMask.shape);
    }

    const result: TensorsToPoseLandmarksAndSegmentationResult = {
      landmarks,
      auxiliaryLandmarks,
      worldLandmarks,
      poseScore,
      segmentationMask
    };

    console.log('✅ PHASE 4 TENSOR PROCESSING: Successfully processed all tensors');
    return result;

  } catch (error) {
    console.error('❌ PHASE 4 TENSOR PROCESSING: Error processing tensors:', error);
    return null;
  }
}