
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * BlazePose model constants and configurations
 */

export const BLAZEPOSE_KEYPOINTS = [
  'nose',
  'left_eye_inner', 'left_eye', 'left_eye_outer',
  'right_eye_inner', 'right_eye', 'right_eye_outer',
  'left_ear', 'right_ear',
  'mouth_left', 'mouth_right',
  'left_shoulder', 'right_shoulder',
  'left_elbow', 'right_elbow',
  'left_wrist', 'right_wrist',
  'left_pinky', 'right_pinky',
  'left_index', 'right_index',
  'left_thumb', 'right_thumb',
  'left_hip', 'right_hip',
  'left_knee', 'right_knee',
  'left_ankle', 'right_ankle',
  'left_heel', 'right_heel',
  'left_foot_index', 'right_foot_index'
];

export const BLAZEPOSE_CONNECTIONS = [
  [0, 1], [1, 2], [2, 3], [3, 7],
  [0, 4], [4, 5], [5, 6], [6, 8],
  [9, 10],
  [11, 12], [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], [17, 19],
  [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], [18, 20],
  [11, 23], [12, 24], [23, 24],
  [23, 25], [25, 27], [27, 29], [27, 31],
  [24, 26], [26, 28], [28, 30], [28, 32]
];

/**
 * BlazePose Lite configuration (legacy - kept for compatibility)
 */
export const BLAZEPOSE_LITE_CONFIG = {
  modelType: 'lite' as const,
  inputSize: { width: 256, height: 256 },
  numLandmarks: 33,
  numDimensions: 5, // x, y, z, visibility, presence
  scoreThreshold: 0.3,
  maxPoses: 1
};

/**
 * BlazePose Full configuration (recommended default)
 * TASK 1: Updated configuration for BlazePose Full model
 * TASK 2: Fixed landmark count to match Full model (39 vs 33)
 * TASK 3: Optimized detection confidence thresholds for Full model
 * TASK 4: Confirmed input size requirements (256x256 for landmarks, 224x224 for detector)
 */
export const BLAZEPOSE_FULL_CONFIG = {
  modelType: 'full' as const,
  inputSize: { width: 256, height: 256 }, // TASK 4: Landmark model uses 256x256 (verified)
  detectorInputSize: { width: 224, height: 224 }, // TASK 4: Detector model uses 224x224 (verified)
  numLandmarks: 39, // TASK 2: Full model outputs 39 landmarks
  numDimensions: 5, // x, y, z, visibility, presence
  scoreThreshold: 0.15, // TASK 3: Optimized for Full model detection accuracy
  maxPoses: 1,
  // TASK 3: Full model specific threshold optimizations
  enableSmoothing: true,
  enableSegmentation: false, // Can be enabled if needed
  confidenceThreshold: 0.6, // TASK 3: Optimized for Full model (balanced accuracy vs sensitivity)
  keypointConfidenceThreshold: 0.4, // TASK 3: Lowered for better keypoint detection
  visibilityThreshold: 0.3, // TASK 3: Threshold for keypoint visibility
  presenceThreshold: 0.5 // TASK 3: Threshold for keypoint presence
};

/**
 * Tensor processing configurations
 * TASK 1: Updated to use Full model configuration instead of Lite
 */
export const BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG = {
  numLandmarks: BLAZEPOSE_FULL_CONFIG.numLandmarks,
  inputImageWidth: BLAZEPOSE_FULL_CONFIG.inputSize.width,
  inputImageHeight: BLAZEPOSE_FULL_CONFIG.inputSize.height,
  normalizeCoordinates: false, // CRITICAL FIX: BlazePose outputs are already normalized (0-1 range)
  flipHorizontally: false,
  flipVertically: false
};

/**
 * Legacy configuration for backwards compatibility
 */
export const BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG_LITE = {
  numLandmarks: BLAZEPOSE_LITE_CONFIG.numLandmarks,
  inputImageWidth: BLAZEPOSE_LITE_CONFIG.inputSize.width,
  inputImageHeight: BLAZEPOSE_LITE_CONFIG.inputSize.height,
  normalizeCoordinates: false, // CRITICAL FIX: BlazePose outputs are already normalized (0-1 range)
  flipHorizontally: false,
  flipVertically: false
};
