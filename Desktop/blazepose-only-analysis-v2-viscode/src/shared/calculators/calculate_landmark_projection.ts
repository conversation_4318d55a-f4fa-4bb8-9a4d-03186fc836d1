
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * Projects landmarks from ROI coordinates back to the original image coordinates.
 * This is critical for displaying landmarks in the correct position on the full image.
 */
export function calculateLandmarkProjection(
    landmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 LANDMARK PROJECTION: Projecting landmarks from ROI to image coordinates');
  console.log('🔧 LANDMARK PROJECTION: ROI rect: ' + JSON.stringify(rect));
  console.log('🔧 LANDMARK PROJECTION: Input landmarks count:', landmarks.length);
  
  // CRITICAL FIX: Validate ROI coordinates before projection to prevent invalid output
  if (rect.xCenter < 0 || rect.xCenter > 1 || rect.yCenter < 0 || rect.yCenter > 1) {
    console.warn('🔧 LANDMARK PROJECTION: Invalid ROI center detected, using fallback:', {
      originalXCenter: rect.xCenter,
      originalYCenter: rect.yCenter,
      fallbackXCenter: 0.5,
      fallbackYCenter: 0.5
    });
    // Use safe fallback ROI center
    rect.xCenter = 0.5;
    rect.yCenter = 0.5;
  }
  
  // CRITICAL FIX: Ensure ROI size is reasonable
  if (rect.width <= 0 || rect.width > 1 || rect.height <= 0 || rect.height > 1) {
    console.warn('🔧 LANDMARK PROJECTION: Invalid ROI size detected, using fallback:', {
      originalWidth: rect.width,
      originalHeight: rect.height,
      fallbackWidth: 0.4,
      fallbackHeight: 0.6
    });
    rect.width = 0.4;
    rect.height = 0.6;
  }

  const projectedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // PHASE 6B: NaN-safe coordinate handling
    let x = landmark.x;
    let y = landmark.y;
    
    // PHASE 6B: Validate input coordinates
    if (isNaN(x) || !isFinite(x)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN X in landmark projection ${index}`);
      x = 0.5;
    }
    if (isNaN(y) || !isFinite(y)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN Y in landmark projection ${index}`);
      y = 0.5;
    }
    
    // CRITICAL FIX: Use TensorFlow.js reference implementation projection formula
    // Ensure rect properties are valid before projection
    const validRect = {
      width: isFinite(rect.width) && rect.width > 0 ? rect.width : 0.1,
      height: isFinite(rect.height) && rect.height > 0 ? rect.height : 0.1,
      xCenter: isFinite(rect.xCenter) ? rect.xCenter : 0.5,
      yCenter: isFinite(rect.yCenter) ? rect.yCenter : 0.5,
      rotation: isFinite(rect.rotation) ? rect.rotation : 0
    };

    // Step 1: Center coordinates to [-0.5, 0.5] range (TensorFlow.js reference)
    const centeredX = x - 0.5;
    const centeredY = y - 0.5;

    // Step 2: Apply rotation transformation (TensorFlow.js reference)
    const angle = validRect.rotation;
    let rotatedX = centeredX;
    let rotatedY = centeredY;

    if (angle !== 0) {
      rotatedX = Math.cos(angle) * centeredX - Math.sin(angle) * centeredY;
      rotatedY = Math.sin(angle) * centeredX + Math.cos(angle) * centeredY;
    }

    // Step 3: Scale and translate to final coordinates (TensorFlow.js reference)
    let projectedX = rotatedX * validRect.width + validRect.xCenter;
    let projectedY = rotatedY * validRect.height + validRect.yCenter;

    // TASK 3: Comprehensive validation of projected coordinates
    if (isNaN(projectedX) || !isFinite(projectedX)) {
      console.warn(`🔧 LANDMARK PROJECTION: Fixed NaN projected X at landmark ${index}`);
      projectedX = validRect.xCenter;
    }
    if (isNaN(projectedY) || !isFinite(projectedY)) {
      console.warn(`🔧 LANDMARK PROJECTION: Fixed NaN projected Y at landmark ${index}`);
      projectedY = validRect.yCenter;
    }

    // TASK 3: Ensure projected coordinates are in reasonable range (normalized 0-1)
    if (projectedX < -0.5 || projectedX > 1.5) {
      console.warn(`🔧 LANDMARK PROJECTION: Clamped projected X at landmark ${index}: ${projectedX} -> ${validRect.xCenter}`);
      projectedX = validRect.xCenter;
    }
    if (projectedY < -0.5 || projectedY > 1.5) {
      console.warn(`🔧 LANDMARK PROJECTION: Clamped projected Y at landmark ${index}: ${projectedY} -> ${validRect.yCenter}`);
      projectedY = validRect.yCenter;
    }

    const projectedLandmark: Keypoint = {
      x: projectedX,
      y: projectedY,
      score: landmark.score,
      name: landmark.name
    };

    // Scale z-coordinate if it exists (TensorFlow.js reference: scale Z as X)
    if (landmark.z !== undefined) {
      let z = landmark.z;
      if (isNaN(z) || !isFinite(z)) {
        z = 0;
      }
      // Scale Z coordinate by width (TensorFlow.js reference implementation)
      projectedLandmark.z = z * validRect.width;
    }

    // Log first few projections for debugging
    if (index < 5) {
      const cleanData = {
        roi: { x: landmark.x?.toFixed(4), y: landmark.y?.toFixed(4) },
        projected: { x: projectedX.toFixed(4), y: projectedY.toFixed(4) },
        score: landmark.score?.toFixed(4)
      };
      console.log(`🔧 PHASE 6B: Landmark ${index} projection: ${JSON.stringify(cleanData)}`);
    }

    return projectedLandmark;
  });

  console.log(`🔧 LANDMARK PROJECTION: Successfully projected ${projectedLandmarks.length} landmarks`);
  return projectedLandmarks;
}
