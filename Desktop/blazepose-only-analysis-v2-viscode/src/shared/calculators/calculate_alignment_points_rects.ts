/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {computeRotation} from './detection_to_rect';
import {ImageSize} from './interfaces/common_interfaces';
import {DetectionToRectConfig} from './interfaces/config_interfaces';
import {Rect} from './interfaces/shape_interfaces';
import {Detection} from './interfaces/shape_interfaces';

// ref:
// https://github.com/google/mediapipe/blob/master/mediapipe/calculators/util/alignment_points_to_rects_calculator.cc
/**
 * Validates if a keypoint has valid coordinates
 * CRITICAL FIX: Accept much wider range for face keypoints since they can legitimately appear far off-screen
 * when person is positioned anywhere in the camera frame
 */
function isValidKeypoint(kp: any): boolean {
  return kp &&
         typeof kp.x === 'number' && !isNaN(kp.x) && isFinite(kp.x) &&
         typeof kp.y === 'number' && !isNaN(kp.y) && isFinite(kp.y) &&
         kp.x >= -2.0 && kp.x <= 3.0 && kp.y >= -2.0 && kp.y <= 3.0; // Allow far off-screen detection
}

/**
 * Creates a fallback ROI from bounding box with reasonable padding
 * Used for detector detections that don't have keypoints (normal BlazePose behavior)
 */
function createFallbackROI(boundingBox: any, imageSize: ImageSize): Rect {
  console.log('🔧 ALIGNMENT POINTS: Creating bounding box ROI for detector detection');

  if (boundingBox && typeof boundingBox.xMin === 'number') {
    // Use bounding box with padding
    const padding = 0.1; // 10% padding
    let centerX = (boundingBox.xMin + boundingBox.xMax) / 2;
    let centerY = (boundingBox.yMin + boundingBox.yMax) / 2;
    let width = (boundingBox.xMax - boundingBox.xMin) * (1 + padding);
    let height = (boundingBox.yMax - boundingBox.yMin) * (1 + padding);

    // CRITICAL FIX: Validate fallback ROI coordinates to prevent invalid regions
    centerX = Math.max(0.1, Math.min(0.9, centerX)); // Keep within 10%-90% of image
    centerY = Math.max(0.1, Math.min(0.9, centerY)); // Keep within 10%-90% of image
    width = Math.max(0.1, Math.min(0.8, width));     // 10%-80% of image width
    height = Math.max(0.1, Math.min(0.8, height));   // 10%-80% of image height

    console.log('🔧 ALIGNMENT POINTS: Fallback ROI from bounding box:', {
      centerX: centerX.toFixed(3),
      centerY: centerY.toFixed(3),
      width: width.toFixed(3),
      height: height.toFixed(3)
    });

    return {
      xCenter: centerX,
      yCenter: centerY,
      width: width,
      height: height,
      rotation: 0
    };
  }

  // Final fallback - center of image with reasonable size
  console.warn('🔧 ALIGNMENT POINTS: Using center fallback ROI');
  return {
    xCenter: 0.5,
    yCenter: 0.5,
    width: 0.4,  // Increased from 0.3 to capture more of the person
    height: 0.6, // Increased from 0.3 for better person coverage
    rotation: 0
  };
}

export function calculateAlignmentPointsRects(
    detection: Detection, imageSize: ImageSize,
    config: DetectionToRectConfig): Rect {

  // CRITICAL: Validate input parameters
  if (!detection) {
    console.error('🔧 ALIGNMENT POINTS: No detection object provided');
    return createFallbackROI(null, imageSize);
  }

  if (!config) {
    console.error('🔧 ALIGNMENT POINTS: No config provided');
    return createFallbackROI(detection.boundingBox, imageSize);
  }

  const startKeypoint = config.rotationVectorStartKeypointIndex;
  const endKeypoint = config.rotationVectorEndKeypointIndex;
  const locationData = detection.locationData;

  const configData = {
    startKeypoint,
    endKeypoint,
    hasLocationData: !!locationData,
    hasRelativeKeypoints: !!(locationData?.relativeKeypoints),
    relativeKeypointsLength: locationData?.relativeKeypoints?.length || 0
  };
  console.log('🔧 ALIGNMENT POINTS: Processing alignment with config: ' + JSON.stringify(configData));

  // CRITICAL FIX: Handle detector detections (no keypoints) vs landmark detections (with keypoints)
  // Detector detections use bounding box for ROI calculation (normal behavior)
  // Landmark detections use keypoints for precise alignment (enhanced behavior)
  if (!locationData || !locationData.relativeKeypoints || !Array.isArray(locationData.relativeKeypoints)) {
    console.log('🔧 ALIGNMENT POINTS: Detector detection (no keypoints) - using bounding box ROI');
    const detectionStructure = {
      hasLocationData: !!locationData,
      locationDataKeys: locationData ? Object.keys(locationData) : 'N/A',
      hasRelativeKeypoints: !!(locationData && locationData.relativeKeypoints),
      relativeKeypointsType: locationData?.relativeKeypoints ? typeof locationData.relativeKeypoints : 'undefined',
      relativeKeypointsLength: locationData?.relativeKeypoints?.length || 0
    };
    console.log('🔧 ALIGNMENT POINTS: Detection structure: ' + JSON.stringify(detectionStructure));
    return createFallbackROI(locationData?.relativeBoundingBox || detection.boundingBox, imageSize);
  }

  // Validate keypoint indices are within bounds
  if (startKeypoint >= locationData.relativeKeypoints.length ||
      endKeypoint >= locationData.relativeKeypoints.length ||
      startKeypoint < 0 || endKeypoint < 0) {
    const boundsError = {
      startKeypoint,
      endKeypoint,
      available: locationData.relativeKeypoints.length,
      configObject: config
    };
    console.error('🔧 ALIGNMENT POINTS: Keypoint indices out of bounds ' + JSON.stringify(boundsError));
    return createFallbackROI(locationData.relativeBoundingBox || detection.boundingBox, imageSize);
  }

  // Validate keypoints have valid coordinates
  const startKp = locationData.relativeKeypoints[startKeypoint];
  const endKp = locationData.relativeKeypoints[endKeypoint];

  // CRITICAL DEBUG: Check if we're using face keypoints for body pose detection
  console.log('🔧 ALIGNMENT POINTS: FACE KEYPOINT ANALYSIS:', {
    startKeypointIndex: startKeypoint,
    endKeypointIndex: endKeypoint,
    keypointMapping: {
      0: 'nose',
      1: 'left_eye_inner',
      2: 'left_eye',
      3: 'left_eye_outer'
    },
    startKp: { x: startKp?.x, y: startKp?.y, type: startKeypoint === 0 ? 'NOSE' : 'LEFT_EYE_INNER' },
    endKp: { x: endKp?.x, y: endKp?.y, type: endKeypoint === 1 ? 'LEFT_EYE_INNER' : 'NOSE' },
    totalRelativeKeypoints: locationData.relativeKeypoints.length
  });

  if (!isValidKeypoint(startKp) || !isValidKeypoint(endKp)) {
    console.warn('🔧 ALIGNMENT POINTS: Invalid keypoint coordinates, using fallback');
    console.warn('🔧 ALIGNMENT POINTS: Keypoint validation:', {
      startKp: startKp,
      endKp: endKp,
      startValid: isValidKeypoint(startKp),
      endValid: isValidKeypoint(endKp)
    });
    return createFallbackROI(locationData.relativeBoundingBox || detection.boundingBox, imageSize);
  }

  // SUCCESS: All validations passed, proceed with keypoint-based alignment calculation
  console.log('🔧 ALIGNMENT POINTS: Landmark detection (with keypoints) - using precise keypoint alignment');

  try {
    // Convert normalized coordinates to pixel coordinates for calculation
    const xCenter = startKp.x * imageSize.width;
    const yCenter = startKp.y * imageSize.height;
    const xScale = endKp.x * imageSize.width;
    const yScale = endKp.y * imageSize.height;

    // CRITICAL FIX: Detect and correct flattened Y coordinates
    // If both keypoints have identical or near-identical Y coordinates, this indicates
    // a coordinate flattening issue in the pipeline
    const yDifference = Math.abs(yScale - yCenter);
    const isYFlattened = yDifference < 5; // Less than 5 pixels difference indicates flattening

    let correctedYCenter = yCenter;
    let correctedBoxSize;

    if (isYFlattened) {
      // CRITICAL FIX: Use a reasonable default position for the pose center
      // Position the ROI at the center of the image with appropriate size
      correctedYCenter = imageSize.height * 0.5; // Center of image
      correctedBoxSize = Math.min(imageSize.width, imageSize.height) * 0.6; // 60% of smaller dimension

      console.warn('🔧 ALIGNMENT POINTS: Detected flattened Y coordinates, applying correction');
      console.warn('🔧 ALIGNMENT POINTS: Original yCenter:', yCenter, 'Corrected yCenter:', correctedYCenter);
    } else {
      // Normal calculation when Y coordinates are properly distributed
      const boxSize = Math.sqrt(
                          (xScale - xCenter) * (xScale - xCenter) +
                          (yScale - yCenter) * (yScale - yCenter)) *
          2;

      // Ensure minimum box size to prevent degenerate cases
      const minBoxSize = Math.min(imageSize.width, imageSize.height) * 0.05; // 5% of smaller dimension
      correctedBoxSize = Math.max(boxSize, minBoxSize);
    }

    const rotation = computeRotation(detection, imageSize, config);

    // Convert back to normalized coordinates for the result
    // CRITICAL FIX: Validate and constrain ROI coordinates to prevent invalid regions
    let normalizedXCenter = xCenter / imageSize.width;
    let normalizedYCenter = correctedYCenter / imageSize.height;
    let normalizedWidth = correctedBoxSize / imageSize.width;
    let normalizedHeight = correctedBoxSize / imageSize.height;
    
    // CRITICAL FIX: Prevent negative or extreme ROI coordinates that break landmark projection
    if (normalizedXCenter < 0) {
      console.warn('🔧 ALIGNMENT POINTS: ROI xCenter negative, correcting:', normalizedXCenter, '→ 0.5');
      normalizedXCenter = 0.5; // Center of image
    }
    if (normalizedYCenter < 0) {
      console.warn('🔧 ALIGNMENT POINTS: ROI yCenter negative, correcting:', normalizedYCenter, '→ 0.5');
      normalizedYCenter = 0.5; // Center of image
    }
    if (normalizedXCenter > 1) {
      console.warn('🔧 ALIGNMENT POINTS: ROI xCenter > 1, correcting:', normalizedXCenter, '→ 0.5');
      normalizedXCenter = 0.5;
    }
    if (normalizedYCenter > 1) {
      console.warn('🔧 ALIGNMENT POINTS: ROI yCenter > 1, correcting:', normalizedYCenter, '→ 0.5');
      normalizedYCenter = 0.5;
    }
    
    // CRITICAL FIX: Ensure minimum and maximum ROI size to prevent degenerate regions
    normalizedWidth = Math.max(0.1, Math.min(0.8, normalizedWidth));   // 10%-80% of image width
    normalizedHeight = Math.max(0.1, Math.min(0.8, normalizedHeight)); // 10%-80% of image height
    
    const result = {
      xCenter: normalizedXCenter,
      yCenter: normalizedYCenter,
      width: normalizedWidth,
      height: normalizedHeight,
      rotation: rotation || 0
    };

    const alignmentData = {
      result: result,
      boxSize: correctedBoxSize.toFixed(2),
      rotation: (rotation || 0).toFixed(2)
    };
    console.log('🔧 ALIGNMENT POINTS: Successfully calculated alignment rect: ' + JSON.stringify(alignmentData));

    return result;

  } catch (error) {
    console.error('🔧 ALIGNMENT POINTS: Error during alignment calculation:', error);
    return createFallbackROI(locationData.relativeBoundingBox || detection.boundingBox, imageSize);
  }
}
