
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { PERFORMANCE_LIMITS } from '../../blazepose_tfjs/constants';
import { PerformanceMonitor } from '../../blazepose_tfjs/performance_monitor';
import { logger, logTensorOperation } from '../../utils/logging_system';
import { disposeTensor, registerTensor } from '../../utils/memory_manager';

/**
 * PHASE 1: Detector result processing for BlazePose detection phase.
 */

export interface DetectorResult {
  boxes: tf.Tensor2D;
  logits: tf.Tensor2D;
}

/**
 * Extracts detector results from raw model output tensors.
 * TASK 1: Enhanced with performance monitoring and early termination
 */
export function detectorResult(
    detectionTensors: tf.Tensor[]): DetectorResult {

  // TASK 1: Initialize performance monitoring
  const monitor = new PerformanceMonitor();
  monitor.startFrame();

  // TASK 5: Comprehensive logging starts with performance timing
  const startTime = performance.now();
  // Processing detector output tensors (logging disabled for performance)
  const ENABLE_DETAILED_LOGGING = PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING;

  // Enhanced validation: Check for empty input
  if (detectionTensors.length < 1) {
    logger.error('tensorProcessing', 'No detection tensors provided');
    return {
      boxes: registerTensor(tf.zeros([1, 4]) as tf.Tensor2D),
      logits: registerTensor(tf.zeros([1, 1]) as tf.Tensor2D)
    };
  }

  try {
    const mainTensor = detectionTensors[0];
    // Main tensor processing

    // Enhanced validation: Validate tensor format for BlazePose
    if (mainTensor.shape.length !== 3) {
      console.error('🔧 DETECTOR RESULT: Expected 3D tensor, got shape:', mainTensor.shape);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    const [batchSize, numAnchors, numValues] = mainTensor.shape;
    // Tensor dimensions processed

    // TASK 1: BlazePose tensor format validation (logging disabled)
    
    // Validate batch dimension
    if (batchSize !== 1) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires batch size 1, got:', batchSize);
      console.error('🔧 DETECTOR RESULT: BlazePose detection phase expects single-batch processing');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // Validate anchor count (should be reasonable for BlazePose)
    if (numAnchors <= 0) {
      console.error('🔧 DETECTOR RESULT: Invalid anchor count:', numAnchors);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // TASK 1: Strict BlazePose format validation for 13-value structure
    if (numValues !== 13) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires exactly 13 values per detection, got:', numValues);
      console.error('🔧 DETECTOR RESULT: Expected format: [yCenter, xCenter, height, width, score, ...8 additional values]');
      console.error('🔧 DETECTOR RESULT: This tensor does not match BlazePose detection output specification');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    if (ENABLE_DETAILED_LOGGING) {
      logTensorOperation('BlazePose tensor format validated', {
        format: `[batch=1, anchors=${numAnchors}, values=13]`,
        structure: '[4 box coords] + [1 score] + [8 additional values]'
      });
    }

    // Handle BlazePose single tensor format: [batch, anchors, 13]
    // Where 13 = [4 box coords, 1 score, 8 additional values]
    if (mainTensor.shape.length === 3 && mainTensor.shape[2] >= 5) {
      if (ENABLE_DETAILED_LOGGING) {
        logTensorOperation('Processing BlazePose tensor format', {
          coordinateOrder: '[yCenter, xCenter, height, width] at indices [0,1,2,3]',
          scoreIndex: 4
        });
      }
      
      // TASK 2: Validate slicing parameters before execution
      const sliceStart = [0, 0, 0];
      const sliceSize = [-1, -1, 4]; // -1 means "all elements in that dimension"
      const scoreStart = [0, 0, 4];
      const scoreSize = [-1, -1, 1];
      
      // Slice parameters configured
      
      // Validate slice bounds
      if (mainTensor.shape[2] < 5) {
        console.error('🔧 DETECTOR RESULT: Tensor too small for slicing - need at least 5 values, got:', mainTensor.shape[2]);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      // Executing tensor slicing and validation (logging disabled for performance)
      try {
        // Test tensor metadata accessibility
        const tensorMetadata = {
          dtype: mainTensor.dtype,
          size: mainTensor.size,
          rank: mainTensor.rank,
          memory: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`
        };
        // Tensor metadata validated
        
        // Test tensor bounds before slicing
        const [batch, anchors, values] = mainTensor.shape;
        const totalElements = batch * anchors * values;
        
        if (totalElements !== mainTensor.size) {
          console.error('🔧 DETECTOR RESULT: Tensor size mismatch - shape implies', totalElements, 'but size is', mainTensor.size);
          return {
            boxes: tf.zeros([1, 4]) as tf.Tensor2D,
            logits: tf.zeros([1, 1]) as tf.Tensor2D
          };
        }
        
        // Tensor integrity tests passed
        
      } catch (metadataError) {
        console.error('🔧 DETECTOR RESULT: Tensor metadata access failed:', metadataError);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }

      // TASK 2: Execute slicing with explicit BlazePose coordinate interpretation
      // Slice boxes: Extract first 4 values [yCenter, xCenter, height, width]
      const boxes = tf.slice(mainTensor, sliceStart, sliceSize) as tf.Tensor3D;
      // Slice scores: Extract 5th value [score]
      const scores = tf.slice(mainTensor, scoreStart, scoreSize) as tf.Tensor3D;
      
      // TASK 4: Post-slicing indexing validation
      console.log('🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...');
      
      // Test tensor element accessibility
      try {
        const boxesShape = boxes.shape;
        const scoresShape = scores.shape;
        
        // Validate indexing bounds
        const expectedBoxElements = boxesShape[0] * boxesShape[1] * boxesShape[2];
        const expectedScoreElements = scoresShape[0] * scoresShape[1] * scoresShape[2];
        
        if (boxes.size !== expectedBoxElements) {
          console.error('🔧 DETECTOR RESULT: Boxes tensor indexing mismatch - expected', expectedBoxElements, 'got', boxes.size);
        }
        
        if (scores.size !== expectedScoreElements) {
          console.error('🔧 DETECTOR RESULT: Scores tensor indexing mismatch - expected', expectedScoreElements, 'got', scores.size);
        }
        
        // Test small sample data extraction to verify indexing
        if (boxesShape[1] > 0) {
          const testSlice = tf.slice(boxes, [0, 0, 0], [1, 1, 4]);
          const testData = testSlice.dataSync();
          testSlice.dispose();
          
          console.log('🔧 DETECTOR RESULT: Index validation - first detection coordinates:', 
            Array.from(testData).map(v => v.toFixed(4)));
        }
        
        console.log('✅ DETECTOR RESULT: Tensor indexing validation passed');
        
      } catch (indexingError) {
        console.error('🔧 DETECTOR RESULT: Tensor indexing validation failed:', indexingError);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      // TASK 2: Validate slicing results
      console.log('🔧 DETECTOR RESULT: Slicing completed successfully');
      console.log('🔧 DETECTOR RESULT: Boxes tensor shape (3D):', boxes.shape);
      console.log('🔧 DETECTOR RESULT: Scores tensor shape (3D):', scores.shape);
      
      // Verify shapes match expectations
      if (boxes.shape[2] !== 4) {
        console.error('🔧 DETECTOR RESULT: Box slice failed - expected 4 coordinates, got:', boxes.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      if (scores.shape[2] !== 1) {
        console.error('🔧 DETECTOR RESULT: Score slice failed - expected 1 score, got:', scores.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      console.log('✅ DETECTOR RESULT: Tensor slicing validation passed');
      
      // TASK 3: Validate anchor-based coordinate transformation readiness
      console.log('🔧 DETECTOR RESULT: Starting coordinate transformation validation');
      console.log('🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility');
      
      // Sample coordinate data for validation (first few detections)
      try {
        const sampleSize = Math.min(3, boxes.shape[1]); // Check first 3 detections or all if fewer
        const sampleSlice = tf.slice(boxes, [0, 0, 0], [1, sampleSize, 4]);
        const sampleData = sampleSlice.dataSync();
        sampleSlice.dispose();
        
        // TASK 1: Sample coordinate validation with performance monitoring
        let invalidDetectionCount = 0;
        const maxSampleSize = Math.min(sampleSize, PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME);

        for (let i = 0; i < maxSampleSize; i++) {
          // TASK 1: Check performance limits during validation
          if (monitor.shouldLimitProcessing()) {
            if (ENABLE_DETAILED_LOGGING) {
              console.warn(`🔧 DETECTOR RESULT: Stopping validation at detection ${i} due to time limit`);
            }
            break;
          }

          const baseIdx = i * 4;
          // CRITICAL FIX: BlazePose tensor format is [xCenter, yCenter, height, width] NOT [yCenter, xCenter]
          // This coordinate order swap was causing all downstream coordinate corruption
          const coords = {
            xCenter: sampleData[baseIdx + 0],  // FIXED: X comes first in tensor
            yCenter: sampleData[baseIdx + 1],  // FIXED: Y comes second in tensor
            height: sampleData[baseIdx + 2],
            width: sampleData[baseIdx + 3]
          };
          
          // PIPELINE TRACE STEP 2: Log extracted coordinates from tensor
          if (i < 3) { // Log first 3 detections for verification
            console.log(`🔍 PIPELINE TRACE STEP 2: Detection ${i} coordinates from tensor:`, {
              xCenter: coords.xCenter.toFixed(6),
              yCenter: coords.yCenter.toFixed(6), 
              height: coords.height.toFixed(6),
              width: coords.width.toFixed(6),
              rawIndex: baseIdx
            });
          }

          // TASK 3: Validate coordinate ranges for anchor transformation
          const isValidCoords =
            isFinite(coords.xCenter) && isFinite(coords.yCenter) &&
            isFinite(coords.height) && isFinite(coords.width);

          if (!isValidCoords) {
            monitor.recordInvalidDetection();
            invalidDetectionCount++;
            // TASK 3: Reduced coordinate validation logging
            if (ENABLE_DETAILED_LOGGING && Math.random() < PERFORMANCE_LIMITS.LOG_SAMPLE_RATE) {
              logger.warn('coordinates', 'Invalid detection coordinates', { index: i, reason: 'non-finite values' });
            }
          }

          // TASK 1: Enhanced coordinate validation with performance limits
          const hasReasonableValues =
            Math.abs(coords.xCenter) <= PERFORMANCE_LIMITS.MAX_COORDINATE_VALUE &&
            Math.abs(coords.yCenter) <= PERFORMANCE_LIMITS.MAX_COORDINATE_VALUE &&
            coords.height > 0 && coords.width > 0 &&
            coords.height <= PERFORMANCE_LIMITS.MAX_COORDINATE_VALUE &&
            coords.width <= PERFORMANCE_LIMITS.MAX_COORDINATE_VALUE;

          if (!hasReasonableValues) {
            monitor.recordInvalidDetection();
            invalidDetectionCount++;
            // TASK 3: Reduced coordinate validation logging
            if (ENABLE_DETAILED_LOGGING && Math.random() < PERFORMANCE_LIMITS.LOG_SAMPLE_RATE) {
              logger.warn('coordinates', 'Unusual coordinate values detected', { index: i });
            }
          } else {
            monitor.recordDetectionProcessed();
          }

          // TASK 1: Early termination for excessive invalid detections
          if (invalidDetectionCount >= PERFORMANCE_LIMITS.MAX_CONSECUTIVE_INVALID_DETECTIONS) {
            if (ENABLE_DETAILED_LOGGING) {
              console.warn(`🔧 DETECTOR RESULT: Stopping validation due to ${invalidDetectionCount} invalid detections`);
            }
            break;
          }
        }
        
        // Coordinate format verification completed
        
      } catch (validationError) {
        console.warn('🔧 DETECTOR RESULT: Could not validate coordinate data:', validationError);
      }
      
      // Coordinate transformation validation completed
      
      // Reshape to 2D for consistency with downstream processing
      const boxes2D = tf.reshape(boxes, [boxes.shape[1], 4]) as tf.Tensor2D;
      const logits2D = tf.reshape(scores, [scores.shape[1], 1]) as tf.Tensor2D;
      
      // Final tensor validation and reshaping completed (logging disabled for performance)
      
      // Clean up intermediate tensors
      boxes.dispose();
      scores.dispose();
      
      // TASK 1: Enhanced performance logging with monitoring data
      const processingTime = monitor.endFrame();
      const performanceMetrics = monitor.getMetrics();

      // TASK 3: Optimized performance logging
      if (ENABLE_DETAILED_LOGGING) {
        logTensorOperation('Detector processing completed', {
          totalTime: `${processingTime.toFixed(2)}ms`,
          detectionsExtracted: boxes2D.shape[0],
          detectionsProcessed: performanceMetrics.detectionsProcessed,
          invalidDetections: performanceMetrics.invalidDetections,
          wasLimited: processingTime > PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS
        });
      }

      // TASK 3: Memory management
      disposeTensor([boxes, scores]);
      
      // TASK 5: Statistical analysis of extracted data
      try {
        const boxSample = tf.slice(boxes2D, [0, 0], [Math.min(5, boxes2D.shape[0]), 4]);
        const scoreSample = tf.slice(logits2D, [0, 0], [Math.min(5, logits2D.shape[0]), 1]);
        
        const boxData = boxSample.dataSync();
        const scoreData = scoreSample.dataSync();
        
        boxSample.dispose();
        scoreSample.dispose();
        
        const boxStats = {
          min: Math.min(...boxData),
          max: Math.max(...boxData),
          mean: Array.from(boxData).reduce((sum, val) => sum + val, 0) / boxData.length,
          validCount: Array.from(boxData).filter(v => isFinite(v)).length
        };
        
        const scoreStats = {
          min: Math.min(...scoreData),
          max: Math.max(...scoreData),
          mean: Array.from(scoreData).reduce((sum, val) => sum + val, 0) / scoreData.length,
          validCount: Array.from(scoreData).filter(v => isFinite(v)).length
        };
        
        // Data quality analysis completed (logging disabled for performance)
        
      } catch (statsError) {
        console.warn('🔧 DETECTOR RESULT: Could not analyze extracted data:', statsError);
      }
      
      // TASK 5: Memory usage summary
      const memoryUsage = {
        inputTensor: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`,
        outputBoxes: `${(boxes2D.size * 4 / 1024).toFixed(2)} KB`,
        outputLogits: `${(logits2D.size * 4 / 1024).toFixed(2)} KB`,
        totalOutput: `${((boxes2D.size + logits2D.size) * 4 / 1024).toFixed(2)} KB`
      };
      
      // Processing completed successfully (logging disabled for performance)
      
      return {
        boxes: registerTensor(boxes2D),
        logits: registerTensor(logits2D)
      };
    }
    
    // Handle multi-tensor format (fallback)
    else if (detectionTensors.length >= 2) {
      // Processing multi-tensor format
      const boxes = detectionTensors[0] as tf.Tensor2D;
      const logits = detectionTensors[1] as tf.Tensor2D;

      return {
        boxes,
        logits
      };
    }
    
    // Unsupported format
    else {
      console.warn('🔧 DETECTOR RESULT: Unsupported tensor format');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Error processing detector result:', error);
    console.error('🔧 DETECTOR RESULT: Input tensor count:', detectionTensors.length);
    if (detectionTensors.length > 0) {
      console.error('🔧 DETECTOR RESULT: Main tensor shape:', detectionTensors[0].shape);
    }
    console.error('🔧 DETECTOR RESULT: Returning zero tensors due to processing error');
    
    // Return safe fallback tensors
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }
}

/**
 * Validates detector result tensors.
 */
export function validateDetectorResult(result: DetectorResult): boolean {
  try {
    if (!result.boxes || !result.logits) {
      console.warn('🔧 DETECTOR RESULT: Missing boxes or logits tensor');
      return false;
    }

    if (result.boxes.shape.length !== 2 || result.logits.shape.length !== 2) {
      console.warn('🔧 DETECTOR RESULT: Invalid tensor dimensions');
      return false;
    }

    // Validation passed
    return true;

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Validation error:', error);
    return false;
  }
}
