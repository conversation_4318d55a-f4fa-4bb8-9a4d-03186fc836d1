client:495 [vite] connecting...
client:614 [vite] connected.
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: Object
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: Object
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: Object
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Creating anchors with config: {"numLayers":5,"aspectRatios":[1],"interpolatedScaleAspectRatio":1,"strides":[8,16,32,32,32]}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 0, scale: 0.1484, scaleNext: 0.2988, interpolated: 0.2106
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 0 - Generated 2 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 0: 28x28 (stride: 8)
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 1: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 2: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"interpolated"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 3: {"layer":0,"position":{"x":1,"y":0},"center":{"xCenter":"0.0536","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 1, scale: 0.2988, scaleNext: 0.4492, interpolated: 0.3664
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 1 - Generated 2 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 1: 14x14 (stride: 16)
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 2, scale: 0.4492, scaleNext: 0.5996, interpolated: 0.5190
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 3, scale: 0.5996, scaleNext: 0.7500, interpolated: 0.6706
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 4, scale: 0.7500, scaleNext: 1.0000, interpolated: 0.8660
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 2 - Generated 6 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 2: 7x7 (stride: 32)
create_ssd_anchors.ts:153 🔧 SSD ANCHORS: FIXED IMPLEMENTATION - Created 2254 anchors (expected: 2254 with interpolated)
detector.ts:121 🔧 DETECTOR INITIALIZATION: Anchor count verification: {"actualAnchorCount":2254,"expectedAnchorCount":2254,"configurationMatch":true}
detector.ts:134 🔧 DETECTOR INITIALIZATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:604 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0,"paused":false,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":1}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.00', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 111ms
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
favicon.ico:1  GET http://localhost:8083/favicon.ico 404 (Not Found)
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-130.8863983154297, -6.017020225524902, 30.701818466186523, 29.820510864257812, 9.098145484924316, -14.37855052947998, 19.36659812927246, 12.781549453735352, 37.6097412109375, -95.7075424194336, 29.777772903442383, 89.47322845458984, -42.88039779663086, -53.6333122253418, -17.927043914794922, 28.582067489624023, 44.45489501953125, 44.468650817871094, 23.3839168548584, 434.6966247558594, -150.37757873535156, -155.14463806152344, 70.87877655029297, 72.2372055053711, 7.230284690856934, -353.5482482910156, -319.0079040527344, -11.641759872436523, 61.3012809753418, -21.839712142944336, -35.256099700927734, -84.10852813720703, 46.76162338256836, 17.245298385620117, 43.734222412109375, -80.06254577636719, -31.22454071044922, 29.762184143066406, 23.8579158782959, -68.58247375488281, -7.925967216491699, 15.95534896850586, 42.17305374145508, 42.18927764892578, 410.4118347167969, 414.49444580078125, -176.63784790039062, -275.2091369628906, -18.49203872680664, 74.27591705322266, -469.05523681640625, -374.279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-130.8864', '-6.0170', '30.7018', '29.8205']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":1}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.47', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.474143,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.474143,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-96.96778106689453, 3.6180896759033203, 21.72270393371582, 26.665199279785156, 1.884796380996704, -7.798126220703125, 31.054607391357422, 2.5563066005706787, 41.111331939697266, -85.47771453857422, 43.1666374206543, 86.95824432373047, -34.27824020385742, -44.57831954956055, -11.76522445678711, 26.491605758666992, 44.49739074707031, 44.51177215576172, -68.12593078613281, 355.6276550292969, -131.4273681640625, -130.614501953125, 42.14735412597656, 61.73047637939453, 61.956539154052734, -284.3910827636719, -196.77426147460938, 3.7600393295288086, 64.29165649414062, -12.508182525634766, -27.77519989013672, -51.885215759277344, 46.05634689331055, 19.75429916381836, 63.29032516479492, -56.14472579956055, -8.612709999084473, 32.881832122802734, 18.91836929321289, -55.94798278808594, -3.6707823276519775, 20.749521255493164, 43.25529098510742, 43.27050018310547, 230.35336303710938, 335.2873840332031, -96.77014923095703, -248.19932556152344, -22.134557723999023, 65.52135467529297, -273.4869384765625, -300.9039611816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-96.9678', '3.6181', '21.7227', '26.6652']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-96.967781', yCenter: '3.618090', height: '21.722704', width: '26.665199', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-44.578320', yCenter: '-11.765224', height: '26.491606', width: '44.497391', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-196.774261', yCenter: '3.760039', height: '64.291656', width: '-12.508183', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":1}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.52', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.523608,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.523608,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-108.67874908447266, 1.6007264852523804, 27.23645782470703, 25.747886657714844, 4.403076648712158, -12.604763984680176, 27.367956161499023, 7.150925159454346, 41.44075012207031, -87.94752502441406, 41.42409896850586, 87.48189544677734, -36.93920135498047, -46.94729995727539, -13.412628173828125, 28.50432014465332, 44.473052978515625, 44.48723602294922, -33.12380599975586, 378.9743957519531, -141.55728149414062, -136.11795043945312, 53.3487663269043, 66.48292541503906, 40.54510498046875, -301.29302978515625, -237.16632080078125, 1.469720721244812, 64.30908203125, -16.492149353027344, -30.43757438659668, -63.2927131652832, 45.76243209838867, 21.47026824951172, 58.656982421875, -63.831634521484375, -13.881585121154785, 30.654815673828125, 21.118745803833008, -61.09508514404297, -5.471351623535156, 19.069887161254883, 42.87097930908203, 42.88655090332031, 286.6943664550781, 362.469970703125, -126.06890869140625, -261.565185546875, -22.14715576171875, 67.94843292236328, -337.9342956542969, -328.17529296875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-108.6787', '1.6007', '27.2365', '25.7479']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-108.678749', yCenter: '1.600726', height: '27.236458', width: '25.747887', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-46.947300', yCenter: '-13.412628', height: '28.504320', width: '44.473053', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-237.166321', yCenter: '1.469721', height: '64.309082', width: '-16.492149', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.569226,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.569226,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:604 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.57', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.572841,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.572841,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-96.17166137695312, 0.7779648900032043, 21.457332611083984, 24.410385131835938, 4.413607597351074, -6.728487491607666, 30.06456184387207, 1.7833492755889893, 40.34856414794922, -84.2351303100586, 43.07562255859375, 86.24786376953125, -35.614967346191406, -44.208560943603516, -10.396217346191406, 28.429534912109375, 44.485557556152344, 44.49988555908203, -78.7795639038086, 348.08837890625, -120.04328918457031, -121.51940155029297, 43.996822357177734, 62.13920593261719, 78.95075988769531, -273.435791015625, -192.58584594726562, 0.4322059452533722, 61.61865234375, -13.989748001098633, -25.323755264282227, -50.90928649902344, 45.359439849853516, 17.477306365966797, 61.77622985839844, -57.128074645996094, -7.847626686096191, 32.02854919433594, 16.22237205505371, -55.389163970947266, -2.840447425842285, 22.363842010498047, 43.291847229003906, 43.30692672729492, 220.0712890625, 329.49591064453125, -92.9949722290039, -238.05699157714844, -20.948532104492188, 63.79200744628906, -261.4438781738281, -294.5377502441406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-96.1717', '0.7780', '21.4573', '24.4104']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-96.171661', yCenter: '0.777965', height: '21.457333', width: '24.410385', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-44.208561', yCenter: '-10.396217', height: '28.429535', width: '44.485558', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-192.585846', yCenter: '0.432206', height: '61.618652', width: '-13.989748', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.62', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.624279,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.624279,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-116.46211242675781, 0.29416272044181824, 30.388273239135742, 27.232494354248047, 4.77235221862793, -13.438980102539062, 25.61029624938965, 11.02080249786377, 41.684242248535156, -88.56101989746094, 37.30879592895508, 87.42111206054688, -36.143497467041016, -50.92005920410156, -15.363595962524414, 27.458200454711914, 44.46733474731445, 44.481346130371094, -4.434924125671387, 405.89068603515625, -140.19638061523438, -150.76043701171875, 61.11369705200195, 69.81212615966797, 25.3893985748291, -327.4649658203125, -268.3204345703125, -3.3490192890167236, 65.66580963134766, -18.00420379638672, -32.411216735839844, -71.1174545288086, 48.25983428955078, 20.489139556884766, 53.87913131713867, -69.79838562011719, -18.331947326660156, 31.533662796020508, 23.084653854370117, -63.29560470581055, -6.232173442840576, 17.96857452392578, 42.644535064697266, 42.66032409667969, 335.63592529296875, 395.6195373535156, -140.6066131591797, -273.6255187988281, -17.081584930419922, 73.56800079345703, -382.0170593261719, -355.82049560546875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-116.4621', '0.2942', '30.3883', '27.2325']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-116.462112', yCenter: '0.294163', height: '30.388273', width: '27.232494', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.920059', yCenter: '-15.363596', height: '27.458200', width: '44.467335', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-268.320435', yCenter: '-3.349019', height: '65.665810', width: '-18.004204', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.67', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.670684,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.670684,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-117.89212799072266, -0.21854479610919952, 31.124792098999023, 27.20635223388672, 5.206475257873535, -12.564495086669922, 24.68413543701172, 12.108773231506348, 41.06985855102539, -88.01708984375, 36.03252029418945, 87.20654296875, -36.54053497314453, -51.13821029663086, -15.772171020507812, 27.491350173950195, 44.47635269165039, 44.49028396606445, -7.259226322174072, 411.9968566894531, -132.94741821289062, -155.29183959960938, 62.7281494140625, 70.40380859375, 33.229400634765625, -334.30218505859375, -272.93865966796875, -4.10693883895874, 66.013916015625, -17.549718856811523, -33.603328704833984, -71.84971618652344, 49.10337448120117, 19.579608917236328, 51.80807113647461, -72.12510681152344, -19.669448852539062, 32.748748779296875, 22.8603458404541, -63.85148620605469, -7.016732692718506, 17.500476837158203, 42.685089111328125, 42.700775146484375, 344.27130126953125, 400.4693298339844, -142.3201446533203, -276.4391174316406, -15.156696319580078, 73.79688262939453, -388.0244140625, -361.3395690917969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.8921', '-0.2185', '31.1248', '27.2064']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.892128', yCenter: '-0.218545', height: '31.124792', width: '27.206352', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-51.138210', yCenter: '-15.772171', height: '27.491350', width: '44.476353', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-272.938660', yCenter: '-4.106939', height: '66.013916', width: '-17.549719', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.72', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.724898,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.724898,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-133.63211059570312, -3.316202163696289, 32.7865104675293, 30.601858139038086, 8.203906059265137, -15.943269729614258, 19.035755157470703, 15.026623725891113, 38.01875686645508, -94.64864349365234, 32.21553421020508, 89.92042541503906, -41.6840705871582, -54.4339714050293, -18.705211639404297, 27.13045310974121, 44.454505920410156, 44.468292236328125, 33.174339294433594, 444.45849609375, -159.01992797851562, -160.90113830566406, 74.47903442382812, 73.41034698486328, -0.25943127274513245, -363.0469665527344, -326.74029541015625, -9.634781837463379, 63.22700500488281, -22.828779220581055, -36.31913757324219, -86.81327056884766, 47.014156341552734, 18.730955123901367, 43.213226318359375, -79.46146392822266, -30.532684326171875, 28.63565444946289, 25.808340072631836, -69.8901138305664, -7.905246734619141, 14.742258071899414, 42.08802032470703, 42.10442352294922, 422.5950927734375, 426.5470275878906, -183.4774932861328, -282.3277893066406, -17.101051330566406, 76.54630279541016, -480.77471923828125, -384.8578796386719]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-133.6321', '-3.3162', '32.7865', '30.6019']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-133.632111', yCenter: '-3.316202', height: '32.786510', width: '30.601858', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-54.433971', yCenter: '-18.705212', height: '27.130453', width: '44.454506', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-326.740295', yCenter: '-9.634782', height: '63.227005', width: '-22.828779', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.795213,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.795213,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:604 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.79', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.799008,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.799008,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-161.52500915527344, -4.175033092498779, 36.3852424621582, 37.43192672729492, 6.1599273681640625, -24.95196533203125, 15.419819831848145, 17.94001007080078, 35.41494369506836, -110.129150390625, 26.49599266052246, 98.33189392089844, -48.28162384033203, -60.48078536987305, -23.718469619750977, 26.4218807220459, 44.404293060302734, 44.418094635009766, 120.6304931640625, 466.9261169433594, -217.0728302001953, -172.04273986816406, 90.00537109375, 72.61479187011719, -85.0800552368164, -389.5148010253906, -416.6354675292969, -12.685158729553223, 65.5246810913086, -25.717594146728516, -46.29327392578125, -114.27491760253906, 46.33067321777344, 22.532501220703125, 34.57192611694336, -95.5654525756836, -47.1557731628418, 29.530033111572266, 31.46108055114746, -81.05899810791016, -11.963303565979004, 9.7288179397583, 41.16461181640625, 41.182228088378906, 633.4957275390625, 438.7504577636719, -278.6752014160156, -297.85699462890625, 1.891883134841919, 79.1089859008789, -688.5208740234375, -398.5279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-161.5250', '-4.1750', '36.3852', '37.4319']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-161.525009', yCenter: '-4.175033', height: '36.385242', width: '37.431927', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-60.480785', yCenter: '-23.718470', height: '26.421881', width: '44.404293', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-416.635468', yCenter: '-12.685159', height: '65.524681', width: '-25.717594', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.85', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.853616,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.853616,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-117.99117279052734, -3.527639150619507, 27.892414093017578, 27.931434631347656, 7.451789379119873, -12.78081226348877, 23.776412963867188, 9.38687801361084, 38.555755615234375, -90.11808013916016, 34.73429870605469, 88.2047119140625, -38.25580978393555, -50.41459655761719, -15.248403549194336, 28.374011993408203, 44.469688415527344, 44.483673095703125, -7.579749584197998, 416.6793518066406, -141.35006713867188, -147.3075408935547, 60.68023681640625, 70.57465362548828, 26.330175399780273, -335.5982971191406, -273.94537353515625, -6.585906982421875, 61.47150802612305, -17.795862197875977, -31.871381759643555, -72.67665100097656, 46.962371826171875, 18.351831436157227, 50.49949264526367, -71.62909698486328, -23.3612060546875, 30.44928741455078, 24.028156280517578, -63.75605773925781, -6.645132541656494, 16.769927978515625, 42.717811584472656, 42.73344421386719, 339.578125, 403.24066162109375, -147.29598999023438, -269.7014465332031, -23.211257934570312, 72.99738311767578, -397.7270202636719, -362.9508361816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.9912', '-3.5276', '27.8924', '27.9314']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.991173', yCenter: '-3.527639', height: '27.892414', width: '27.931435', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.414597', yCenter: '-15.248404', height: '28.374012', width: '44.469688', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-273.945374', yCenter: '-6.585907', height: '61.471508', width: '-17.795862', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.90', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]0: 11: 2242: 2243: 3length: 4[[Prototype]]: Array(0)
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.899991,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.899991,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-118.2226333618164, -0.9667824506759644, 27.823287963867188, 28.292438507080078, 6.301304340362549, -14.235280990600586, 24.687408447265625, 8.82028579711914, 38.515464782714844, -91.42886352539062, 37.62111282348633, 89.18975830078125, -38.4666748046875, -50.71095275878906, -15.411734580993652, 28.059646606445312, 44.460060119628906, 44.474151611328125, -2.0591320991516113, 410.9268798828125, -154.3023223876953, -144.9470977783203, 61.0676383972168, 69.62452697753906, 16.473987579345703, -330.3340759277344, -278.58935546875, -3.7968122959136963, 63.380828857421875, -18.8704891204834, -31.748062133789062, -73.9017562866211, 46.62026596069336, 19.777339935302734, 51.77473831176758, -71.87771606445312, -21.111812591552734, 29.747953414916992, 23.680055618286133, -64.94073486328125, -6.4539031982421875, 17.402196884155273, 42.53852844238281, 42.554466247558594, 345.39410400390625, 400.7725830078125, -154.57028198242188, -269.9444580078125, -21.76801872253418, 73.10189819335938, -403.843994140625, -360.01019287109375]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-118.2226', '-0.9668', '27.8233', '28.2924']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-118.222633', yCenter: '-0.966782', height: '27.823288', width: '28.292439', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.710953', yCenter: '-15.411735', height: '28.059647', width: '44.460060', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-278.589355', yCenter: '-3.796812', height: '63.380829', width: '-18.870489', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}rawBoxTensor: constructor: "Tensor"isTensor: trueshape: (2) [2254, 4]type: "object"[[Prototype]]: ObjectrawScoreTensor: constructor: "Tensor"isTensor: trueshape: (2) [2254, 1]type: "object"[[Prototype]]: Object[[Prototype]]: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:304 🔴 BASELINE TEST: Red "+" indicator drawn at center {"centerX":285,"centerY":506.5,"canvasSize":"570x1013"}
SideViewBlazePoseOverlay.tsx:327 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.96', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.962764,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.962764,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-104.14421081542969, -0.5565630197525024, 24.71101188659668, 25.59415054321289, 5.170046806335449, -10.556075096130371, 29.236053466796875, 4.351587772369385, 40.80064392089844, -87.31981658935547, 41.037818908691406, 87.29914093017578, -35.5989990234375, -46.623313903808594, -12.173044204711914, 28.45753288269043, 44.4766845703125, 44.49094009399414, -46.25608444213867, 380.22491455078125, -133.180908203125, -133.0860595703125, 50.0676383972168, 66.92786407470703, 51.64769744873047, -300.8127746582031, -225.4937286376953, -1.5703885555267334, 63.69719696044922, -15.250652313232422, -27.288000106811523, -60.133262634277344, 46.79068374633789, 18.788734436035156, 59.99374008178711, -62.79758071899414, -13.138731002807617, 31.40743637084961, 20.252559661865234, -59.27878952026367, -4.23896598815918, 20.92066764831543, 42.96674346923828, 42.98228454589844, 269.6730651855469, 367.72491455078125, -116.19718933105469, -254.27256774902344, -21.967052459716797, 69.7855224609375, -318.0685729980469, -327.6724548339844]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-104.1442', '-0.5566', '24.7110', '25.5942']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-104.144211', yCenter: '-0.556563', height: '24.711012', width: '25.594151', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-46.623314', yCenter: '-12.173044', height: '28.457533', width: '44.476685', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-225.493729', yCenter: '-1.570389', height: '63.697197', width: '-15.250652', rawIndex: 8}height: "63.697197"rawIndex: 8width: "-15.250652"xCenter: "-225.493729"yCenter: "-1.570389"[[Prototype]]: Object
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:335
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:593
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:592
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:605
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:603
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:344 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.998888,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:610 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.998888,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/be4459b0-2253-44f6-a961..."}
SideViewBlazePoseOverlay.tsx:602 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":3.449648,"paused":true,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:604 🚀 DETECTION LOOP: Starting pose detection loop now!
