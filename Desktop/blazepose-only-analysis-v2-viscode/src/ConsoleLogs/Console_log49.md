client:495 [vite] connecting...
client:614 [vite] connected.
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: {feet: 5, inches: 10}
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: {backend: 'webgl', memory: {…}, platform: PlatformBrowser}
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: {modelType: 'full', enableSmoothing: true, enableSegmentation: false, detectorUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...', landmarkUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...'}
create_ssd_anchors.ts:44 🔧 SSD ANCHORS: Creating anchors with config: {reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: Array(0), featureMapWidth: Array(0), numLayers: 5, …}
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Feature map dimensions: {heights: Array(0), widths: Array(0), numLayers: 5, aspectRatios: Array(1)}
create_ssd_anchors.ts:53 🔧 SSD ANCHORS: CRITICAL DEBUG - Anchor offsets: {anchorOffsetX: 0.5, anchorOffsetY: 0.5, potentialForNegativeX: false, potentialForNegativeY: false}
create_ssd_anchors.ts:78 🔧 SSD ANCHORS: Layer 0: 28x28 (stride: 8)
create_ssd_anchors.ts:98 🔧 SSD ANCHOR 1: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:98 🔧 SSD ANCHOR 2: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:98 🔧 SSD ANCHOR 3: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:98 🔧 SSD ANCHOR 4: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:98 🔧 SSD ANCHOR 5: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:78 🔧 SSD ANCHORS: Layer 1: 14x14 (stride: 16)
create_ssd_anchors.ts:78 🔧 SSD ANCHORS: Layer 2: 7x7 (stride: 32)
create_ssd_anchors.ts:78 🔧 SSD ANCHORS: Layer 3: 7x7 (stride: 32)
create_ssd_anchors.ts:78 🔧 SSD ANCHORS: Layer 4: 7x7 (stride: 32)
create_ssd_anchors.ts:129 🔧 SSD ANCHORS: Created 1127 anchors
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0.010438,"paused":false,"videoWidth":1080,"videoHeight":1920}
favicon.ico:1  GET http://localhost:8082/favicon.ico 404 (Not Found)
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.02', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 74ms
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.089522,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.089522,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-123.95761108398438, -5.478638172149658, 29.46474266052246, 28.517230987548828, 8.389588356018066, -11.956067085266113, 20.538293838500977, 11.77538776397705, 37.83790969848633, -91.94666290283203, 30.82642936706543, 88.1690673828125, -41.06968688964844, -52.05326461791992, -16.657548904418945, 28.49187469482422, 44.471435546875, 44.485191345214844, -3.5906333923339844, 420.38677978515625, -133.6934356689453, -150.2481231689453, 65.1270980834961, 70.340576171875, 31.47983169555664, -341.067626953125, -294.7553405761719, -10.202412605285645, 60.487632751464844, -20.847822189331055, -33.98816680908203, -76.803955078125, 46.41505432128906, 17.405719757080078, 45.623878479003906, -74.04950714111328, -27.2985782623291, 29.71405029296875, 22.963857650756836, -65.27828979492188, -6.9575934410095215, 16.58053207397461, 42.46106719970703, 42.476844787597656, 360.1613464355469, 403.6128845214844, -148.1473846435547, -269.49176025390625, -20.608787536621094, 73.13468170166016, -413.6933898925781, -363.759033203125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-123.9576', '-5.4786', '29.4647', '28.5172']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.63', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.634751,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.634751,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-146.38720703125, -6.186207294464111, 35.71052932739258, 33.42029571533203, 7.68263578414917, -18.305234909057617, 16.45728874206543, 17.799081802368164, 39.35396194458008, -100.84861755371094, 25.170133590698242, 93.52276611328125, -44.672767639160156, -58.16096496582031, -20.948463439941406, 28.04294776916504, 44.41825866699219, 44.43196487426758, 69.83970642089844, 441.1869201660156, -169.21987915039062, -166.60305786132812, 79.24964904785156, 69.36608123779297, -32.42168426513672, -367.84478759765625, -371.46783447265625, -15.853527069091797, 63.22080612182617, -24.788328170776367, -40.14929962158203, -99.50363159179688, 47.16977310180664, 19.085556030273438, 39.91462707519531, -87.77848815917969, -42.669677734375, 30.761709213256836, 28.040531158447266, -74.34675598144531, -9.260086059570312, 14.257806777954102, 41.70663833618164, 41.7235221862793, 543.90869140625, 409.8170471191406, -228.447265625, -284.7286071777344, -6.70304012298584, 73.75554656982422, -597.2178344726562, -373.50811767578125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-146.3872', '-6.1862', '35.7105', '33.4203']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-146.387207', yCenter: '-6.186207', height: '35.710529', width: '33.420296', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-58.160965', yCenter: '-20.948463', height: '28.042948', width: '44.418259', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-371.467834', yCenter: '-15.853527', height: '63.220806', width: '-24.788328', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.677747,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.677747,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.68', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.680623,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.680623,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-117.89212799072266, -0.21854479610919952, 31.124792098999023, 27.20635223388672, 5.206475257873535, -12.564495086669922, 24.68413543701172, 12.108773231506348, 41.06985855102539, -88.01708984375, 36.03252029418945, 87.20654296875, -36.54053497314453, -51.13821029663086, -15.772171020507812, 27.491350173950195, 44.47635269165039, 44.49028396606445, -7.259226322174072, 411.9968566894531, -132.94741821289062, -155.29183959960938, 62.7281494140625, 70.40380859375, 33.229400634765625, -334.30218505859375, -272.93865966796875, -4.10693883895874, 66.013916015625, -17.549718856811523, -33.603328704833984, -71.84971618652344, 49.10337448120117, 19.579608917236328, 51.80807113647461, -72.12510681152344, -19.669448852539062, 32.748748779296875, 22.8603458404541, -63.85148620605469, -7.016732692718506, 17.500476837158203, 42.685089111328125, 42.700775146484375, 344.27130126953125, 400.4693298339844, -142.3201446533203, -276.4391174316406, -15.156696319580078, 73.79688262939453, -388.0244140625, -361.3395690917969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.8921', '-0.2185', '31.1248', '27.2064']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.892128', yCenter: '-0.218545', height: '31.124792', width: '27.206352', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-51.138210', yCenter: '-15.772171', height: '27.491350', width: '44.476353', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-272.938660', yCenter: '-4.106939', height: '66.013916', width: '-17.549719', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.74', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.739062,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.739062,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-133.63211059570312, -3.316202163696289, 32.7865104675293, 30.601858139038086, 8.203906059265137, -15.943269729614258, 19.035755157470703, 15.026623725891113, 38.01875686645508, -94.64864349365234, 32.21553421020508, 89.92042541503906, -41.6840705871582, -54.4339714050293, -18.705211639404297, 27.13045310974121, 44.454505920410156, 44.468292236328125, 33.174339294433594, 444.45849609375, -159.01992797851562, -160.90113830566406, 74.47903442382812, 73.41034698486328, -0.25943127274513245, -363.0469665527344, -326.74029541015625, -9.634781837463379, 63.22700500488281, -22.828779220581055, -36.31913757324219, -86.81327056884766, 47.014156341552734, 18.730955123901367, 43.213226318359375, -79.46146392822266, -30.532684326171875, 28.63565444946289, 25.808340072631836, -69.8901138305664, -7.905246734619141, 14.742258071899414, 42.08802032470703, 42.10442352294922, 422.5950927734375, 426.5470275878906, -183.4774932861328, -282.3277893066406, -17.101051330566406, 76.54630279541016, -480.77471923828125, -384.8578796386719]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-133.6321', '-3.3162', '32.7865', '30.6019']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-133.632111', yCenter: '-3.316202', height: '32.786510', width: '30.601858', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-54.433971', yCenter: '-18.705212', height: '27.130453', width: '44.454506', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-326.740295', yCenter: '-9.634782', height: '63.227005', width: '-22.828779', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.82', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.820049,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.820049,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-112.0095443725586, 0.12835945188999176, 30.12190818786621, 24.84273338317871, 4.199545860290527, -14.287402153015137, 27.17662811279297, 9.6307373046875, 42.112789154052734, -86.29190826416016, 39.510711669921875, 86.58361053466797, -34.93535232543945, -47.78221130371094, -13.669992446899414, 27.71477508544922, 44.47733688354492, 44.491363525390625, -18.350605010986328, 395.91839599609375, -133.2026824951172, -146.38900756835938, 57.60898971557617, 69.28041076660156, 36.70830535888672, -317.2321472167969, -249.92955017089844, -1.2959030866622925, 64.78804779052734, -17.20775604248047, -31.61674690246582, -67.14581298828125, 46.91744613647461, 21.38733673095703, 57.00569152832031, -65.97152709960938, -16.04274559020996, 31.118587493896484, 22.614728927612305, -62.2169075012207, -5.754645347595215, 18.05815315246582, 42.830970764160156, 42.84654235839844, 304.6419677734375, 379.8472595214844, -129.69375610351562, -269.7278747558594, -21.05126190185547, 70.58094024658203, -354.40911865234375, -344.1356506347656]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-112.0095', '0.1284', '30.1219', '24.8427']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-112.009544', yCenter: '0.128359', height: '30.121908', width: '24.842733', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-47.782211', yCenter: '-13.669992', height: '27.714775', width: '44.477337', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-249.929550', yCenter: '-1.295903', height: '64.788048', width: '-17.207756', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.86', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.858752,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.858752,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-95.42677307128906, 0.5907348990440369, 23.28662872314453, 22.629358291625977, 3.1545844078063965, -8.563335418701172, 30.48284912109375, 2.7987172603607178, 40.33057403564453, -81.04092407226562, 42.3203239440918, 85.24686431884766, -33.3404426574707, -42.579368591308594, -9.740514755249023, 27.862478256225586, 44.494163513183594, 44.508384704589844, -71.69239044189453, 347.1804504394531, -112.47891235351562, -125.60334014892578, 44.01390075683594, 61.843143463134766, 76.17192840576172, -274.4905700683594, -188.93304443359375, 1.8686693906784058, 62.00962448120117, -13.581527709960938, -26.96289825439453, -50.27402877807617, 44.711700439453125, 19.151668548583984, 61.870025634765625, -54.14381790161133, -9.282466888427734, 31.20501136779785, 17.869606018066406, -55.579898834228516, -3.3402321338653564, 20.85743522644043, 43.45159912109375, 43.46638870239258, 221.0910186767578, 321.9602355957031, -87.0864028930664, -241.82957458496094, -22.058574676513672, 62.47393035888672, -261.1626281738281, -290.9153747558594]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-95.4268', '0.5907', '23.2866', '22.6294']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-95.426773', yCenter: '0.590735', height: '23.286629', width: '22.629358', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-42.579369', yCenter: '-9.740515', height: '27.862478', width: '44.494164', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-188.933044', yCenter: '1.868669', height: '62.009624', width: '-13.581528', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.902777,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.902777,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.90', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.908332,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.908332,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-117.99117279052734, -3.527639150619507, 27.892414093017578, 27.931434631347656, 7.451789379119873, -12.78081226348877, 23.776412963867188, 9.38687801361084, 38.555755615234375, -90.11808013916016, 34.73429870605469, 88.2047119140625, -38.25580978393555, -50.41459655761719, -15.248403549194336, 28.374011993408203, 44.469688415527344, 44.483673095703125, -7.579749584197998, 416.6793518066406, -141.35006713867188, -147.3075408935547, 60.68023681640625, 70.57465362548828, 26.330175399780273, -335.5982971191406, -273.94537353515625, -6.585906982421875, 61.47150802612305, -17.795862197875977, -31.871381759643555, -72.67665100097656, 46.962371826171875, 18.351831436157227, 50.49949264526367, -71.62909698486328, -23.3612060546875, 30.44928741455078, 24.028156280517578, -63.75605773925781, -6.645132541656494, 16.769927978515625, 42.717811584472656, 42.73344421386719, 339.578125, 403.24066162109375, -147.29598999023438, -269.7014465332031, -23.211257934570312, 72.99738311767578, -397.7270202636719, -362.9508361816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.9912', '-3.5276', '27.8924', '27.9314']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.991173', yCenter: '-3.527639', height: '27.892414', width: '27.931435', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.414597', yCenter: '-15.248404', height: '28.374012', width: '44.469688', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-273.945374', yCenter: '-6.585907', height: '61.471508', width: '-17.795862', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.97', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.968686,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.968686,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-111.15109252929688, -0.7564656138420105, 28.02439308166504, 24.996166229248047, 4.174032211303711, -12.447470664978027, 27.1287784576416, 8.378366470336914, 40.641822814941406, -86.77392578125, 38.26259994506836, 86.99799346923828, -35.17338180541992, -48.21865463256836, -13.446538925170898, 27.8856258392334, 44.46710205078125, 44.48115539550781, -31.395570755004883, 386.2037658691406, -126.84266662597656, -140.88597106933594, 54.269798278808594, 66.38798522949219, 46.46969223022461, -310.2815856933594, -252.24339294433594, -2.5631895065307617, 63.514041900634766, -16.9388484954834, -31.11656951904297, -65.35975646972656, 46.21685028076172, 20.570419311523438, 56.51769256591797, -65.52603149414062, -17.55021095275879, 31.2783203125, 21.601497650146484, -61.97418212890625, -5.870743751525879, 18.961612701416016, 42.768699645996094, 42.7843132019043, 300.9669189453125, 370.0926208496094, -125.14498138427734, -262.41143798828125, -21.541629791259766, 69.0040054321289, -350.1705017089844, -334.3716125488281]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-111.1511', '-0.7565', '28.0244', '24.9962']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-111.151093', yCenter: '-0.756466', height: '28.024393', width: '24.996166', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-48.218655', yCenter: '-13.446539', height: '27.885626', width: '44.467102', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-252.243393', yCenter: '-2.563190', height: '63.514042', width: '-16.938848', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.02', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.017717,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.017717,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-125.08766174316406, 0.6526203751564026, 30.536670684814453, 30.526002883911133, 6.078330039978027, -15.33211612701416, 24.604949951171875, 11.082527160644531, 40.75889205932617, -94.9222183227539, 37.16621780395508, 90.00135803222656, -38.15363693237305, -52.847015380859375, -17.319942474365234, 27.436954498291016, 44.45170211791992, 44.465843200683594, 13.728985786437988, 429.4867248535156, -165.47874450683594, -153.78921508789062, 64.08243560791016, 71.55418395996094, 1.3736679553985596, -348.1347351074219, -297.14410400390625, -4.354240417480469, 67.2618408203125, -18.440025329589844, -32.56941604614258, -79.72525024414062, 49.24091339111328, 20.43701171875, 51.52667236328125, -78.18669128417969, -20.653364181518555, 31.681079864501953, 25.163169860839844, -67.14319610595703, -7.514141082763672, 17.098024368286133, 42.34559631347656, 42.36190414428711, 380.368896484375, 428.687255859375, -174.4801025390625, -279.5795593261719, -15.994836807250977, 77.44305419921875, -436.2039794921875, -383.1202392578125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-125.0877', '0.6526', '30.5367', '30.5260']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-125.087662', yCenter: '0.652620', height: '30.536671', width: '30.526003', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-52.847015', yCenter: '-17.319942', height: '27.436954', width: '44.451702', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-297.144104', yCenter: '-4.354240', height: '67.261841', width: '-18.440025', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.05', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.055926,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.055926,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-177.42115783691406, -4.615962505340576, 38.02428436279297, 41.69137191772461, 4.158482551574707, -26.821186065673828, 11.605700492858887, 21.992429733276367, 33.792144775390625, -114.1332015991211, 20.425750732421875, 103.37533569335938, -50.775840759277344, -62.91486358642578, -26.415550231933594, 25.148488998413086, 44.385032653808594, 44.39875793457031, 155.8630828857422, 469.0916748046875, -229.7808837890625, -179.35064697265625, 98.32652282714844, 74.05278778076172, -111.87948608398438, -392.7686767578125, -464.96197509765625, -13.455986976623535, 62.448543548583984, -25.239633560180664, -54.19507598876953, -125.0434799194336, 44.54443359375, 25.79832649230957, 28.260807037353516, -101.12738037109375, -59.15517807006836, 34.6739501953125, 36.69972229003906, -86.42832946777344, -13.718148231506348, 4.879421234130859, 40.708404541015625, 40.72669219970703, 737.0289306640625, 422.9454040527344, -321.1348876953125, -303.75653076171875, 10.16748332977295, 82.2681884765625, -789.98291015625, -382.2032775878906]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-177.4212', '-4.6160', '38.0243', '41.6914']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-177.421158', yCenter: '-4.615963', height: '38.024284', width: '41.691372', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-62.914864', yCenter: '-26.415550', height: '25.148489', width: '44.385033', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-464.961975', yCenter: '-13.455987', height: '62.448544', width: '-25.239634', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 1127.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.097565,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.097565,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/7f875398-0bda-4881-8521..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":4.046431,"paused":true,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
