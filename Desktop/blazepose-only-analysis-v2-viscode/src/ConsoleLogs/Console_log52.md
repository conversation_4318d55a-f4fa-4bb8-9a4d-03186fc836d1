client:495 [vite] connecting...
client:614 [vite] connected.
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: Object
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: Object
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: Object
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Creating anchors with config: {"numLayers":5,"aspectRatios":[1],"interpolatedScaleAspectRatio":1,"strides":[8,16,32,32,32]}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 0, scale: 0.1484, scaleNext: 0.2988, interpolated: 0.2106
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 0 - Generated 2 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 0: 28x28 (stride: 8)
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 1: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 2: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"interpolated"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 3: {"layer":0,"position":{"x":1,"y":0},"center":{"xCenter":"0.0536","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 1, scale: 0.2988, scaleNext: 0.4492, interpolated: 0.3664
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 1 - Generated 2 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 1: 14x14 (stride: 16)
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 2, scale: 0.4492, scaleNext: 0.5996, interpolated: 0.5190
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 3, scale: 0.5996, scaleNext: 0.7500, interpolated: 0.6706
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 4, scale: 0.7500, scaleNext: 1.0000, interpolated: 0.8660
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 2 - Generated 6 anchor types (1 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 2: 7x7 (stride: 32)
create_ssd_anchors.ts:153 🔧 SSD ANCHORS: FIXED IMPLEMENTATION - Created 2254 anchors (expected: 2254 with interpolated)
detector.ts:121 🔧 DETECTOR INITIALIZATION: Anchor count verification: {"actualAnchorCount":2254,"expectedAnchorCount":2254,"configurationMatch":true}
detector.ts:134 🔧 DETECTOR INITIALIZATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.00', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 101ms
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0,"paused":false,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
favicon.ico:1  GET http://localhost:8083/favicon.ico 404 (Not Found)
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-130.8863983154297, -6.017020225524902, 30.701818466186523, 29.820510864257812, 9.098145484924316, -14.37855052947998, 19.36659812927246, 12.781549453735352, 37.6097412109375, -95.7075424194336, 29.777772903442383, 89.47322845458984, -42.88039779663086, -53.6333122253418, -17.927043914794922, 28.582067489624023, 44.45489501953125, 44.468650817871094, 23.3839168548584, 434.6966247558594, -150.37757873535156, -155.14463806152344, 70.87877655029297, 72.2372055053711, 7.230284690856934, -353.5482482910156, -319.0079040527344, -11.641759872436523, 61.3012809753418, -21.839712142944336, -35.256099700927734, -84.10852813720703, 46.76162338256836, 17.245298385620117, 43.734222412109375, -80.06254577636719, -31.22454071044922, 29.762184143066406, 23.8579158782959, -68.58247375488281, -7.925967216491699, 15.95534896850586, 42.17305374145508, 42.18927764892578, 410.4118347167969, 414.49444580078125, -176.63784790039062, -275.2091369628906, -18.49203872680664, 74.27591705322266, -469.05523681640625, -374.279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-130.8864', '-6.0170', '30.7018', '29.8205']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.41', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.413738,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.413738,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-106.63321685791016, 3.148637294769287, 23.300329208374023, 28.52560043334961, 3.1410837173461914, -10.416458129882812, 28.29393768310547, 4.454567909240723, 39.658714294433594, -88.42522430419922, 42.09817123413086, 89.3663101196289, -37.06324005126953, -46.38139724731445, -13.379651069641113, 27.092805862426758, 44.47301483154297, 44.48739242553711, -43.01441955566406, 371.6293640136719, -150.04185485839844, -135.56275939941406, 48.26823425292969, 63.19979476928711, 38.86061096191406, -299.0610046386719, -232.49444580078125, 3.1617114543914795, 63.488712310791016, -13.926131248474121, -30.41487693786621, -60.84623336791992, 46.117408752441406, 20.20177459716797, 59.92670822143555, -61.77776336669922, -13.430465698242188, 32.85702133178711, 21.897979736328125, -60.28710174560547, -4.67203426361084, 18.75572967529297, 42.86338806152344, 42.87921142578125, 279.72161865234375, 355.51104736328125, -130.3157958984375, -260.1314392089844, -25.667356491088867, 67.24800872802734, -337.9345397949219, -321.8534240722656]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-106.6332', '3.1486', '23.3003', '28.5256']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-106.633217', yCenter: '3.148637', height: '23.300329', width: '28.525600', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-46.381397', yCenter: '-13.379651', height: '27.092806', width: '44.473015', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-232.494446', yCenter: '3.161711', height: '63.488712', width: '-13.926131', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.473038,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.473038,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.48', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.4872,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.4872,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-102.36202239990234, -1.2599942684173584, 25.361629486083984, 23.83538055419922, 4.571991443634033, -9.832160949707031, 27.97359275817871, 5.324705123901367, 39.52388000488281, -84.0289306640625, 38.52405548095703, 85.6294174194336, -35.16689682006836, -46.25, -11.713632583618164, 28.47246551513672, 44.48151779174805, 44.49562072753906, -51.234405517578125, 367.8866271972656, -117.17552947998047, -133.555908203125, 49.04515075683594, 64.3158950805664, 61.81832504272461, -293.5038146972656, -220.1666717529297, -1.5506037473678589, 60.97774887084961, -16.206279754638672, -28.864429473876953, -57.32918167114258, 44.74147033691406, 19.259037017822266, 56.855831146240234, -58.53314971923828, -14.998188018798828, 29.974111557006836, 19.382280349731445, -57.820709228515625, -4.554573059082031, 19.99020004272461, 43.216796875, 43.23177719116211, 257.8985290527344, 347.3937072753906, -101.11912536621094, -252.71954345703125, -22.896947860717773, 66.41924285888672, -302.2909851074219, -313.5332946777344]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-102.3620', '-1.2600', '25.3616', '23.8354']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-102.362022', yCenter: '-1.259994', height: '25.361629', width: '23.835381', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-46.250000', yCenter: '-11.713633', height: '28.472466', width: '44.481518', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-220.166672', yCenter: '-1.550604', height: '60.977749', width: '-16.206280', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.54', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.543567,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.543567,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-100.54286193847656, -1.8078943490982056, 25.162094116210938, 23.605398178100586, 4.131378650665283, -9.920692443847656, 29.209426879882812, 4.661080360412598, 40.114501953125, -83.30487823486328, 38.71304702758789, 86.08970642089844, -33.9790153503418, -45.561981201171875, -11.006872177124023, 28.186580657958984, 44.47865295410156, 44.492820739746094, -53.28453063964844, 368.0223083496094, -114.15869903564453, -134.9575653076172, 47.96296310424805, 64.24798583984375, 63.684478759765625, -294.20867919921875, -214.9480438232422, -1.8982986211776733, 61.625736236572266, -14.83452033996582, -28.23164176940918, -56.191078186035156, 45.76383972167969, 18.779983520507812, 58.438663482666016, -58.37299346923828, -13.918606758117676, 32.125396728515625, 19.60692024230957, -57.28696060180664, -4.066619396209717, 20.311073303222656, 43.19164276123047, 43.206756591796875, 254.29043579101562, 346.86468505859375, -100.0875473022461, -252.6057891845703, -22.07219123840332, 66.69374084472656, -297.2992858886719, -312.362060546875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-100.5429', '-1.8079', '25.1621', '23.6054']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-100.542862', yCenter: '-1.807894', height: '25.162094', width: '23.605398', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-45.561981', yCenter: '-11.006872', height: '28.186581', width: '44.478653', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-214.948044', yCenter: '-1.898299', height: '61.625736', width: '-14.834520', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.59', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.59244,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.59244,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-96.17166137695312, 0.7779648900032043, 21.457332611083984, 24.410385131835938, 4.413607597351074, -6.728487491607666, 30.06456184387207, 1.7833492755889893, 40.34856414794922, -84.2351303100586, 43.07562255859375, 86.24786376953125, -35.614967346191406, -44.208560943603516, -10.396217346191406, 28.429534912109375, 44.485557556152344, 44.49988555908203, -78.7795639038086, 348.08837890625, -120.04328918457031, -121.51940155029297, 43.996822357177734, 62.13920593261719, 78.95075988769531, -273.435791015625, -192.58584594726562, 0.4322059452533722, 61.61865234375, -13.989748001098633, -25.323755264282227, -50.90928649902344, 45.359439849853516, 17.477306365966797, 61.77622985839844, -57.128074645996094, -7.847626686096191, 32.02854919433594, 16.22237205505371, -55.389163970947266, -2.840447425842285, 22.363842010498047, 43.291847229003906, 43.30692672729492, 220.0712890625, 329.49591064453125, -92.9949722290039, -238.05699157714844, -20.948532104492188, 63.79200744628906, -261.4438781738281, -294.5377502441406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-96.1717', '0.7780', '21.4573', '24.4104']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-96.171661', yCenter: '0.777965', height: '21.457333', width: '24.410385', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-44.208561', yCenter: '-10.396217', height: '28.429535', width: '44.485558', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-192.585846', yCenter: '0.432206', height: '61.618652', width: '-13.989748', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.64', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.638133,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.638133,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-134.80844116210938, -3.7768561840057373, 34.92599105834961, 30.409162521362305, 7.819737434387207, -18.503284454345703, 20.98050880432129, 15.828801155090332, 40.627681732177734, -96.9185791015625, 32.54665756225586, 90.7699203491211, -40.34077453613281, -55.39628982543945, -19.06988525390625, 27.834497451782227, 44.44884490966797, 44.46268844604492, 39.03700256347656, 453.2659912109375, -162.31068420410156, -165.43923950195312, 73.28138732910156, 75.01050567626953, -8.534564018249512, -370.1932067871094, -331.442626953125, -9.283123016357422, 67.11241912841797, -21.537391662597656, -36.56487274169922, -89.89106750488281, 48.970375061035156, 20.961502075195312, 47.249732971191406, -82.7468490600586, -30.178359985351562, 29.83698081970215, 27.648202896118164, -71.03723907470703, -9.02031421661377, 15.041457176208496, 42.06366729736328, 42.08020782470703, 439.8226623535156, 440.7823181152344, -191.93582153320312, -290.101318359375, -15.49858283996582, 78.57537078857422, -497.8036193847656, -397.4577331542969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-134.8084', '-3.7769', '34.9260', '30.4092']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-134.808441', yCenter: '-3.776856', height: '34.925991', width: '30.409163', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.396290', yCenter: '-19.069885', height: '27.834497', width: '44.448845', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-331.442627', yCenter: '-9.283123', height: '67.112419', width: '-21.537392', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.680505,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.680505,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.69', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.695875,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.695875,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-150.4053955078125, -3.152252674102783, 36.311927795410156, 33.91553497314453, 7.540216445922852, -21.73019027709961, 17.666240692138672, 16.812685012817383, 38.96144104003906, -104.02592468261719, 29.339675903320312, 92.92973327636719, -45.328853607177734, -57.688316345214844, -21.68879508972168, 27.529081344604492, 44.41136169433594, 44.42517852783203, 87.61174774169922, 457.8102722167969, -193.29966735839844, -167.85369873046875, 82.97307586669922, 72.48688507080078, -53.622276306152344, -379.2313232421875, -376.7832336425781, -10.124112129211426, 66.04845428466797, -24.979337692260742, -40.77371597290039, -102.879150390625, 46.76618194580078, 21.42923927307129, 40.179931640625, -90.90288543701172, -38.490013122558594, 26.950471878051758, 27.982126235961914, -76.82575225830078, -10.777609825134277, 12.31152057647705, 41.65911102294922, 41.676055908203125, 546.3490600585938, 434.56964111328125, -241.9290008544922, -293.7166442871094, -5.017651081085205, 74.7180404663086, -602.543212890625, -399.0176086425781]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-150.4054', '-3.1523', '36.3119', '33.9155']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-150.405396', yCenter: '-3.152253', height: '36.311928', width: '33.915535', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-57.688316', yCenter: '-21.688795', height: '27.529081', width: '44.411362', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-376.783234', yCenter: '-10.124112', height: '66.048454', width: '-24.979338', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.76', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.7603,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.7603,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-179.14144897460938, -5.632275581359863, 38.68685531616211, 41.542015075683594, 4.0265655517578125, -27.791322708129883, 11.886754035949707, 22.96377944946289, 34.99637985229492, -115.61763763427734, 19.937898635864258, 104.96820831298828, -50.31924057006836, -64.02435302734375, -26.38846778869629, 25.368549346923828, 44.3851432800293, 44.39889907836914, 153.9429473876953, 464.5542907714844, -228.57083129882812, -180.58961486816406, 97.75735473632812, 73.4137191772461, -110.41232299804688, -389.93646240234375, -469.4381408691406, -17.613916397094727, 62.87086868286133, -25.665002822875977, -54.110191345214844, -129.1725311279297, 45.05697250366211, 27.182662963867188, 30.1998348236084, -101.93568420410156, -61.66376495361328, 36.841678619384766, 38.2952766418457, -87.11043548583984, -13.662764549255371, 6.113003253936768, 40.658809661865234, 40.677207946777344, 761.1864624023438, 410.8434143066406, -330.03192138671875, -302.3603515625, 10.855303764343262, 82.90016174316406, -815.2200927734375, -369.11029052734375]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-179.1414', '-5.6323', '38.6869', '41.5420']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-179.141449', yCenter: '-5.632276', height: '38.686855', width: '41.542015', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-64.024353', yCenter: '-26.388468', height: '25.368549', width: '44.385143', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-469.438141', yCenter: '-17.613916', height: '62.870869', width: '-25.665003', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.81', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.808198,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.808198,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-161.52500915527344, -4.175033092498779, 36.3852424621582, 37.43192672729492, 6.1599273681640625, -24.95196533203125, 15.419819831848145, 17.94001007080078, 35.41494369506836, -110.129150390625, 26.49599266052246, 98.33189392089844, -48.28162384033203, -60.48078536987305, -23.718469619750977, 26.4218807220459, 44.404293060302734, 44.418094635009766, 120.6304931640625, 466.9261169433594, -217.0728302001953, -172.04273986816406, 90.00537109375, 72.61479187011719, -85.0800552368164, -389.5148010253906, -416.6354675292969, -12.685158729553223, 65.5246810913086, -25.717594146728516, -46.29327392578125, -114.27491760253906, 46.33067321777344, 22.532501220703125, 34.57192611694336, -95.5654525756836, -47.1557731628418, 29.530033111572266, 31.46108055114746, -81.05899810791016, -11.963303565979004, 9.7288179397583, 41.16461181640625, 41.182228088378906, 633.4957275390625, 438.7504577636719, -278.6752014160156, -297.85699462890625, 1.891883134841919, 79.1089859008789, -688.5208740234375, -398.5279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-161.5250', '-4.1750', '36.3852', '37.4319']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-161.525009', yCenter: '-4.175033', height: '36.385242', width: '37.431927', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-60.480785', yCenter: '-23.718470', height: '26.421881', width: '44.404293', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-416.635468', yCenter: '-12.685159', height: '65.524681', width: '-25.717594', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1013","videoRect":"570x1013.3203125","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1013","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.85', canvasSize: '570x1013'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.856273,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.856273,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-117.99117279052734, -3.527639150619507, 27.892414093017578, 27.931434631347656, 7.451789379119873, -12.78081226348877, 23.776412963867188, 9.38687801361084, 38.555755615234375, -90.11808013916016, 34.73429870605469, 88.2047119140625, -38.25580978393555, -50.41459655761719, -15.248403549194336, 28.374011993408203, 44.469688415527344, 44.483673095703125, -7.579749584197998, 416.6793518066406, -141.35006713867188, -147.3075408935547, 60.68023681640625, 70.57465362548828, 26.330175399780273, -335.5982971191406, -273.94537353515625, -6.585906982421875, 61.47150802612305, -17.795862197875977, -31.871381759643555, -72.67665100097656, 46.962371826171875, 18.351831436157227, 50.49949264526367, -71.62909698486328, -23.3612060546875, 30.44928741455078, 24.028156280517578, -63.75605773925781, -6.645132541656494, 16.769927978515625, 42.717811584472656, 42.73344421386719, 339.578125, 403.24066162109375, -147.29598999023438, -269.7014465332031, -23.211257934570312, 72.99738311767578, -397.7270202636719, -362.9508361816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.9912', '-3.5276', '27.8924', '27.9314']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.991173', yCenter: '-3.527639', height: '27.892414', width: '27.931435', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.414597', yCenter: '-15.248404', height: '28.374012', width: '44.469688', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-273.945374', yCenter: '-6.585907', height: '61.471508', width: '-17.795862', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[2254],"size":2254},"anchorY":{"shape":[2254],"size":2254},"anchorW":{"shape":[2254],"size":2254},"anchorH":{"shape":[2254],"size":2254}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Error in slice2D: begin[1] + size[1] (5) would overflow input.shape[1] (4)
    at assert (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:1742:11)
    at Object.assertParamsValid (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:13146:5)
    at Object.slice3 [as kernelFunc] (http://localhost:8083/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:31907:22)
    at kernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at slice_ (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6702:17)
    at Module.slice__op (http://localhost:8083/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.926905,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.926905,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8083/d6a118b2-616f-4b60-ac18..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":2.97063,"paused":true,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
