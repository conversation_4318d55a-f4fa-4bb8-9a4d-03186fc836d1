SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.684171,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)0: 11: 22542: 13length: 3[[Prototype]]: Array(0)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)0: -134.54606628417971: -5.5221338272094732: 31.5651741027832033: 31.2028179168701174: 10.1690387725830085: -15.2534189224243166: 18.274644851684577: 13.8918542861938488: 38.5885047912597669: -97.8820114135742210: 30.54979324340820311: 91.0529327392578112: -44.19238662719726613: -54.7570037841796914: -18.927984237670915: 28.929204940795916: 44.44479370117187517: 44.4585990905761718: 33.9276275634765619: 448.069305419921920: -164.3163146972656221: -157.184204101562522: 73.5327301025390623: 75.567993164062524: -5.0302362442016625: -361.811737060546926: -333.320922851562527: -12.71803188323974628: 61.40889739990234429: -22.72137641906738330: -35.0325126647949231: -88.046997070312532: 47.0267829895019533: 17.2077083587646534: 43.04555511474609435: -83.1355361938476636: -32.5177803039550837: 30.4071311950683638: 24.7433872222900439: -70.4752044677734440: -7.99777221679687541: 16.0138816833496142: 41.9506912231445343: 41.96732330322265644: 429.47875976562545: 427.8536987304687546: -192.6130065917968847: -278.1864624023437548: -19.30491256713867249: 77.5531539916992250: -494.5673217773437551: -382.85430908203125length: 52[[Prototype]]: Array(0)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)0: "-134.5461"1: "-5.5221"2: "31.5652"3: "31.2028"length: 4[[Prototype]]: Array(0)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)0: 11: 22542: 4length: 3[[Prototype]]: Array(0)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)0: 11: 22542: 1length: 3[[Prototype]]: Array(0)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Objectheight: "31.565174"rawIndex: 0width: "31.202818"xCenter: "-134.546066"yCenter: "-5.522134"[[Prototype]]: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Objectheight: "28.929205"rawIndex: 4width: "44.444794"xCenter: "-54.757004"yCenter: "-18.927984"[[Prototype]]: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: ObjectrawBoxTensor: {type: 'object', isTensor: true, shape: Array(2)}rawScoreTensor: {type: 'object', isTensor: true, shape: Array(2)}[[Prototype]]: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: ObjectnumCoords: 12reverseOutputOrder: truexScale: 224yScale: 224[[Prototype]]: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.728171,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.728171,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.732834,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.732834,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.784235,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.784235,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.833971,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.833971,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.882536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.882536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.93188,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.93188,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.937505,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.937505,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.00221,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.00221,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.049802,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.049802,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.094911,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.094911,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.148174,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.148174,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.152214,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.152214,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.20041,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.20041,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.241646,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.241646,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.300392,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.300392,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.340441,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.340441,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.342889,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.342889,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.400827,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.400827,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.450564,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.450564,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection Object
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.494917,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.494917,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.548292,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.548292,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":6}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.552523,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.552523,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":6}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.618286,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.618286,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":6}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.66104,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.66104,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":6}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: Array(4)
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.700877,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.700877,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: Object
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: Array(3)
detector.ts:361 🔍 Raw tensor data sample (first 52 values): Array(52)
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: Array(4)
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): Array(3)
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): Array(3)
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: Object
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: Object
detector.ts:369 🔍 DETECTOR: detectorResult returned: Object
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: Object
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.737311,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.737311,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":7}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.741616,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.741616,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-183.14561462402344, -3.6569972038269043, 42.3497428894043, 40.95480728149414, 1.6155197620391846, -29.508161544799805, 11.416181564331055, 25.859310150146484, 34.83927917480469, -113.55269622802734, 17.821361541748047, 103.04254913330078, -48.50632858276367, -65.12960815429688, -27.06304359436035, 23.8820858001709, 44.37892150878906, 44.39252471923828, 182.43606567382812, 471.01910400390625, -225.88026428222656, -189.5908660888672, 104.23802947998047, 75.1536865234375, -125.766845703125, -396.9366149902344, -484.1919250488281, -16.143692016601562, 65.46821594238281, -26.843463897705078, -57.21756362915039, -132.0818328857422, 45.92319107055664, 27.612079620361328, 25.755155563354492, -103.4023208618164, -63.37363052368164, 36.603519439697266, 38.63172912597656, -88.84054565429688, -14.086071014404297, 4.650125980377197, 40.541290283203125, 40.5597038269043, 798.14892578125, 425.6154479980469, -333.2057800292969, -313.0595703125, 20.400428771972656, 86.96958923339844, -836.4315185546875, -381.4058837890625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-183.1456', '-3.6570', '42.3497', '40.9548']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-183.145615', yCenter: '-3.656997', height: '42.349743', width: '40.954807', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-65.129608', yCenter: '-27.063044', height: '23.882086', width: '44.378922', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-484.191925', yCenter: '-16.143692', height: '65.468216', width: '-26.843464', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":7}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.801206,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.801206,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-202.35324096679688, -2.841033458709717, 42.374305725097656, 43.89311599731445, -1.027936339378357, -34.1225700378418, 6.416269779205322, 30.14097785949707, 32.268070220947266, -119.05684661865234, 14.110265731811523, 108.36434173583984, -51.51023864746094, -67.70072174072266, -29.3204345703125, 21.519275665283203, 44.35166931152344, 44.36518096923828, 230.5499725341797, 474.24822998046875, -256.21844482421875, -191.67666625976562, 115.3299789428711, 79.42662811279297, -168.14649963378906, -395.3835754394531, -535.9863891601562, -15.095613479614258, 63.13883972167969, -28.858789443969727, -67.18028259277344, -145.8921661376953, 41.88065719604492, 33.69955825805664, 19.016023635864258, -105.3252182006836, -73.4927749633789, 38.54371643066406, 44.8790168762207, -96.09942626953125, -16.03217315673828, -1.4414095878601074, 39.979469299316406, 39.99851989746094, 887.7621459960938, 404.055419921875, -371.5081481933594, -313.4187927246094, 24.860294342041016, 90.27233123779297, -928.6525268554688, -357.9619140625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-202.3532', '-2.8410', '42.3743', '43.8931']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-202.353241', yCenter: '-2.841033', height: '42.374306', width: '43.893116', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-67.700722', yCenter: '-29.320435', height: '21.519276', width: '44.351669', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-535.986389', yCenter: '-15.095613', height: '63.138840', width: '-28.858789', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":7}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.846147,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.846147,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-135.5208740234375, -1.2542515993118286, 31.860671997070312, 31.84015464782715, 8.171035766601562, -17.50732421875, 19.97601890563965, 13.541329383850098, 37.86764907836914, -98.14837646484375, 35.536502838134766, 91.84180450439453, -42.46717071533203, -54.751670837402344, -18.907926559448242, 27.645401000976562, 44.445526123046875, 44.4594841003418, 35.729156494140625, 438.4917297363281, -178.31536865234375, -153.3590850830078, 73.75980377197266, 71.18525695800781, -11.95964241027832, -357.4026184082031, -329.26690673828125, -7.54729700088501, 65.36202239990234, -22.829748153686523, -36.32077407836914, -90.2172622680664, 47.42412185668945, 20.138538360595703, 45.890357971191406, -81.91423034667969, -28.728174209594727, 28.889490127563477, 27.357276916503906, -71.3651351928711, -8.172246932983398, 14.883922576904297, 42.07163619995117, 42.08812713623047, 437.4358825683594, 423.4835205078125, -198.89218139648438, -277.7241516113281, -16.50078773498535, 75.04126739501953, -500.02850341796875, -381.7366943359375]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-135.5209', '-1.2543', '31.8607', '31.8402']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-135.520874', yCenter: '-1.254252', height: '31.860672', width: '31.840155', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-54.751671', yCenter: '-18.907927', height: '27.645401', width: '44.445526', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-329.266907', yCenter: '-7.547297', height: '65.362022', width: '-22.829748', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":7}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.884817,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.884817,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-119.67977905273438, -0.2037678211927414, 30.423328399658203, 27.14569854736328, 6.010887145996094, -15.105305671691895, 24.706287384033203, 11.227160453796387, 40.55921173095703, -90.25945281982422, 37.88726806640625, 87.63679504394531, -36.476524353027344, -50.33860397338867, -15.547080993652344, 27.51412010192871, 44.4669075012207, 44.48091125488281, -3.886535882949829, 414.3971862792969, -146.2330322265625, -147.74514770507812, 61.75183868408203, 70.27739715576172, 22.810426712036133, -333.6922607421875, -279.0491638183594, -3.52055025100708, 64.91979217529297, -19.378341674804688, -31.85538673400879, -75.26848602294922, 47.3594970703125, 20.828519821166992, 52.59736633300781, -72.89559936523438, -19.791337966918945, 29.577390670776367, 24.51372528076172, -65.36490631103516, -6.57444953918457, 17.205703735351562, 42.54181671142578, 42.557735443115234, 345.2176513671875, 407.3341979980469, -153.5101776123047, -272.03997802734375, -20.150365829467773, 74.37128448486328, -400.8483581542969, -365.1854553222656]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-119.6798', '-0.2038', '30.4233', '27.1457']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-119.679779', yCenter: '-0.203768', height: '30.423328', width: '27.145699', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.338604', yCenter: '-15.547081', height: '27.514120', width: '44.466908', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-279.049164', yCenter: '-3.520550', height: '64.919792', width: '-19.378342', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.933542,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":7.933542,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":8}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":8.939891,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":8.939891,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/b132d87b-569a-45d2-b9d7..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":8.946616,"paused":true,"videoWidth":1080,"videoHeight":1920}
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-122.7494125366211, -4.712123394012451, 30.195409774780273, 27.67933464050293, 9.202360153198242, -14.163432121276855, 22.022544860839844, 12.01484489440918, 40.105796813964844, -92.25102996826172, 34.082611083984375, 87.86799621582031, -39.94061279296875, -50.861183166503906, -16.29774284362793, 29.233781814575195, 44.461708068847656, 44.47560119628906, -3.1860151290893555, 430.1775207519531, -142.08010864257812, -151.16493225097656, 64.17447662353516, 73.88084411621094, 26.429353713989258, -345.0315856933594, -287.2174987792969, -9.691929817199707, 62.533653259277344, -20.203746795654297, -31.828760147094727, -78.18997955322266, 47.242515563964844, 18.755054473876953, 50.011474609375, -75.53084564208984, -25.813907623291016, 29.855281829833984, 24.399784088134766, -66.11628723144531, -6.870511054992676, 17.192398071289062, 42.47416305541992, 42.49009323120117, 355.67022705078125, 418.11328125, -153.4068603515625, -275.12603759765625, -24.129140853881836, 75.44536590576172, -416.5478515625, -375.5315856933594]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-122.7494', '-4.7121', '30.1954', '27.6793']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-122.749413', yCenter: '-4.712123', height: '30.195410', width: '27.679335', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.861183', yCenter: '-16.297743', height: '29.233782', width: '44.461708', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-287.217499', yCenter: '-9.691930', height: '62.533653', width: '-20.203747', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
