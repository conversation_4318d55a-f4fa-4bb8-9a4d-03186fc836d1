client:495 [vite] connecting...
client:614 [vite] connected.
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: {feet: 5, inches: 10}
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: {backend: 'webgl', memory: {…}, platform: PlatformBrowser}
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: {modelType: 'full', enableSmoothing: true, enableSegmentation: false, detectorUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...', landmarkUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...'}
create_ssd_anchors.ts:44 🔧 SSD ANCHORS: Creating anchors with config: {reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: Array(6), featureMapWidth: Array(6), numLayers: 6, …}
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Feature map dimensions: {heights: Array(6), widths: Array(6), numLayers: 6, aspectRatios: Array(2)}
create_ssd_anchors.ts:53 🔧 SSD ANCHORS: CRITICAL DEBUG - Anchor offsets: {anchorOffsetX: 0.5, anchorOffsetY: 0.5, potentialForNegativeX: false, potentialForNegativeY: false}
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 0: 28x28
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 1: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 2: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 3: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 4: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 5: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 1: 16x16
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 2: 8x8
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 3: 4x4
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 4: 2x2
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 5: 1x1
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Created 2250 anchors
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.01', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 105ms
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0.086484,"paused":false,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.087723,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.087723,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
favicon.ico:1  GET http://localhost:8081/favicon.ico 404 (Not Found)
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-130.8863983154297, -6.017020225524902, 30.701818466186523, 29.820510864257812, 9.098145484924316, -14.37855052947998, 19.36659812927246, 12.781549453735352, 37.6097412109375, -95.7075424194336, 29.777772903442383, 89.47322845458984, -42.88039779663086, -53.6333122253418, -17.927043914794922, 28.582067489624023, 44.45489501953125, 44.468650817871094, 23.3839168548584, 434.6966247558594, -150.37757873535156, -155.14463806152344, 70.87877655029297, 72.2372055053711, 7.230284690856934, -353.5482482910156, -319.0079040527344, -11.641759872436523, 61.3012809753418, -21.839712142944336, -35.256099700927734, -84.10852813720703, 46.76162338256836, 17.245298385620117, 43.734222412109375, -80.06254577636719, -31.22454071044922, 29.762184143066406, 23.8579158782959, -68.58247375488281, -7.925967216491699, 15.95534896850586, 42.17305374145508, 42.18927764892578, 410.4118347167969, 414.49444580078125, -176.63784790039062, -275.2091369628906, -18.49203872680664, 74.27591705322266, -469.05523681640625, -374.279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-130.8864', '-6.0170', '30.7018', '29.8205']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.18', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.182715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.182715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.19', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.192022,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.192022,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-92.40853118896484, -2.14312744140625, 21.786373138427734, 22.11491584777832, 4.015638828277588, -7.879236698150635, 31.15460205078125, 1.1623234748840332, 40.058834075927734, -81.09104919433594, 41.21393585205078, 85.7042236328125, -33.640830993652344, -42.13155746459961, -8.987822532653809, 28.90133285522461, 44.49059295654297, 44.5048713684082, -86.78099060058594, 349.5536804199219, -105.13580322265625, -124.78382873535156, 39.32023620605469, 62.831764221191406, 86.4603271484375, -275.0758972167969, -183.4922637939453, -0.6202813386917114, 60.3310432434082, -13.088068008422852, -25.298288345336914, -47.65382766723633, 44.3593635559082, 18.239105224609375, 62.14387893676758, -53.90993118286133, -9.767181396484375, 32.28388214111328, 16.059762954711914, -54.55998992919922, -3.056915044784546, 22.133438110351562, 43.444087982177734, 43.45891571044922, 196.92608642578125, 326.8105773925781, -76.70394134521484, -241.26467895507812, -25.5710391998291, 64.63663482666016, -239.86488342285156, -292.296142578125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-92.4085', '-2.1431', '21.7864', '22.1149']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-92.408531', yCenter: '-2.143127', height: '21.786373', width: '22.114916', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-42.131557', yCenter: '-8.987823', height: '28.901333', width: '44.490593', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-183.492264', yCenter: '-0.620281', height: '60.331043', width: '-13.088068', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-92.3745346069336, 0.8503541350364685, 20.5238094329834, 23.69338035583496, 2.8098156452178955, -5.847290992736816, 31.895526885986328, 0.7940861582756042, 41.37717056274414, -82.68775177001953, 42.76167678833008, 85.23108673095703, -34.09239196777344, -42.050384521484375, -9.778409004211426, 28.0316219329834, 44.4971923828125, 44.511505126953125, -93.20697784423828, 340.98358154296875, -109.33358764648438, -122.41755676269531, 38.074119567871094, 61.27225875854492, 88.86576843261719, -268.3564147949219, -176.98190307617188, 2.526824474334717, 62.49740982055664, -12.782509803771973, -25.174060821533203, -46.01404571533203, 44.934410095214844, 18.58452033996582, 64.5469970703125, -54.035377502441406, -5.832997798919678, 32.808692932128906, 15.238598823547363, -53.85005187988281, -2.6802978515625, 22.615541458129883, 43.47272491455078, 43.48756790161133, 188.5776824951172, 318.8624267578125, -75.48455047607422, -237.36624145507812, -22.522653579711914, 62.923282623291016, -227.1230926513672, -285.49395751953125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-92.3745', '0.8504', '20.5238', '23.6934']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-92.374535', yCenter: '0.850354', height: '20.523809', width: '23.693380', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-42.050385', yCenter: '-9.778409', height: '28.031622', width: '44.497192', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-176.981903', yCenter: '2.526824', height: '62.497410', width: '-12.782510', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.243475,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.243475,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.26', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.263233,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.263233,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-124.2955093383789, -3.9126250743865967, 31.982929229736328, 27.900920867919922, 8.176383018493652, -14.68222713470459, 21.707481384277344, 13.404290199279785, 39.53047561645508, -91.20906066894531, 32.98139572143555, 87.2938232421875, -38.42406463623047, -52.22825241088867, -16.75022315979004, 27.845813751220703, 44.4615478515625, 44.475372314453125, 6.149413108825684, 433.4749450683594, -138.68450927734375, -155.70498657226562, 66.89099884033203, 72.77118682861328, 23.31061553955078, -351.6151123046875, -296.39593505859375, -7.953659534454346, 63.50709533691406, -20.773948669433594, -33.461647033691406, -78.57318115234375, 47.104434967041016, 19.5331974029541, 47.08848571777344, -74.65187072753906, -25.538976669311523, 28.804807662963867, 25.03851890563965, -66.35588836669922, -7.273731231689453, 15.814507484436035, 42.389488220214844, 42.405452728271484, 366.9866638183594, 418.86865234375, -152.2772216796875, -276.7036437988281, -18.256912231445312, 75.54915618896484, -418.0045166015625, -377.05047607421875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-124.2955', '-3.9126', '31.9829', '27.9009']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-124.295509', yCenter: '-3.912625', height: '31.982929', width: '27.900921', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-52.228252', yCenter: '-16.750223', height: '27.845814', width: '44.461548', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-296.395935', yCenter: '-7.953660', height: '63.507095', width: '-20.773949', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.29', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.292715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.292715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-133.6677703857422, -3.3944900035858154, 32.514591217041016, 31.11994171142578, 7.178594589233398, -15.147101402282715, 18.688196182250977, 15.622016906738281, 39.028202056884766, -95.39656066894531, 31.323368072509766, 91.93486785888672, -42.04281234741211, -55.48686218261719, -18.741453170776367, 27.76298713684082, 44.449005126953125, 44.46285629272461, 20.637371063232422, 425.3644104003906, -156.3790283203125, -158.87290954589844, 69.48836517333984, 68.0987319946289, 5.253230094909668, -351.9127197265625, -329.77740478515625, -8.728510856628418, 64.33702087402344, -21.41324234008789, -38.34980010986328, -87.02095794677734, 46.8077278137207, 20.278772354125977, 45.67219543457031, -79.43982696533203, -31.21091079711914, 31.693788528442383, 26.83330726623535, -69.99451446533203, -8.543956756591797, 14.672757148742676, 42.14985656738281, 42.16624450683594, 431.434326171875, 402.71282958984375, -191.0473175048828, -279.59295654296875, -18.512451171875, 71.87494659423828, -494.0888366699219, -367.8005065917969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-133.6678', '-3.3945', '32.5146', '31.1199']
 ✅ DETECTOR RESULT: Tensor indexing validation passed
 🔧 DETECTOR RESULT: Slicing completed successfully
 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
 ✅ DETECTOR RESULT: Tensor slicing validation passed
 🔧 DETECTOR RESULT: Starting coordinate transformation validation
 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-133.667770', yCenter: '-3.394490', height: '32.514591', width: '31.119942', rawIndex: 0}
 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.486862', yCenter: '-18.741453', height: '27.762987', width: '44.449005', rawIndex: 4}
 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-329.777405', yCenter: '-8.728511', height: '64.337021', width: '-21.413242', rawIndex: 8}
 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: 
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:107
detectPose @ detector.ts:313
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:164
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:190
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.33', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.334227,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.334227,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-111.91265106201172, -0.604552686214447, 28.124675750732422, 27.37732696533203, 6.419748783111572, -11.004325866699219, 25.55445098876953, 9.361189842224121, 40.89176559448242, -87.7922134399414, 37.73933029174805, 87.32823944091797, -36.210514068603516, -50.085540771484375, -14.482438087463379, 27.680072784423828, 44.477752685546875, 44.491859436035156, -28.557369232177734, 405.11016845703125, -134.23265075683594, -147.15171813964844, 56.61017990112305, 69.6845703125, 43.634063720703125, -325.8420715332031, -252.8214874267578, -3.6208605766296387, 64.22813415527344, -16.286039352416992, -30.228490829467773, -66.61016845703125, 47.354583740234375, 19.19456672668457, 54.275569915771484, -66.70899200439453, -17.235233306884766, 32.31379318237305, 22.702112197875977, -61.34731674194336, -5.533083438873291, 18.20956802368164, 42.83983612060547, 42.85546112060547, 302.2760009765625, 394.0992126464844, -130.30081176757812, -268.15142822265625, -21.292879104614258, 73.1036376953125, -353.09259033203125, -353.2862854003906]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-111.9127', '-0.6046', '28.1247', '27.3773']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-111.912651', yCenter: '-0.604553', height: '28.124676', width: '27.377327', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.085541', yCenter: '-14.482438', height: '27.680073', width: '44.477753', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-252.821487', yCenter: '-3.620861', height: '64.228134', width: '-16.286039', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.37', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.37836,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.37836,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-125.99757385253906, -2.989555597305298, 30.623302459716797, 30.38325309753418, 7.835110187530518, -13.328619956970215, 20.709779739379883, 13.680414199829102, 38.75544738769531, -92.75369262695312, 33.72036361694336, 91.09233856201172, -39.54802322387695, -53.65089416503906, -17.402956008911133, 27.683053970336914, 44.46122741699219, 44.47519302368164, -7.819507598876953, 424.947998046875, -146.11572265625, -154.47779846191406, 63.44028854370117, 68.64201354980469, 27.64691162109375, -349.01116943359375, -304.3684997558594, -7.782813549041748, 63.95247268676758, -19.281755447387695, -35.21134948730469, -80.48857879638672, 47.208213806152344, 20.23642349243164, 48.77885055541992, -75.53562927246094, -26.593372344970703, 32.82651901245117, 27.041597366333008, -67.14461517333984, -7.623241424560547, 15.401427268981934, 42.38215637207031, 42.39832305908203, 375.9181213378906, 407.5686340332031, -168.51243591308594, -275.2664794921875, -23.004932403564453, 73.08536529541016, -439.865478515625, -368.8697814941406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-125.9976', '-2.9896', '30.6233', '30.3833']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-125.997574', yCenter: '-2.989556', height: '30.623302', width: '30.383253', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-53.650894', yCenter: '-17.402956', height: '27.683054', width: '44.461227', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-304.368500', yCenter: '-7.782814', height: '63.952473', width: '-19.281755', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.419931,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.419931,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.42', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.426561,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.426561,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-156.56040954589844, -3.3237757682800293, 37.33900833129883, 35.27011489868164, 5.774257183074951, -22.0263729095459, 15.769556045532227, 19.40589714050293, 37.278175354003906, -104.62517547607422, 25.501567840576172, 95.1743392944336, -45.24931716918945, -59.14309310913086, -22.600196838378906, 26.259836196899414, 44.41142272949219, 44.425140380859375, 104.46749114990234, 451.46893310546875, -191.78941345214844, -172.84780883789062, 87.08216857910156, 70.1645278930664, -62.888450622558594, -378.84210205078125, -396.9363708496094, -11.844284057617188, 64.64949035644531, -23.590911865234375, -45.19445037841797, -107.90135192871094, 47.055538177490234, 21.682998657226562, 36.12664794921875, -93.54972076416016, -45.59654998779297, 31.009342193603516, 30.470212936401367, -79.20026397705078, -11.662331581115723, 10.3682222366333, 41.599876403808594, 41.616920471191406, 615.1553344726562, 421.0213317871094, -265.1405944824219, -294.9998779296875, 2.963083505630493, 76.13191986083984, -664.3294677734375, -384.9202880859375]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-156.5604', '-3.3238', '37.3390', '35.2701']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-156.560410', yCenter: '-3.323776', height: '37.339008', width: '35.270115', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-59.143093', yCenter: '-22.600197', height: '26.259836', width: '44.411423', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-396.936371', yCenter: '-11.844284', height: '64.649490', width: '-23.590912', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.47', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.470028,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.470028,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-140.43362426757812, -2.294687032699585, 32.197265625, 32.42231750488281, 9.52273941040039, -18.10296630859375, 18.13564109802246, 13.57729721069336, 36.08610534667969, -100.40884399414062, 32.27736282348633, 91.22859954833984, -44.69697570800781, -55.30386734008789, -19.57513999938965, 27.455135345458984, 44.430965423583984, 44.444862365722656, 65.3661880493164, 452.3669738769531, -187.53575134277344, -158.92071533203125, 80.86659240722656, 73.551025390625, -33.45787048339844, -369.3055419921875, -344.9222717285156, -9.35782527923584, 62.83213806152344, -23.675390243530273, -36.283172607421875, -94.15235137939453, 46.80009078979492, 17.297090530395508, 38.73889923095703, -85.99890899658203, -34.06479263305664, 26.246349334716797, 25.65310287475586, -73.2886734008789, -8.75562858581543, 13.817153930664062, 41.95928955078125, 41.97589874267578, 492.1914367675781, 438.0451965332031, -221.09458923339844, -284.50640869140625, -8.757964134216309, 76.5608901977539, -549.10400390625, -396.2010498046875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-140.4336', '-2.2947', '32.1973', '32.4223']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-140.433624', yCenter: '-2.294687', height: '32.197266', width: '32.422318', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.303867', yCenter: '-19.575140', height: '27.455135', width: '44.430965', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-344.922272', yCenter: '-9.357825', height: '62.832138', width: '-23.675390', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.52', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.526765,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.526765,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-133.4470977783203, -4.475689888000488, 29.614273071289062, 31.682722091674805, 9.597671508789062, -15.062826156616211, 18.568140029907227, 12.599186897277832, 36.287261962890625, -96.55213928222656, 31.222951889038086, 90.61532592773438, -43.52823257446289, -53.46243667602539, -18.503686904907227, 27.981290817260742, 44.445526123046875, 44.45939636230469, 32.63252258300781, 442.55914306640625, -167.1554412841797, -155.3623046875, 72.16983795166016, 72.7192153930664, -6.911509990692139, -360.1578369140625, -324.4856872558594, -10.009133338928223, 60.12282180786133, -21.930179595947266, -35.25345230102539, -86.198486328125, 45.54230880737305, 17.92220687866211, 42.44377517700195, -79.9004135131836, -32.74103927612305, 28.278345108032227, 25.700284957885742, -69.86380767822266, -7.97611141204834, 14.251008987426758, 42.17884063720703, 42.19515609741211, 425.1836853027344, 422.4668273925781, -190.43629455566406, -278.5850830078125, -20.19629669189453, 74.67837524414062, -490.85736083984375, -382.3296203613281]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-133.4471', '-4.4757', '29.6143', '31.6827']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-133.447098', yCenter: '-4.475690', height: '29.614273', width: '31.682722', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-53.462437', yCenter: '-18.503687', height: '27.981291', width: '44.445526', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-324.485687', yCenter: '-10.009133', height: '60.122822', width: '-21.930180', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.57', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.57377,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.57377,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-123.78075408935547, -3.9132754802703857, 29.25518798828125, 28.541593551635742, 8.403807640075684, -14.161598205566406, 21.78751564025879, 11.239751815795898, 37.89085388183594, -91.16725158691406, 33.22095489501953, 87.85258483886719, -38.99559783935547, -50.80849838256836, -16.11865997314453, 27.755847930908203, 44.45690155029297, 44.47080993652344, 9.297776222229004, 430.1006774902344, -146.8555450439453, -152.19947814941406, 65.71577453613281, 71.80411529541016, 15.175288200378418, -348.4976806640625, -290.74853515625, -8.11586856842041, 60.85175704956055, -20.0346622467041, -32.57853698730469, -77.96803283691406, 46.078731536865234, 18.367488861083984, 46.598976135253906, -74.11332702636719, -27.434762954711914, 28.2530574798584, 25.35661506652832, -66.0155029296875, -6.890414237976074, 15.280313491821289, 42.579124450683594, 42.59489822387695, 370.56060791015625, 415.1648254394531, -160.1319122314453, -274.2489318847656, -22.015132904052734, 74.10636138916016, -430.07452392578125, -374.6274719238281]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-123.7808', '-3.9133', '29.2552', '28.5416']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-123.780754', yCenter: '-3.913275', height: '29.255188', width: '28.541594', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.808498', yCenter: '-16.118660', height: '27.755848', width: '44.456902', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-290.748535', yCenter: '-8.115869', height: '60.851757', width: '-20.034662', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.621242,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.621242,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.63', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.634984,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.634984,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-119.1600341796875, -1.9619715213775635, 29.19172477722168, 27.410972595214844, 6.020007133483887, -16.263986587524414, 25.48137664794922, 8.44543743133545, 39.19339370727539, -91.85794067382812, 36.19414520263672, 87.7896499633789, -37.706233978271484, -49.40229797363281, -15.0712308883667, 28.2312068939209, 44.463218688964844, 44.47724151611328, 12.496919631958008, 413.3130798339844, -154.5870361328125, -146.34173583984375, 61.87718963623047, 69.68636322021484, 4.387426853179932, -333.0224304199219, -274.0191345214844, -4.604846954345703, 63.88733673095703, -19.228836059570312, -31.69730567932129, -75.31462860107422, 46.332420349121094, 20.478363037109375, 51.709716796875, -70.73492431640625, -23.219165802001953, 27.739748001098633, 24.638389587402344, -65.16547393798828, -6.57895040512085, 17.082773208618164, 42.63560104370117, 42.65132141113281, 362.7138366699219, 401.3815002441406, -153.25416564941406, -272.27880859375, -18.80916976928711, 73.44357299804688, -414.8790283203125, -361.26025390625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-119.1600', '-1.9620', '29.1917', '27.4110']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-119.160034', yCenter: '-1.961972', height: '29.191725', width: '27.410973', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-49.402298', yCenter: '-15.071231', height: '28.231207', width: '44.463219', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-274.019135', yCenter: '-4.604847', height: '63.887337', width: '-19.228836', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.68', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.681437,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.681437,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-122.60665893554688, -5.235272407531738, 29.657140731811523, 28.04186248779297, 7.996857166290283, -15.396207809448242, 23.741180419921875, 9.774060249328613, 39.649967193603516, -93.02131652832031, 32.83145523071289, 88.19324493408203, -38.74065399169922, -51.02882766723633, -15.675426483154297, 28.75122833251953, 44.454750061035156, 44.46870422363281, 10.929914474487305, 431.1178283691406, -147.15902709960938, -151.6319580078125, 63.31012725830078, 72.74007415771484, 10.441808700561523, -347.73876953125, -287.5831604003906, -9.527853965759277, 62.28494644165039, -18.847963333129883, -31.92171287536621, -77.85617065429688, 47.86345672607422, 18.081071853637695, 48.862037658691406, -75.86665344238281, -26.019567489624023, 29.545169830322266, 24.49601936340332, -65.70106506347656, -7.091709613800049, 16.57170295715332, 42.566593170166016, 42.58238983154297, 370.4275207519531, 420.6823425292969, -157.25633239746094, -274.4610290527344, -19.439428329467773, 75.99443817138672, -425.1405029296875, -376.9762268066406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-122.6067', '-5.2353', '29.6571', '28.0419']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-122.606659', yCenter: '-5.235272', height: '29.657141', width: '28.041862', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-51.028828', yCenter: '-15.675426', height: '28.751228', width: '44.454750', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-287.583160', yCenter: '-9.527854', height: '62.284946', width: '-18.847963', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.73', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.72793,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.72793,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-142.81065368652344, -0.43859192728996277, 33.26875686645508, 33.89204788208008, 7.104899883270264, -21.884723663330078, 20.04399871826172, 13.425711631774902, 37.83243179321289, -103.1083984375, 35.192657470703125, 93.2208023071289, -44.22117233276367, -55.08050537109375, -20.50383949279785, 27.87105369567871, 44.41902542114258, 44.43303298950195, 70.61566925048828, 438.2127380371094, -203.4520721435547, -152.17034912109375, 77.82939910888672, 69.11598205566406, -49.2963752746582, -359.3427734375, -350.161865234375, -6.469929218292236, 67.35553741455078, -24.95363998413086, -38.13312911987305, -98.99882507324219, 46.81742477416992, 23.012216567993164, 45.10559844970703, -85.40782165527344, -31.621145248413086, 26.326871871948242, 30.037769317626953, -74.40418243408203, -9.043869018554688, 13.497929573059082, 41.7381591796875, 41.75509262084961, 496.76422119140625, 423.7030334472656, -225.87969970703125, -281.5072326660156, -11.257237434387207, 74.39501953125, -558.4666748046875, -384.0260925292969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-142.8107', '-0.4386', '33.2688', '33.8920']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-142.810654', yCenter: '-0.438592', height: '33.268757', width: '33.892048', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.080505', yCenter: '-20.503839', height: '27.871054', width: '44.419025', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-350.161865', yCenter: '-6.469929', height: '67.355537', width: '-24.953640', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '3.76', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.759001,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.759001,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-137.43801879882812, -2.706636428833008, 33.46413040161133, 31.70309829711914, 6.846388816833496, -18.86578369140625, 20.2337703704834, 14.90822696685791, 39.165504455566406, -97.73326873779297, 32.64054870605469, 91.44306945800781, -41.1878776550293, -54.44816970825195, -19.30470085144043, 27.22693634033203, 44.44078826904297, 44.45465850830078, 44.792755126953125, 440.98809814453125, -171.90936279296875, -159.41200256347656, 72.82984924316406, 70.73075866699219, -18.773408889770508, -362.7214660644531, -335.0484619140625, -7.461889266967773, 65.56565856933594, -22.30927085876465, -37.88973617553711, -91.66637420654297, 46.79970169067383, 22.24565887451172, 46.286224365234375, -81.99420166015625, -31.362140655517578, 28.182924270629883, 28.753238677978516, -71.57810974121094, -9.16156005859375, 13.43936824798584, 42.10356903076172, 42.119991302490234, 451.69500732421875, 423.8382263183594, -201.68539428710938, -283.9713439941406, -16.52647590637207, 74.64082336425781, -514.4058227539062, -385.1967468261719]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-137.4380', '-2.7066', '33.4641', '31.7031']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-137.438019', yCenter: '-2.706636', height: '33.464130', width: '31.703098', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-54.448170', yCenter: '-19.304701', height: '27.226936', width: '44.440788', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-335.048462', yCenter: '-7.461889', height: '65.565659', width: '-22.309271', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.801445,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.801445,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 4, videoTime: '4.81', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.817267,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.817267,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-157.90997314453125, -2.260408639907837, 37.053977966308594, 36.82992935180664, 5.367639541625977, -22.410337448120117, 14.975286483764648, 19.551197052001953, 36.55497741699219, -105.82192993164062, 27.63783836364746, 97.57389068603516, -46.23252487182617, -59.897056579589844, -23.1708927154541, 25.853900909423828, 44.413917541503906, 44.427703857421875, 96.21930694580078, 450.294921875, -201.15283203125, -168.79747009277344, 86.28630828857422, 69.0715560913086, -60.78617858886719, -377.83184814453125, -402.339111328125, -11.034029006958008, 65.49310302734375, -26.21082305908203, -46.88174057006836, -109.96643829345703, 45.78017807006836, 23.96406364440918, 36.3706169128418, -90.24012756347656, -44.86762237548828, 30.105390548706055, 32.72549819946289, -78.78857421875, -11.064093589782715, 9.571123123168945, 41.41966247558594, 41.4368782043457, 598.396728515625, 418.7920837402344, -257.4910583496094, -291.8097229003906, -1.3778810501098633, 76.51426696777344, -651.6508178710938, -381.2992248535156]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-157.9100', '-2.2604', '37.0540', '36.8299']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-157.909973', yCenter: '-2.260409', height: '37.053978', width: '36.829929', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-59.897057', yCenter: '-23.170893', height: '25.853901', width: '44.413918', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-402.339111', yCenter: '-11.034029', height: '65.493103', width: '-26.210823', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 4, videoTime: '4.85', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.856471,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.856471,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-171.57798767089844, -1.9813320636749268, 38.1181755065918, 40.11323928833008, 5.039428234100342, -26.88652992248535, 12.281807899475098, 21.801294326782227, 34.83316421508789, -111.72718811035156, 25.720426559448242, 100.32575988769531, -49.34819030761719, -61.515541076660156, -26.023099899291992, 25.192806243896484, 44.395286560058594, 44.40898132324219, 137.7314453125, 462.1408386230469, -228.01483154296875, -168.2701873779297, 96.51116943359375, 72.57915496826172, -96.89114379882812, -383.8639221191406, -442.55682373046875, -11.399515151977539, 65.2894515991211, -28.46087074279785, -51.341243743896484, -123.40428161621094, 43.9141960144043, 27.98923683166504, 31.863588333129883, -96.20613861083984, -51.70375061035156, 30.43100929260254, 37.018253326416016, -84.27523803710938, -12.508326530456543, 6.183761119842529, 40.854713439941406, 40.87261962890625, 682.6728515625, 423.61114501953125, -294.80169677734375, -294.40093994140625, 6.708031177520752, 80.78392028808594, -733.6367797851562, -381.2566223144531]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-171.5780', '-1.9813', '38.1182', '40.1132']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-171.577988', yCenter: '-1.981332', height: '38.118176', width: '40.113239', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-61.515541', yCenter: '-26.023100', height: '25.192806', width: '44.395287', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-442.556824', yCenter: '-11.399515', height: '65.289452', width: '-28.460871', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 4, videoTime: '4.90', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.898618,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.898618,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-157.29318237304688, -5.236802577972412, 36.39460754394531, 37.864810943603516, 5.711390495300293, -21.345293045043945, 14.946001052856445, 18.558122634887695, 34.719791412353516, -105.61075592041016, 24.408117294311523, 98.12345123291016, -46.867618560791016, -60.42630386352539, -23.402267456054688, 25.477392196655273, 44.42406463623047, 44.43779754638672, 100.02449798583984, 466.004150390625, -194.47354125976562, -176.61264038085938, 87.88787841796875, 71.81875610351562, -59.364280700683594, -392.0741271972656, -405.6449279785156, -15.047691345214844, 63.24071502685547, -24.34629249572754, -46.66257095336914, -109.23368072509766, 46.17949676513672, 21.62224769592285, 33.40593719482422, -91.028076171875, -48.89488220214844, 31.991239547729492, 31.855953216552734, -78.09636688232422, -11.325235366821289, 9.29350471496582, 41.43048095703125, 41.44770812988281, 611.4989013671875, 432.7678527832031, -257.4319763183594, -297.7159729003906, 1.3245335817337036, 79.42142486572266, -660.2029418945312, -393.04693603515625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-157.2932', '-5.2368', '36.3946', '37.8648']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-157.293182', yCenter: '-5.236803', height: '36.394608', width: '37.864811', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-60.426304', yCenter: '-23.402267', height: '25.477392', width: '44.424065', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-405.644928', yCenter: '-15.047691', height: '63.240715', width: '-24.346292', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":4}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 4, videoTime: '4.95', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.955555,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":4.955555,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-157.9211883544922, -0.7088581323623657, 34.78861618041992, 37.82280349731445, 5.810164928436279, -20.641374588012695, 13.812337875366211, 18.908374786376953, 36.31071472167969, -106.12506866455078, 30.429433822631836, 98.62802124023438, -48.4318962097168, -59.46509552001953, -23.644643783569336, 26.132474899291992, 44.42439270019531, 44.438209533691406, 79.61900329589844, 444.738525390625, -208.80950927734375, -163.09585571289062, 83.49545288085938, 68.50914001464844, -53.121524810791016, -371.2932434082031, -401.4480285644531, -9.67477798461914, 64.009521484375, -26.067169189453125, -46.748016357421875, -108.41352844238281, 44.08275604248047, 24.661714553833008, 38.10352325439453, -89.15755462646484, -42.06251525878906, 30.640655517578125, 31.569429397583008, -79.3092041015625, -11.192607879638672, 9.659299850463867, 41.375518798828125, 41.39283752441406, 562.8489990234375, 412.0653991699219, -257.3888244628906, -287.8343200683594, -10.484023094177246, 74.46390533447266, -632.0733032226562, -376.5141296386719]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-157.9212', '-0.7089', '34.7886', '37.8228']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-157.921188', yCenter: '-0.708858', height: '34.788616', width: '37.822803', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-59.465096', yCenter: '-23.644644', height: '26.132475', width: '44.424393', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-401.448029', yCenter: '-9.674778', height: '64.009521', width: '-26.067169', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":4,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.00493,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":5.00493,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 5, videoTime: '6.00', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.006938,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.006938,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-148.78970336914062, 0.6733261942863464, 32.43707275390625, 35.427207946777344, 6.285910606384277, -18.107898712158203, 15.621837615966797, 17.020633697509766, 38.35803985595703, -102.80008697509766, 34.59859085083008, 97.4501724243164, -47.29650115966797, -57.48734664916992, -21.466121673583984, 27.669124603271484, 44.41997146606445, 44.43397903442383, 41.29153823852539, 418.161865234375, -198.69166564941406, -152.5297393798828, 74.08200073242188, 64.21524047851562, -26.382177352905273, -347.97821044921875, -369.60107421875, -7.653990745544434, 65.58473205566406, -25.584136962890625, -42.46332931518555, -100.0108871459961, 44.30402374267578, 24.685562133789062, 45.67668533325195, -84.77508544921875, -34.511653900146484, 31.5090274810791, 29.32296371459961, -76.50749206542969, -9.54305362701416, 13.399169921875, 41.624935150146484, 41.64203643798828, 490.2979431152344, 389.5724182128906, -234.6182861328125, -278.1018981933594, -20.868276596069336, 69.40376281738281, -570.2520141601562, -357.7950134277344]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-148.7897', '0.6733', '32.4371', '35.4272']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-148.789703', yCenter: '0.673326', height: '32.437073', width: '35.427208', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-57.487347', yCenter: '-21.466122', height: '27.669125', width: '44.419971', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-369.601074', yCenter: '-7.653991', height: '65.584732', width: '-25.584137', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 5, videoTime: '6.06', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.059795,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.059795,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-125.68093872070312, -2.883552074432373, 29.96855926513672, 29.12253761291504, 8.27600383758545, -15.137168884277344, 21.666122436523438, 12.076188087463379, 37.86246871948242, -93.57206726074219, 35.397132873535156, 89.49974822998047, -40.060611724853516, -52.99576187133789, -17.026283264160156, 27.508010864257812, 44.462032318115234, 44.4759521484375, 13.080971717834473, 440.0451354980469, -156.81956481933594, -154.76824951171875, 68.74491882324219, 74.12539672851562, 11.510337829589844, -355.34161376953125, -303.38775634765625, -8.275857925415039, 62.89464569091797, -20.213245391845703, -33.726585388183594, -80.7440185546875, 47.37173080444336, 18.530214309692383, 47.225677490234375, -77.21319580078125, -26.045480728149414, 29.87265968322754, 24.825687408447266, -67.66960906982422, -7.485870838165283, 15.795312881469727, 42.273216247558594, 42.28944396972656, 375.4364929199219, 426.3359069824219, -167.7235870361328, -277.9317321777344, -22.055265426635742, 76.5710220336914, -437.79180908203125, -382.6834411621094]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-125.6809', '-2.8836', '29.9686', '29.1225']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-125.680939', yCenter: '-2.883552', height: '29.968559', width: '29.122538', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-52.995762', yCenter: '-17.026283', height: '27.508011', width: '44.462032', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-303.387756', yCenter: '-8.275858', height: '62.894646', width: '-20.213245', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 5, videoTime: '6.10', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.101715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.101715,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-118.82313537597656, -1.5920976400375366, 27.18927001953125, 27.698305130004883, 7.573215007781982, -13.903456687927246, 23.530622482299805, 9.259248733520508, 38.17939376831055, -91.61678314208984, 37.71976852416992, 88.62306213378906, -39.264461517333984, -50.48588562011719, -15.393778800964355, 28.185712814331055, 44.466156005859375, 44.480167388916016, -1.740147590637207, 415.6234436035156, -154.76939392089844, -143.8280792236328, 62.883270263671875, 71.66018676757812, 18.267702102661133, -331.5418701171875, -277.8957824707031, -5.914820671081543, 61.84031677246094, -19.49016571044922, -31.677040100097656, -74.0295181274414, 46.232086181640625, 18.870691299438477, 50.76300048828125, -71.76897430419922, -22.59775161743164, 29.0308780670166, 23.395774841308594, -65.38023376464844, -6.320163726806641, 17.120250701904297, 42.53264617919922, 42.54851531982422, 339.14495849609375, 402.8959045410156, -150.84109497070312, -269.24652099609375, -24.925939559936523, 73.32450103759766, -401.1947937011719, -361.7637939453125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-118.8231', '-1.5921', '27.1893', '27.6983']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-118.823135', yCenter: '-1.592098', height: '27.189270', width: '27.698305', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.485886', yCenter: '-15.393779', height: '28.185713', width: '44.466156', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-277.895782', yCenter: '-5.914821', height: '61.840317', width: '-19.490166', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":5}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 5, videoTime: '6.15', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.155863,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.155863,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-127.90992736816406, -4.079179286956787, 29.627046585083008, 30.106678009033203, 9.918570518493652, -15.885050773620605, 20.905874252319336, 11.490754127502441, 38.01296615600586, -95.48463439941406, 34.31160354614258, 89.82978820800781, -41.66090393066406, -52.931976318359375, -17.363628387451172, 28.487760543823242, 44.44978332519531, 44.463741302490234, 28.28746223449707, 445.25616455078125, -167.93125915527344, -153.1588897705078, 70.72740936279297, 75.79232025146484, -4.747846603393555, -357.0665588378906, -307.4406433105469, -9.685907363891602, 61.99346923828125, -20.833045959472656, -32.868595123291016, -82.98439025878906, 47.28907012939453, 17.676774978637695, 46.18492126464844, -78.1689224243164, -28.494539260864258, 28.917522430419922, 25.786598205566406, -68.43657684326172, -7.0824480056762695, 15.744304656982422, 42.22293472290039, 42.23927307128906, 395.82562255859375, 430.3541259765625, -177.2216796875, -278.013671875, -22.02943992614746, 77.4072265625, -460.49200439453125, -385.1991271972656]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-127.9099', '-4.0792', '29.6270', '30.1067']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-127.909927', yCenter: '-4.079179', height: '29.627047', width: '30.106678', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-52.931976', yCenter: '-17.363628', height: '28.487761', width: '44.449783', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-307.440643', yCenter: '-9.685907', height: '61.993469', width: '-20.833046', rawIndex: 8}
detector.ts:369 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {numCoords: 12, reverseOutputOrder: true, xScale: 224, yScale: 224}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:130
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 2250.
    at Object.assertAndGetBroadcastShape (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8081/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8081/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:131
detectPose @ detector.ts:385
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:425 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":5,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.198536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":6.198536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/592d7c16-f219-4c90-8d95..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":6.592398,"paused":true,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
