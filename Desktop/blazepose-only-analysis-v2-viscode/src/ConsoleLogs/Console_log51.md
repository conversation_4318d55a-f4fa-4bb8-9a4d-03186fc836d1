client:495 [vite] connecting...
client:614 [vite] connected.
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: Object
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: Object
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: Object
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: Object
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: Object
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Creating anchors with config: {"numLayers":5,"aspectRatios":[1,2],"interpolatedScaleAspectRatio":1,"strides":[8,16,32,32,32]}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 0, scale: 0.1484, scaleNext: 0.2988, interpolated: 0.2106
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 0 - Generated 3 anchor types (2 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 0: 28x28 (stride: 8)
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 1: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 2: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"standard"}
create_ssd_anchors.ts:136 🔧 SSD ANCHOR 3: {"layer":0,"position":{"x":0,"y":0},"center":{"xCenter":"0.0179","yCenter":"0.0179"},"size":{"width":"1.0000","height":"1.0000"},"anchorType":"interpolated"}
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 1, scale: 0.2988, scaleNext: 0.4492, interpolated: 0.3664
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 1 - Generated 3 anchor types (2 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 1: 14x14 (stride: 16)
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 2, scale: 0.4492, scaleNext: 0.5996, interpolated: 0.5190
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 3, scale: 0.5996, scaleNext: 0.7500, interpolated: 0.6706
create_ssd_anchors.ts:90 🔧 SSD ANCHORS: Added interpolated anchor - Layer 4, scale: 0.7500, scaleNext: 1.0000, interpolated: 0.8660
create_ssd_anchors.ts:103 🔧 SSD ANCHORS: Layer 2 - Generated 9 anchor types (2 standard + 1 interpolated)
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Layer 2: 7x7 (stride: 32)
create_ssd_anchors.ts:153 🔧 SSD ANCHORS: FIXED IMPLEMENTATION - Created 3381 anchors (expected: 2254 with interpolated)
detector.ts:121 🔧 DETECTOR INITIALIZATION: Anchor count verification: {"actualAnchorCount":3381,"expectedAnchorCount":2254,"configurationMatch":false}
detector.ts:134 🔧 DETECTOR INITIALIZATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.00', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 119ms
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0,"paused":false,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
favicon.ico:1  GET http://localhost:8082/favicon.ico 404 (Not Found)
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-130.8863983154297, -6.017020225524902, 30.701818466186523, 29.820510864257812, 9.098145484924316, -14.37855052947998, 19.36659812927246, 12.781549453735352, 37.6097412109375, -95.7075424194336, 29.777772903442383, 89.47322845458984, -42.88039779663086, -53.6333122253418, -17.927043914794922, 28.582067489624023, 44.45489501953125, 44.468650817871094, 23.3839168548584, 434.6966247558594, -150.37757873535156, -155.14463806152344, 70.87877655029297, 72.2372055053711, 7.230284690856934, -353.5482482910156, -319.0079040527344, -11.641759872436523, 61.3012809753418, -21.839712142944336, -35.256099700927734, -84.10852813720703, 46.76162338256836, 17.245298385620117, 43.734222412109375, -80.06254577636719, -31.22454071044922, 29.762184143066406, 23.8579158782959, -68.58247375488281, -7.925967216491699, 15.95534896850586, 42.17305374145508, 42.18927764892578, 410.4118347167969, 414.49444580078125, -176.63784790039062, -275.2091369628906, -18.49203872680664, 74.27591705322266, -469.05523681640625, -374.279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-130.8864', '-6.0170', '30.7018', '29.8205']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.47', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.47494,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.47494,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-96.96778106689453, 3.6180896759033203, 21.72270393371582, 26.665199279785156, 1.884796380996704, -7.798126220703125, 31.054607391357422, 2.5563066005706787, 41.111331939697266, -85.47771453857422, 43.1666374206543, 86.95824432373047, -34.27824020385742, -44.57831954956055, -11.76522445678711, 26.491605758666992, 44.49739074707031, 44.51177215576172, -68.12593078613281, 355.6276550292969, -131.4273681640625, -130.614501953125, 42.14735412597656, 61.73047637939453, 61.956539154052734, -284.3910827636719, -196.77426147460938, 3.7600393295288086, 64.29165649414062, -12.508182525634766, -27.77519989013672, -51.885215759277344, 46.05634689331055, 19.75429916381836, 63.29032516479492, -56.14472579956055, -8.612709999084473, 32.881832122802734, 18.91836929321289, -55.94798278808594, -3.6707823276519775, 20.749521255493164, 43.25529098510742, 43.27050018310547, 230.35336303710938, 335.2873840332031, -96.77014923095703, -248.19932556152344, -22.134557723999023, 65.52135467529297, -273.4869384765625, -300.9039611816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-96.9678', '3.6181', '21.7227', '26.6652']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-96.967781', yCenter: '3.618090', height: '21.722704', width: '26.665199', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-44.578320', yCenter: '-11.765224', height: '26.491606', width: '44.497391', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-196.774261', yCenter: '3.760039', height: '64.291656', width: '-12.508183', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.55', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.548964,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.548964,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-128.05026245117188, 0.40765777230262756, 32.285011291503906, 29.166667938232422, 7.485445022583008, -17.135108947753906, 22.384815216064453, 13.18284797668457, 39.598793029785156, -94.57259368896484, 37.901363372802734, 89.33132934570312, -39.57382583618164, -52.603187561035156, -17.573467254638672, 27.650724411010742, 44.45615005493164, 44.47014617919922, 22.027280807495117, 436.8525085449219, -166.95433044433594, -154.9508819580078, 70.34326934814453, 73.48196411132812, 1.5966635942459106, -352.8862609863281, -304.0143127441406, -5.135889053344727, 66.15099334716797, -20.789918899536133, -33.849021911621094, -83.44107055664062, 48.26042175292969, 20.664873123168945, 48.5452766418457, -80.02388000488281, -24.066364288330078, 29.038488388061523, 25.328645706176758, -69.09473419189453, -8.041793823242188, 15.970258712768555, 42.269134521484375, 42.285404205322266, 398.9991760253906, 431.013916015625, -179.29388427734375, -283.0845642089844, -15.735613822937012, 77.56877899169922, -455.044921875, -387.06378173828125]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-128.0503', '0.4077', '32.2850', '29.1667']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-128.050262', yCenter: '0.407658', height: '32.285011', width: '29.166668', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-52.603188', yCenter: '-17.573467', height: '27.650724', width: '44.456150', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-304.014313', yCenter: '-5.135889', height: '66.150993', width: '-20.789919', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.60789,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.60789,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.61', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.61817,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.61817,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-116.46211242675781, 0.29416272044181824, 30.388273239135742, 27.232494354248047, 4.77235221862793, -13.438980102539062, 25.61029624938965, 11.02080249786377, 41.684242248535156, -88.56101989746094, 37.30879592895508, 87.42111206054688, -36.143497467041016, -50.92005920410156, -15.363595962524414, 27.458200454711914, 44.46733474731445, 44.481346130371094, -4.434924125671387, 405.89068603515625, -140.19638061523438, -150.76043701171875, 61.11369705200195, 69.81212615966797, 25.3893985748291, -327.4649658203125, -268.3204345703125, -3.3490192890167236, 65.66580963134766, -18.00420379638672, -32.411216735839844, -71.1174545288086, 48.25983428955078, 20.489139556884766, 53.87913131713867, -69.79838562011719, -18.331947326660156, 31.533662796020508, 23.084653854370117, -63.29560470581055, -6.232173442840576, 17.96857452392578, 42.644535064697266, 42.66032409667969, 335.63592529296875, 395.6195373535156, -140.6066131591797, -273.6255187988281, -17.081584930419922, 73.56800079345703, -382.0170593261719, -355.82049560546875]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-116.4621', '0.2942', '30.3883', '27.2325']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-116.462112', yCenter: '0.294163', height: '30.388273', width: '27.232494', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.920059', yCenter: '-15.363596', height: '27.458200', width: '44.467335', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-268.320435', yCenter: '-3.349019', height: '65.665810', width: '-18.004204', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.67', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.6691,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.6691,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-134.80844116210938, -3.7768561840057373, 34.92599105834961, 30.409162521362305, 7.819737434387207, -18.503284454345703, 20.98050880432129, 15.828801155090332, 40.627681732177734, -96.9185791015625, 32.54665756225586, 90.7699203491211, -40.34077453613281, -55.39628982543945, -19.06988525390625, 27.834497451782227, 44.44884490966797, 44.46268844604492, 39.03700256347656, 453.2659912109375, -162.31068420410156, -165.43923950195312, 73.28138732910156, 75.01050567626953, -8.534564018249512, -370.1932067871094, -331.442626953125, -9.283123016357422, 67.11241912841797, -21.537391662597656, -36.56487274169922, -89.89106750488281, 48.970375061035156, 20.961502075195312, 47.249732971191406, -82.7468490600586, -30.178359985351562, 29.83698081970215, 27.648202896118164, -71.03723907470703, -9.02031421661377, 15.041457176208496, 42.06366729736328, 42.08020782470703, 439.8226623535156, 440.7823181152344, -191.93582153320312, -290.101318359375, -15.49858283996582, 78.57537078857422, -497.8036193847656, -397.4577331542969]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-134.8084', '-3.7769', '34.9260', '30.4092']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-134.808441', yCenter: '-3.776856', height: '34.925991', width: '30.409163', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.396290', yCenter: '-19.069885', height: '27.834497', width: '44.448845', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-331.442627', yCenter: '-9.283123', height: '67.112419', width: '-21.537392', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.70', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.706204,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.706204,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-137.09254455566406, -4.759131908416748, 34.68738555908203, 31.67345428466797, 7.898149013519287, -15.914597511291504, 18.711639404296875, 16.345922470092773, 39.176673889160156, -95.87313842773438, 28.916370391845703, 90.58160400390625, -41.64961242675781, -55.69544219970703, -19.690656661987305, 27.43570327758789, 44.445167541503906, 44.458885192871094, 39.920875549316406, 447.8717346191406, -153.4259033203125, -165.25767517089844, 74.90496826171875, 72.79608154296875, -3.6263296604156494, -368.8012390136719, -342.2869567871094, -11.908013343811035, 63.187782287597656, -22.51272964477539, -37.830265045166016, -89.52249908447266, 47.672977447509766, 18.77058982849121, 42.20293426513672, -82.43720245361328, -34.31276321411133, 30.046831130981445, 26.570322036743164, -70.73571014404297, -8.787253379821777, 14.288961410522461, 41.96643829345703, 41.982994079589844, 455.1380920410156, 426.2620849609375, -192.05003356933594, -284.7877197265625, -12.784048080444336, 76.3331069946289, -508.4860534667969, -385.8424072265625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-137.0925', '-4.7591', '34.6874', '31.6735']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-137.092545', yCenter: '-4.759132', height: '34.687386', width: '31.673454', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-55.695442', yCenter: '-19.690657', height: '27.435703', width: '44.445168', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-342.286957', yCenter: '-11.908013', height: '63.187782', width: '-22.512730', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '1.77', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.768905,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.768905,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-112.45255279541016, 0.304209440946579, 29.365314483642578, 25.488569259643555, 4.689576148986816, -13.16895866394043, 26.50193214416504, 9.577054977416992, 41.109554290771484, -86.24594116210938, 39.610206604003906, 86.50997924804688, -35.583534240722656, -48.42196273803711, -13.959179878234863, 27.456741333007812, 44.477622985839844, 44.49163055419922, -17.125511169433594, 396.4800109863281, -134.19775390625, -144.76522827148438, 59.89793014526367, 69.25939178466797, 38.13323211669922, -317.2757568359375, -252.29640197753906, -1.697181224822998, 64.64427185058594, -16.89263343811035, -31.73888397216797, -66.93595123291016, 47.400875091552734, 19.97437858581543, 55.986297607421875, -66.41653442382812, -16.449321746826172, 31.256771087646484, 22.507129669189453, -62.28898620605469, -5.65077543258667, 18.00722885131836, 42.79247283935547, 42.80809020996094, 309.049560546875, 379.62310791015625, -130.83836364746094, -266.5263977050781, -20.092933654785156, 70.36378479003906, -357.6086120605469, -343.0279846191406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-112.4526', '0.3042', '29.3653', '25.4886']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-112.452553', yCenter: '0.304209', height: '29.365314', width: '25.488569', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-48.421963', yCenter: '-13.959180', height: '27.456741', width: '44.477623', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-252.296402', yCenter: '-1.697181', height: '64.644272', width: '-16.892633', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.806536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.806536,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.82', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.820761,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.820761,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-161.52500915527344, -4.175033092498779, 36.3852424621582, 37.43192672729492, 6.1599273681640625, -24.95196533203125, 15.419819831848145, 17.94001007080078, 35.41494369506836, -110.129150390625, 26.49599266052246, 98.33189392089844, -48.28162384033203, -60.48078536987305, -23.718469619750977, 26.4218807220459, 44.404293060302734, 44.418094635009766, 120.6304931640625, 466.9261169433594, -217.0728302001953, -172.04273986816406, 90.00537109375, 72.61479187011719, -85.0800552368164, -389.5148010253906, -416.6354675292969, -12.685158729553223, 65.5246810913086, -25.717594146728516, -46.29327392578125, -114.27491760253906, 46.33067321777344, 22.532501220703125, 34.57192611694336, -95.5654525756836, -47.1557731628418, 29.530033111572266, 31.46108055114746, -81.05899810791016, -11.963303565979004, 9.7288179397583, 41.16461181640625, 41.182228088378906, 633.4957275390625, 438.7504577636719, -278.6752014160156, -297.85699462890625, 1.891883134841919, 79.1089859008789, -688.5208740234375, -398.5279541015625]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-161.5250', '-4.1750', '36.3852', '37.4319']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-161.525009', yCenter: '-4.175033', height: '36.385242', width: '37.431927', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-60.480785', yCenter: '-23.718470', height: '26.421881', width: '44.404293', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-416.635468', yCenter: '-12.685159', height: '65.524681', width: '-25.717594', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.86', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.868041,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.868041,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-117.99117279052734, -3.527639150619507, 27.892414093017578, 27.931434631347656, 7.451789379119873, -12.78081226348877, 23.776412963867188, 9.38687801361084, 38.555755615234375, -90.11808013916016, 34.73429870605469, 88.2047119140625, -38.25580978393555, -50.41459655761719, -15.248403549194336, 28.374011993408203, 44.469688415527344, 44.483673095703125, -7.579749584197998, 416.6793518066406, -141.35006713867188, -147.3075408935547, 60.68023681640625, 70.57465362548828, 26.330175399780273, -335.5982971191406, -273.94537353515625, -6.585906982421875, 61.47150802612305, -17.795862197875977, -31.871381759643555, -72.67665100097656, 46.962371826171875, 18.351831436157227, 50.49949264526367, -71.62909698486328, -23.3612060546875, 30.44928741455078, 24.028156280517578, -63.75605773925781, -6.645132541656494, 16.769927978515625, 42.717811584472656, 42.73344421386719, 339.578125, 403.24066162109375, -147.29598999023438, -269.7014465332031, -23.211257934570312, 72.99738311767578, -397.7270202636719, -362.9508361816406]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-117.9912', '-3.5276', '27.8924', '27.9314']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-117.991173', yCenter: '-3.527639', height: '27.892414', width: '27.931435', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.414597', yCenter: '-15.248404', height: '28.374012', width: '44.469688', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-273.945374', yCenter: '-6.585907', height: '61.471508', width: '-17.795862', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.91', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.915734,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.915734,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-118.2226333618164, -0.9667824506759644, 27.823287963867188, 28.292438507080078, 6.301304340362549, -14.235280990600586, 24.687408447265625, 8.82028579711914, 38.515464782714844, -91.42886352539062, 37.62111282348633, 89.18975830078125, -38.4666748046875, -50.71095275878906, -15.411734580993652, 28.059646606445312, 44.460060119628906, 44.474151611328125, -2.0591320991516113, 410.9268798828125, -154.3023223876953, -144.9470977783203, 61.0676383972168, 69.62452697753906, 16.473987579345703, -330.3340759277344, -278.58935546875, -3.7968122959136963, 63.380828857421875, -18.8704891204834, -31.748062133789062, -73.9017562866211, 46.62026596069336, 19.777339935302734, 51.77473831176758, -71.87771606445312, -21.111812591552734, 29.747953414916992, 23.680055618286133, -64.94073486328125, -6.4539031982421875, 17.402196884155273, 42.53852844238281, 42.554466247558594, 345.39410400390625, 400.7725830078125, -154.57028198242188, -269.9444580078125, -21.76801872253418, 73.10189819335938, -403.843994140625, -360.01019287109375]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-118.2226', '-0.9668', '27.8233', '28.2924']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-118.222633', yCenter: '-0.966782', height: '27.823288', width: '28.292439', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-50.710953', yCenter: '-15.411735', height: '28.059647', width: '44.460060', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-278.589355', yCenter: '-3.796812', height: '63.380829', width: '-18.870489', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"570x1014","videoRect":"570.6666870117188x1014.5","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"570x1014","frame":3}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 3, videoTime: '2.96', canvasSize: '570x1014'}
detector.ts:354 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.963228,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.963228,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:357 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:370 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:371 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:377 🔍 Raw tensor data sample (first 52 values): (52) [-111.15109252929688, -0.7564656138420105, 28.02439308166504, 24.996166229248047, 4.174032211303711, -12.447470664978027, 27.1287784576416, 8.378366470336914, 40.641822814941406, -86.77392578125, 38.26259994506836, 86.99799346923828, -35.17338180541992, -48.21865463256836, -13.446538925170898, 27.8856258392334, 44.46710205078125, 44.48115539550781, -31.395570755004883, 386.2037658691406, -126.84266662597656, -140.88597106933594, 54.269798278808594, 66.38798522949219, 46.46969223022461, -310.2815856933594, -252.24339294433594, -2.5631895065307617, 63.514041900634766, -16.9388484954834, -31.11656951904297, -65.35975646972656, 46.21685028076172, 20.570419311523438, 56.51769256591797, -65.52603149414062, -17.55021095275879, 31.2783203125, 21.601497650146484, -61.97418212890625, -5.870743751525879, 18.961612701416016, 42.768699645996094, 42.7843132019043, 300.9669189453125, 370.0926208496094, -125.14498138427734, -262.41143798828125, -21.541629791259766, 69.0040054321289, -350.1705017089844, -334.3716125488281]
detector_result.ts:174 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
detector_result.ts:199 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-111.1511', '-0.7565', '28.0244', '24.9962']
detector_result.ts:203 ✅ DETECTOR RESULT: Tensor indexing validation passed
detector_result.ts:216 🔧 DETECTOR RESULT: Slicing completed successfully
detector_result.ts:217 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
detector_result.ts:218 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
detector_result.ts:241 ✅ DETECTOR RESULT: Tensor slicing validation passed
detector_result.ts:244 🔧 DETECTOR RESULT: Starting coordinate transformation validation
detector_result.ts:245 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 0 coordinates from tensor: {xCenter: '-111.151093', yCenter: '-0.756466', height: '28.024393', width: '24.996166', rawIndex: 0}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 1 coordinates from tensor: {xCenter: '-48.218655', yCenter: '-13.446539', height: '27.885626', width: '44.467102', rawIndex: 4}
detector_result.ts:279 🔍 PIPELINE TRACE STEP 2: Detection 2 coordinates from tensor: {xCenter: '-252.243393', yCenter: '-2.563190', height: '63.514042', width: '-16.938848', rawIndex: 8}
detector.ts:385 🔍 DETECTOR: detectorResult returned: {boxes: {…}, logits: {…}}
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:60 🔍 SURGICAL IMPLEMENTATION: Tensor types and shapes: {rawScoreTensor: {…}, rawBoxTensor: {…}}rawBoxTensor: constructor: "Tensor"isTensor: trueshape: (2) [2254, 4]type: "object"[[Prototype]]: ObjectrawScoreTensor: constructor: "Tensor"isTensor: trueshape: (2) [2254, 1]type: "object"[[Prototype]]: Object[[Prototype]]: Object
tensors_to_detections_reference.ts:83 🔍 SURGICAL IMPLEMENTATION: About to decode boxes with config: {"numCoords":12,"reverseOutputOrder":true,"xScale":224,"yScale":224,"numBoxes":2254}
tensors_to_detections_reference.ts:92 🔍 SURGICAL IMPLEMENTATION: Anchor tensor shapes: {"anchorX":{"shape":[3381],"size":3381},"anchorY":{"shape":[3381],"size":3381},"anchorW":{"shape":[3381],"size":3381},"anchorH":{"shape":[3381],"size":3381}}
tensors_to_detections_reference.ts:100 🔍 SURGICAL IMPLEMENTATION: Raw tensor shapes: {"rawBoxTensor":{"shape":[2254,4],"size":9016},"rawScoreTensor":{"shape":[2254,1],"size":2254}}
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error during tensor processing: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:145
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔍 SURGICAL IMPLEMENTATION: Error stack: Error: Operands could not be broadcast together with shapes 2254 and 3381.
    at Object.assertAndGetBroadcastShape (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:7345:13)
    at new BinaryOpPackedProgram (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29541:45)
    at Object.multiply3 [as kernelFunc] (http://localhost:8082/node_modules/.vite/deps/@tensorflow_tfjs.js?v=b432580d:29967:15)
    at kernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4034:22)
    at http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4078:21
    at _Engine.scopedRun (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3927:19)
    at _Engine.runKernelFunc (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4072:10)
    at _Engine.runKernel (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:3988:17)
    at mul_ (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:6158:17)
    at Module.mul__op (http://localhost:8082/node_modules/.vite/deps/chunk-AFFYF5PH.js?v=b432580d:4676:22)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
tensorsToDetectionsReference @ tensors_to_detections_reference.ts:146
detectPose @ detector.ts:401
await in detectPose
estimatePoses @ detector.ts:194
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
detector.ts:441 🔧 DETECTOR: No valid detections after filtering
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":3,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.01012,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":3.01012,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8082/9723e4d6-faf9-45ab-a935..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":3.515163,"paused":true,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
