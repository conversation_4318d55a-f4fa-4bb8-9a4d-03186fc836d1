client:495 [vite] connecting...
client:614 [vite] connected.
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
client:223 [vite] hot updated: /src/index.css
client:223 [vite] hot updated: /src/components/SideViewBlazePoseOverlay.tsx
Index.tsx:37 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
Index.tsx:38 analysisMode: 3D
Index.tsx:39 activityType: Running
Index.tsx:40 videoSetup: Treadmill
Index.tsx:41 analysisQuality: Full
Index.tsx:42 userHeight: {feet: 5, inches: 10}
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
PoseOverlay.tsx:38 === BLAZEPOSE FULL MODEL ===
PoseOverlay.tsx:39 ✅ 3D Running Analysis - BlazePose Full
PoseOverlay.tsx:40 Props: {analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', userHeight: {…}}
PoseOverlay.tsx:41 ============================
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
useBlazePoseDetection.ts:64 🧪 E2E TEST: BlazePose detection hook initializing with quality: Full
constants.ts:236 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
constants.ts:237 🔍 Detector: 1 verified: 2024-01-07
constants.ts:238 🔍 Landmark Full: 1 verified: 2024-01-07
constants.ts:239 🔍 Landmark Lite: 1 verified: 2024-01-07
constants.ts:240 🔍 Landmark Heavy: 1 verified: 2024-01-07
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
constants.ts:249 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates Error Component Stack
    at SideViewBlazePoseOverlay (SideViewBlazePoseOverlay.tsx:17:3)
    at PoseOverlay.tsx:17:3
    at div (<anonymous>)
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at VideoPlayer.tsx:19:3
    at div (<anonymous>)
    at _c8 (card.tsx:62:6)
    at div (<anonymous>)
    at _c (card.tsx:8:6)
    at div (<anonymous>)
    at div (<anonymous>)
    at div (<anonymous>)
    at ResultsPanel.tsx:24:3
    at Index (Index.tsx:19:43)
    at RenderedRoute (react-router-dom.js?v=b432580d:4069:5)
    at Routes (react-router-dom.js?v=b432580d:4508:5)
    at Router (react-router-dom.js?v=b432580d:4451:15)
    at BrowserRouter (react-router-dom.js?v=b432580d:5196:5)
    at Provider (chunk-WIFN2VF7.js?v=b432580d:28:15)
    at TooltipProvider (@radix-ui_react-tooltip.js?v=b432580d:2288:5)
    at QueryClientProvider (@tanstack_react-query.js?v=b432580d:2794:3)
    at App (<anonymous>)
overrideMethod @ hook.js:608
(anonymous) @ constants.ts:249
checkModelVersions @ constants.ts:246
initBlazePose @ useBlazePoseDetection.ts:67
(anonymous) @ useBlazePoseDetection.ts:121
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
logging_system.ts:288 🔧 LOGGING: Enabling production logging configuration
logging_system.ts:290 🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session
logging_system.ts:310 🔧 LOGGING: Using production logging in development for performance optimization
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":false,"hasVideo":true,"hasCanvas":true,"videoReadyState":0,"videoDimensions":"0x0","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:74 ⏸️ DETECTION LOOP: BlazePose not initialized yet
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'side', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
VideoPlayer.tsx:131 🎥 VideoPlayer props updated - BLAZEPOSE FULL MODEL: {analysisMode: '3D', analysisType: 'running', viewType: 'rear', videoSetup: 'Treadmill', modelQuality: 'Full', …}
VideoPlayer.tsx:137 🧪 E2E TEST: VideoPlayer component updated (should be minimal, not infinite)
useBlazePoseDetection.ts:75 ✅ WebGL backend ready for Phase 4 BlazePose
useBlazePoseDetection.ts:83 📊 TensorFlow.js Backend Info: {backend: 'webgl', memory: {…}, platform: PlatformBrowser}
detector_utils.ts:23 🔧 MODEL CONFIG: Validating BlazePose model configuration
detector_utils.ts:30 🔧 MODEL CONFIG: Input model type: full
detector_utils.ts:31 🔧 MODEL CONFIG: Default model type: full
detector_utils.ts:58 ✅ MODEL CONFIG: Final model type selected: full
detector_utils.ts:81 ✅ MODEL CONFIG: Final configuration: {modelType: 'full', enableSmoothing: true, enableSegmentation: false, detectorUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...', landmarkUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...'}
create_ssd_anchors.ts:44 🔧 SSD ANCHORS: Creating anchors with config: {reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: Array(6), featureMapWidth: Array(6), numLayers: 6, …}
create_ssd_anchors.ts:45 🔧 SSD ANCHORS: Feature map dimensions: {heights: Array(6), widths: Array(6), numLayers: 6, aspectRatios: Array(2)}
create_ssd_anchors.ts:53 🔧 SSD ANCHORS: CRITICAL DEBUG - Anchor offsets: {anchorOffsetX: 0.5, anchorOffsetY: 0.5, potentialForNegativeX: false, potentialForNegativeY: false}
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 0: 28x28
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 1: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}calculated: {xCenter: 0.017857142857142856, yCenter: 0.017857142857142856, width: 0.1484375, height: 0.1484375}featureMapSize: {width: 28, height: 28}gridPosition: {x: 0, y: 0}isNegative: {x: false, y: false}isOutOfRange: {x: false, y: false}layer: 0offsets: {x: 0.5, y: 0.5}[[Prototype]]: Object
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 2: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}calculated: {xCenter: 0.017857142857142856, yCenter: 0.017857142857142856, width: 0.2099223256647563, height: 0.10496116283237814}featureMapSize: {width: 28, height: 28}gridPosition: {x: 0, y: 0}isNegative: {x: false, y: false}isOutOfRange: {x: false, y: false}layer: 0offsets: {x: 0.5, y: 0.5}[[Prototype]]: Object
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 3: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}calculated: {xCenter: 0.05357142857142857, yCenter: 0.017857142857142856, width: 0.1484375, height: 0.1484375}featureMapSize: {width: 28, height: 28}gridPosition: {x: 1, y: 0}isNegative: {x: false, y: false}isOutOfRange: {x: false, y: false}layer: 0offsets: {x: 0.5, y: 0.5}[[Prototype]]: Object
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 4: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}calculated: {xCenter: 0.05357142857142857, yCenter: 0.017857142857142856, width: 0.2099223256647563, height: 0.10496116283237814}featureMapSize: {width: 28, height: 28}gridPosition: {x: 1, y: 0}isNegative: {x: false, y: false}isOutOfRange: {x: false, y: false}layer: 0offsets: {x: 0.5, y: 0.5}[[Prototype]]: Object
create_ssd_anchors.ts:87 🔧 SSD ANCHOR 5: Coordinates check {layer: 0, gridPosition: {…}, featureMapSize: {…}, offsets: {…}, calculated: {…}, …}calculated: {xCenter: 0.08928571428571429, yCenter: 0.017857142857142856, width: 0.1484375, height: 0.1484375}featureMapSize: {width: 28, height: 28}gridPosition: {x: 2, y: 0}isNegative: {x: false, y: false}isOutOfRange: {x: false, y: false}layer: 0offsets: {x: 0.5, y: 0.5}[[Prototype]]: Object
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 1: 16x16
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 2: 8x8
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 3: 4x4
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 4: 2x2
create_ssd_anchors.ts:67 🔧 SSD ANCHORS: Layer 5: 1x1
create_ssd_anchors.ts:118 🔧 SSD ANCHORS: Created 2250 anchors
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0,"duration":9.496667,"paused":true,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
VideoPlayer.tsx:64 🧪 E2E TEST: Video play/pause toggled {isPlaying: false}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '0.00', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:176 [Violation] 'requestAnimationFrame' handler took 363ms
SideViewBlazePoseOverlay.tsx:121 📹 VIDEO EVENT: play {"readyState":4,"currentTime":0.329993,"paused":false,"videoWidth":1080,"videoHeight":1920}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.332999,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":0.332999,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
favicon.ico:1  GET http://localhost:8081/favicon.ico 404 (Not Found)
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-130.8863983154297, -6.017020225524902, 30.701818466186523, 29.820510864257812, 9.098145484924316, -14.37855052947998, 19.36659812927246, 12.781549453735352, 37.6097412109375, -95.7075424194336, 29.777772903442383, 89.47322845458984, -42.88039779663086, -53.6333122253418, -17.927043914794922, 28.582067489624023, 44.45489501953125, 44.468650817871094, 23.3839168548584, 434.6966247558594, -150.37757873535156, -155.14463806152344, 70.87877655029297, 72.2372055053711, 7.230284690856934, -353.5482482910156, -319.0079040527344, -11.641759872436523, 61.3012809753418, -21.839712142944336, -35.256099700927734, -84.10852813720703, 46.76162338256836, 17.245298385620117, 43.734222412109375, -80.06254577636719, -31.22454071044922, 29.762184143066406, 23.8579158782959, -68.58247375488281, -7.925967216491699, 15.95534896850586, 42.17305374145508, 42.18927764892578, 410.4118347167969, 414.49444580078125, -176.63784790039062, -275.2091369628906, -18.49203872680664, 74.27591705322266, -469.05523681640625, -374.279541015625]
error_handler.js:1 🔧 DETECTOR RESULT: Error processing detector result: TypeError: Cannot read properties of undefined (reading 'shape')
    at detectorResult (detector_result.ts:57:20)
    at BlazePoseTfjsDetector.detectPose (detector.ts:366:29)
    at async BlazePoseTfjsDetector.estimatePoses (detector.ts:178:26)
    at async detectPoses (useBlazePoseDetection.ts:184:21)
    at async detectPose (SideViewBlazePoseOverlay.tsx:306:23)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:435
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:436
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:440
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:44 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.551244,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":1}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 1, videoTime: '1.56', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.558886,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.558886,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-96.17166137695312, 0.7779648900032043, 21.457332611083984, 24.410385131835938, 4.413607597351074, -6.728487491607666, 30.06456184387207, 1.7833492755889893, 40.34856414794922, -84.2351303100586, 43.07562255859375, 86.24786376953125, -35.614967346191406, -44.208560943603516, -10.396217346191406, 28.429534912109375, 44.485557556152344, 44.49988555908203, -78.7795639038086, 348.08837890625, -120.04328918457031, -121.51940155029297, 43.996822357177734, 62.13920593261719, 78.95075988769531, -273.435791015625, -192.58584594726562, 0.4322059452533722, 61.61865234375, -13.989748001098633, -25.323755264282227, -50.90928649902344, 45.359439849853516, 17.477306365966797, 61.77622985839844, -57.128074645996094, -7.847626686096191, 32.02854919433594, 16.22237205505371, -55.389163970947266, -2.840447425842285, 22.363842010498047, 43.291847229003906, 43.30692672729492, 220.0712890625, 329.49591064453125, -92.9949722290039, -238.05699157714844, -20.948532104492188, 63.79200744628906, -261.4438781738281, -294.5377502441406]
error_handler.js:1 🔧 DETECTOR RESULT: Error processing detector result: TypeError: Cannot read properties of undefined (reading 'shape')
    at detectorResult (detector_result.ts:57:20)
    at BlazePoseTfjsDetector.detectPose (detector.ts:366:29)
    at async BlazePoseTfjsDetector.estimatePoses (detector.ts:178:26)
    at async detectPoses (useBlazePoseDetection.ts:184:21)
    at async detectPose (SideViewBlazePoseOverlay.tsx:306:23)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:435
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:436
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
error_handler.js:1 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:440
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:215
requestAnimationFrame
detectPose @ SideViewBlazePoseOverlay.tsx:187
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:44 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":1,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.606585,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":1.606585,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:551 🚀 DETECTION LOOP: Starting pose detection loop now!
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.61', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.612632,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.612632,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-139.07106018066406, -4.81708288192749, 34.81623077392578, 30.587181091308594, 9.31594181060791, -18.941129684448242, 19.214345932006836, 15.287081718444824, 38.547943115234375, -97.03435516357422, 29.799837112426758, 88.40646362304688, -41.523746490478516, -54.17269515991211, -19.265371322631836, 27.50203514099121, 44.43318176269531, 44.446929931640625, 61.78240203857422, 461.605712890625, -166.05401611328125, -164.69358825683594, 79.05537414550781, 76.171630859375, -23.16260528564453, -376.47161865234375, -340.8684997558594, -10.748628616333008, 63.79055404663086, -23.185409545898438, -36.347652435302734, -92.56307983398438, 47.46870422363281, 18.665834426879883, 41.303131103515625, -84.82254791259766, -35.016639709472656, 26.310497283935547, 27.5191593170166, -72.43891143798828, -9.010404586791992, 13.283675193786621, 41.985260009765625, 42.00175476074219, 474.7171325683594, 442.01361083984375, -202.18780517578125, -288.2191162109375, -11.028167724609375, 76.73332214355469, -528.1290893554688, -401.4677429199219]
error_handler.js:1 🔧 DETECTOR RESULT: Error processing detector result: TypeError: Cannot read properties of undefined (reading 'shape')
    at detectorResult (detector_result.ts:57:20)
    at BlazePoseTfjsDetector.detectPose (detector.ts:366:29)
    at async BlazePoseTfjsDetector.estimatePoses (detector.ts:178:26)
    at async detectPoses (useBlazePoseDetection.ts:184:21)
    at async detectPose (SideViewBlazePoseOverlay.tsx:306:23)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:435
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:436
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:440
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:44 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
SideViewBlazePoseOverlay.tsx:298 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.68', canvasSize: '571x1015'}
detector.ts:338 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.683072,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
SideViewBlazePoseOverlay.tsx:557 🧹 DETECTION LOOP: Cleaning up detection loop
SideViewBlazePoseOverlay.tsx:59 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
SideViewBlazePoseOverlay.tsx:68 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
SideViewBlazePoseOverlay.tsx:71 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
SideViewBlazePoseOverlay.tsx:88 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
SideViewBlazePoseOverlay.tsx:99 ✅ DETECTION LOOP: All components ready, initializing detection loop
SideViewBlazePoseOverlay.tsx:110 📹 VIDEO STATE: {"readyState":4,"currentTime":2.683072,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
SideViewBlazePoseOverlay.tsx:549 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
detector.ts:341 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
detector.ts:354 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
detector.ts:355 🔍 Raw tensor shape: (3) [1, 2254, 13]
detector.ts:361 🔍 Raw tensor data sample (first 52 values): (52) [-172.46609497070312, -4.092321872711182, 40.34149169921875, 38.61773681640625, 6.045773983001709, -28.459945678710938, 13.172086715698242, 21.983428955078125, 35.350799560546875, -112.90956115722656, 22.66602325439453, 98.68367767333984, -48.52381134033203, -62.9459228515625, -25.466964721679688, 25.667680740356445, 44.38923645019531, 44.40292739868164, 159.50582885742188, 473.53564453125, -226.01708984375, -178.81202697753906, 99.53173065185547, 73.65528869628906, -111.43595123291016, -396.8439636230469, -448.4565734863281, -14.32230281829834, 66.62190246582031, -27.931001663208008, -50.28094482421875, -125.1294937133789, 46.19797897338867, 24.711767196655273, 30.0921630859375, -100.61001586914062, -55.164100646972656, 29.92505645751953, 35.43861770629883, -85.11859130859375, -12.946258544921875, 7.38214111328125, 40.91014862060547, 40.928009033203125, 725.9146728515625, 433.5286560058594, -308.412109375, -302.9953308105469, 13.005881309509277, 80.39009094238281, -770.197021484375, -393.9493713378906]
error_handler.js:1 🔧 DETECTOR RESULT: Error processing detector result: TypeError: Cannot read properties of undefined (reading 'shape')
    at detectorResult (detector_result.ts:57:20)
    at BlazePoseTfjsDetector.detectPose (detector.ts:366:29)
    at async BlazePoseTfjsDetector.estimatePoses (detector.ts:178:26)
    at async detectPoses (useBlazePoseDetection.ts:184:21)
    at async detectPose (SideViewBlazePoseOverlay.tsx:306:23)
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:435
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:436
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
error_handler.js:1 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ hook.js:608
detectorResult @ detector_result.ts:440
detectPose @ detector.ts:366
await in detectPose
estimatePoses @ detector.ts:178
detectPoses @ useBlazePoseDetection.ts:184
detectPose @ SideViewBlazePoseOverlay.tsx:306
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:540
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:539
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:552
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:550
commitHookEffectListMount @ chunk-R6S4VRB5.js?v=b432580d:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js?v=b432580d:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js?v=b432580d:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js?v=b432580d:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js?v=b432580d:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js?v=b432580d:19490
flushPassiveEffects @ chunk-R6S4VRB5.js?v=b432580d:19447
(anonymous) @ chunk-R6S4VRB5.js?v=b432580d:19328
workLoop @ chunk-R6S4VRB5.js?v=b432580d:197
flushWork @ chunk-R6S4VRB5.js?v=b432580d:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js?v=b432580d:384
tensors_to_detections_reference.ts:39 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
tensors_to_detections_reference.ts:44 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
SideViewBlazePoseOverlay.tsx:315 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
SideViewBlazePoseOverlay.tsx:167 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
SideViewBlazePoseOverlay.tsx:271 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.71', canvasSize: '571x1015'}
 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.715186,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.715186,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
 🔍 Raw tensor shape: (3) [1, 2254, 13]
 🔍 Raw tensor data sample (first 52 values): (52) [-140.45260620117188, -4.54355525970459, 34.75094985961914, 32.49273681640625, 8.899796485900879, -19.091793060302734, 19.2739200592041, 15.410252571105957, 39.0397834777832, -99.87772369384766, 30.850460052490234, 91.69127655029297, -42.66804885864258, -56.380008697509766, -20.109825134277344, 27.937049865722656, 44.43765640258789, 44.45147705078125, 59.40297317504883, 457.4595642089844, -175.6001434326172, -162.72801208496094, 78.11051177978516, 74.42808532714844, -26.72856903076172, -374.10882568359375, -348.4704895019531, -11.79498291015625, 65.64744567871094, -22.900053024291992, -36.99563980102539, -95.91888427734375, 48.8543586730957, 18.97856330871582, 43.412906646728516, -87.44194793701172, -34.349037170410156, 29.13753890991211, 27.94795036315918, -72.45767211914062, -9.160806655883789, 14.61790943145752, 41.937225341796875, 41.953895568847656, 488.8231506347656, 441.84698486328125, -216.10426330566406, -285.713134765625, -11.075161933898926, 78.41167449951172, -547.140625, -397.1869812011719]
 🔧 DETECTOR RESULT: Error processing detector result: 
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:470
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:471
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:475
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
 [04:50:35.007] ERROR:DETECTION Pose detection error (×5) {"error":"Size(2250) must match the product of shape 2254,1"}
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
logImmediate @ logging_system.ts:107
(anonymous) @ logging_system.ts:144
(anonymous) @ logging_system.ts:142
flushQueue @ logging_system.ts:139
addToQueue @ logging_system.ts:97
error @ logging_system.ts:164
detectPoses @ useBlazePoseDetection.ts:217
await in detectPoses
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
 🎨 CANVAS POSITIONING: {"canvasSize":"571x1015","videoRect":"571.03515625x1015.15625","position":"0px, 0px","zIndex":"10"}
 🧪 E2E TEST: Drawing canvas test indicators {"canvasSize":"571x1015","frame":2}
 🎯 DETECTION LOOP: Starting pose detection {frame: 2, videoTime: '2.76', canvasSize: '571x1015'}
 🔍 PIPELINE TRACE: Model input tensor shape: (4) [1, 224, 224, 3]
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.761495,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.761495,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🔍 PIPELINE TRACE: Model input range: {min: -1, max: 1, expectedRange: '[-1, 1]'}
 🔍 PIPELINE TRACE STEP 1: RAW TENSOR FROM MODEL
 🔍 Raw tensor shape: (3) [1, 2254, 13]
 🔍 Raw tensor data sample (first 52 values): (52) [-179.14144897460938, -5.632275581359863, 38.68685531616211, 41.542015075683594, 4.0265655517578125, -27.791322708129883, 11.886754035949707, 22.96377944946289, 34.99637985229492, -115.61763763427734, 19.937898635864258, 104.96820831298828, -50.31924057006836, -64.02435302734375, -26.38846778869629, 25.368549346923828, 44.3851432800293, 44.39889907836914, 153.9429473876953, 464.5542907714844, -228.57083129882812, -180.58961486816406, 97.75735473632812, 73.4137191772461, -110.41232299804688, -389.93646240234375, -469.4381408691406, -17.613916397094727, 62.87086868286133, -25.665002822875977, -54.110191345214844, -129.1725311279297, 45.05697250366211, 27.182662963867188, 30.1998348236084, -101.93568420410156, -61.66376495361328, 36.841678619384766, 38.2952766418457, -87.11043548583984, -13.662764549255371, 6.113003253936768, 40.658809661865234, 40.677207946777344, 761.1864624023438, 410.8434143066406, -330.03192138671875, -302.3603515625, 10.855303764343262, 82.90016174316406, -815.2200927734375, -369.11029052734375]
 🔧 DETECTOR RESULT: Error processing detector result: 
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:470
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔧 DETECTOR RESULT: Input tensor count: undefined
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:471
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔧 DETECTOR RESULT: Returning zero tensors due to processing error
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
detectorResult @ detector_result.ts:475
detectPose @ detector.ts:294
await in detectPose
estimatePoses @ detector.ts:124
detectPoses @ useBlazePoseDetection.ts:163
detectPose @ SideViewBlazePoseOverlay.tsx:268
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
requestAnimationFrame
(anonymous) @ SideViewBlazePoseOverlay.tsx:474
setTimeout
detectPose @ SideViewBlazePoseOverlay.tsx:473
await in detectPose
(anonymous) @ SideViewBlazePoseOverlay.tsx:485
setTimeout
(anonymous) @ SideViewBlazePoseOverlay.tsx:483
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
 🔍 SURGICAL IMPLEMENTATION: Starting reference tensorsToDetections
 🔍 SURGICAL IMPLEMENTATION: Tensor shapes: {rawScoreTensor: Array(2), rawBoxTensor: Array(2)}
 📊 DETECTION LOOP: Pose detection result {"frame":2,"posesFound":0,"firstPoseKeypoints":0}
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.800826,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🧹 DETECTION LOOP: Cleaning up detection loop
 🚀 DETECTION LOOP INITIALIZATION - Starting useEffect
 📊 DETECTION LOOP STATE: {"isInitialized":true,"hasVideo":true,"hasCanvas":true,"videoReadyState":4,"videoDimensions":"1080x1920","videoSrc":"Has source"}
 🧪 E2E TEST: SideViewBlazePoseOverlay component initialized successfully
 ✅ DETECTION LOOP: All prerequisites met, proceeding with initialization
 ✅ DETECTION LOOP: All components ready, initializing detection loop
 📹 VIDEO STATE: {"readyState":4,"currentTime":2.800826,"duration":9.496667,"paused":false,"ended":false,"videoWidth":1080,"videoHeight":1920,"src":"blob:http://localhost:8081/d46a62f9-124d-4b7f-8dac..."}
 ⏰ DETECTION LOOP: Setting up 1-second delay before starting detection
 🧪 E2E TEST: Video play/pause toggled {isPlaying: true}
 📹 VIDEO EVENT: pause {"readyState":4,"currentTime":3.694886,"paused":true,"videoWidth":1080,"videoHeight":1920}
 🚀 DETECTION LOOP: Starting pose detection loop now!
 [04:50:37.244] ERROR:DETECTION Pose detection error {"error":"Size(2250) must match the product of shape 2254,1"}
wr.error @ error_handler.js:1
overrideMethod @ installHook.js:1
logImmediate @ logging_system.ts:107
(anonymous) @ logging_system.ts:144
(anonymous) @ logging_system.ts:142
flushQueue @ logging_system.ts:139
(anonymous) @ logging_system.ts:153
setInterval
startBatchTimer @ logging_system.ts:152
restartBatchTimer @ logging_system.ts:160
updateConfig @ logging_system.ts:63
enableProductionLogging @ logging_system.ts:227
initializeOptimalLogging @ logging_system.ts:240
(anonymous) @ SideViewBlazePoseOverlay.tsx:40
commitHookEffectListMount @ chunk-R6S4VRB5.js:16915
commitPassiveMountOnFiber @ chunk-R6S4VRB5.js:18156
commitPassiveMountEffects_complete @ chunk-R6S4VRB5.js:18129
commitPassiveMountEffects_begin @ chunk-R6S4VRB5.js:18119
commitPassiveMountEffects @ chunk-R6S4VRB5.js:18109
flushPassiveEffectsImpl @ chunk-R6S4VRB5.js:19490
flushPassiveEffects @ chunk-R6S4VRB5.js:19447
(anonymous) @ chunk-R6S4VRB5.js:19328
workLoop @ chunk-R6S4VRB5.js:197
flushWork @ chunk-R6S4VRB5.js:176
performWorkUntilDeadline @ chunk-R6S4VRB5.js:384
