/**
 * Visual keypoint filtering utilities for BlazePose overlay display
 * Provides visual-only filtering while preserving full pipeline processing
 */

import { BLAZEPOSE_CORE_KEYPOINT_INDICES } from '@/blazepose_tfjs/constants';

/**
 * Filter keypoints for visual display only - keeps core body landmarks for running analysis
 * This preserves full pipeline processing while showing only essential keypoints in overlay
 * 
 * Filters out:
 * - Eye detail keypoints (left_eye_inner, left_eye_outer, right_eye_inner, right_eye_outer)
 * - Mouth keypoints (mouth_left, mouth_right)
 * - Finger keypoints (left_pinky, right_pinky, left_index, right_index, left_thumb, right_thumb)
 * - Wrist detail points (left_wrist_pinky, left_wrist_index, etc.)
 * 
 * Preserves:
 * - Nose, ears, shoulders, elbows, wrists, hips, knees, ankles, heels, feet
 */
export function filterKeypointsForDisplay(keypoints: any[]): any[] {
  return keypoints.filter((_, index) => BLAZEPOSE_CORE_KEYPOINT_INDICES.includes(index));
}

/**
 * Map connections to work with filtered keypoint indices
 * This ensures skeletal connections work correctly with the filtered keypoint array
 */
export function mapConnectionsToFilteredIndices(connections: number[][]): number[][] {
  return connections
    .map(([startIdx, endIdx]) => {
      const filteredStartIdx = BLAZEPOSE_CORE_KEYPOINT_INDICES.indexOf(startIdx);
      const filteredEndIdx = BLAZEPOSE_CORE_KEYPOINT_INDICES.indexOf(endIdx);
      
      // Only include connections where both keypoints are in the core set
      if (filteredStartIdx !== -1 && filteredEndIdx !== -1) {
        return [filteredStartIdx, filteredEndIdx];
      }
      return null;
    })
    .filter(connection => connection !== null) as number[][];
}

/**
 * Get the count of keypoints that will be displayed after filtering
 */
export function getDisplayKeypointCount(): number {
  return BLAZEPOSE_CORE_KEYPOINT_INDICES.length;
}

/**
 * Check if a keypoint index will be displayed after filtering
 */
export function isKeypointDisplayed(index: number): boolean {
  return BLAZEPOSE_CORE_KEYPOINT_INDICES.includes(index);
}
