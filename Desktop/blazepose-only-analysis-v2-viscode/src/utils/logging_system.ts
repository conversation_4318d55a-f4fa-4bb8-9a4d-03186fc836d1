/**
 * Configurable logging system for BlazePose pipeline
 * TASK 3: Optimize performance and reduce excessive debug logging
 */

export enum LogLevel {
  SILENT = 0,
  ERROR = 1,
  WARN = 2,
  INFO = 3,
  DEBUG = 4,
  VERBOSE = 5
}

export interface LogConfig {
  globalLevel: LogLevel;
  componentLevels: {
    tensorProcessing: LogLevel;
    detection: LogLevel;
    overlay: LogLevel;
    performance: LogLevel;
    memory: LogLevel;
    coordinates: LogLevel;
  };
  enableBatching: boolean;
  batchSize: number;
  batchInterval: number;
  enableSampling: boolean;
  sampleRate: number; // 0.1 = log 10% of messages
}

export const DEFAULT_LOG_CONFIG: LogConfig = {
  globalLevel: LogLevel.WARN, // TASK 3: Reduced from INFO to WARN
  componentLevels: {
    tensorProcessing: LogLevel.ERROR, // TASK 3: Heavily reduced
    detection: LogLevel.WARN,
    overlay: LogLevel.INFO,
    performance: LogLevel.INFO,
    memory: LogLevel.WARN,
    coordinates: LogLevel.ERROR // TASK 3: Heavily reduced
  },
  enableBatching: true,
  batchSize: 10,
  batchInterval: 1000, // 1 second
  enableSampling: true,
  sampleRate: 0.1 // Log only 10% of verbose messages
};

// TASK 5: Enhanced production logging configuration for <50 lines per session
export const PRODUCTION_LOG_CONFIG: LogConfig = {
  globalLevel: LogLevel.ERROR, // Only errors in production
  componentLevels: {
    tensorProcessing: LogLevel.SILENT, // Completely silent - major source of spam
    detection: LogLevel.ERROR, // Only detection errors
    overlay: LogLevel.ERROR, // Only overlay errors
    performance: LogLevel.WARN, // Performance warnings only
    memory: LogLevel.ERROR, // Only memory errors
    coordinates: LogLevel.SILENT // Completely silent - major source of spam
  },
  enableBatching: true,
  batchSize: 5, // TASK 5: Smaller batches for immediate error visibility
  batchInterval: 5000, // TASK 5: Less frequent batching
  enableSampling: true,
  sampleRate: 0.01 // TASK 5: Only 1% of messages to minimize output
};

export type LogComponent = keyof LogConfig['componentLevels'];

class LoggingSystem {
  private config: LogConfig;
  private messageQueue: Array<{ level: LogLevel; component: LogComponent; message: string; data?: any; timestamp: number }> = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private messageCount: { [key in LogComponent]: number } = {
    tensorProcessing: 0,
    detection: 0,
    overlay: 0,
    performance: 0,
    memory: 0,
    coordinates: 0
  };
  private lastFlushTime: number = Date.now();

  constructor(config: LogConfig = DEFAULT_LOG_CONFIG) {
    this.config = config;
    this.startBatchTimer();
  }

  updateConfig(newConfig: Partial<LogConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.restartBatchTimer();
  }

  private shouldLog(level: LogLevel, component: LogComponent): boolean {
    // Check global level
    if (level > this.config.globalLevel) return false;
    
    // Check component-specific level
    if (level > this.config.componentLevels[component]) return false;
    
    // Apply sampling for verbose messages
    if (this.config.enableSampling && level >= LogLevel.DEBUG) {
      return Math.random() < this.config.sampleRate;
    }
    
    return true;
  }

  private formatMessage(level: LogLevel, component: LogComponent, message: string, data?: any): string {
    const levelStr = LogLevel[level];
    const timestamp = new Date().toISOString().substr(11, 12); // HH:MM:SS.mmm
    const componentStr = component.toUpperCase();
    
    let formatted = `[${timestamp}] ${levelStr}:${componentStr} ${message}`;
    
    if (data && typeof data === 'object') {
      formatted += ` ${JSON.stringify(data)}`;
    } else if (data !== undefined) {
      formatted += ` ${data}`;
    }
    
    return formatted;
  }

  private addToQueue(level: LogLevel, component: LogComponent, message: string, data?: any): void {
    if (!this.shouldLog(level, component)) return;
    
    this.messageCount[component]++;
    
    if (this.config.enableBatching) {
      this.messageQueue.push({
        level,
        component,
        message,
        data,
        timestamp: Date.now()
      });
      
      if (this.messageQueue.length >= this.config.batchSize) {
        this.flushQueue();
      }
    } else {
      this.logImmediate(level, component, message, data);
    }
  }

  private logImmediate(level: LogLevel, component: LogComponent, message: string, data?: any): void {
    const formatted = this.formatMessage(level, component, message, data);
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(formatted);
        break;
      case LogLevel.WARN:
        console.warn(formatted);
        break;
      case LogLevel.INFO:
      case LogLevel.DEBUG:
      case LogLevel.VERBOSE:
      default:
        console.log(formatted);
        break;
    }
  }

  private flushQueue(): void {
    if (this.messageQueue.length === 0) return;
    
    // Group messages by component and level
    const grouped: { [key: string]: Array<{ message: string; data?: any; count: number }> } = {};
    
    this.messageQueue.forEach(item => {
      const key = `${LogLevel[item.level]}:${item.component}`;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      
      // Check if we can batch similar messages
      const existing = grouped[key].find(g => g.message === item.message);
      if (existing) {
        existing.count++;
      } else {
        grouped[key].push({ message: item.message, data: item.data, count: 1 });
      }
    });
    
    // Output grouped messages
    Object.entries(grouped).forEach(([key, messages]) => {
      const [levelStr, component] = key.split(':');
      const level = LogLevel[levelStr as keyof typeof LogLevel] as LogLevel;
      
      messages.forEach(({ message, data, count }) => {
        const finalMessage = count > 1 ? `${message} (×${count})` : message;
        this.logImmediate(level, component as LogComponent, finalMessage, data);
      });
    });
    
    this.messageQueue = [];
    this.lastFlushTime = Date.now();
  }

  private startBatchTimer(): void {
    if (!this.config.enableBatching) return;
    
    this.batchTimer = setInterval(() => {
      this.flushQueue();
    }, this.config.batchInterval);
  }

  private restartBatchTimer(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    this.startBatchTimer();
  }

  // Public logging methods
  error(component: LogComponent, message: string, data?: any): void {
    this.addToQueue(LogLevel.ERROR, component, message, data);
  }

  warn(component: LogComponent, message: string, data?: any): void {
    this.addToQueue(LogLevel.WARN, component, message, data);
  }

  info(component: LogComponent, message: string, data?: any): void {
    this.addToQueue(LogLevel.INFO, component, message, data);
  }

  debug(component: LogComponent, message: string, data?: any): void {
    this.addToQueue(LogLevel.DEBUG, component, message, data);
  }

  verbose(component: LogComponent, message: string, data?: any): void {
    this.addToQueue(LogLevel.VERBOSE, component, message, data);
  }

  // Utility methods
  getMessageCounts(): { [key in LogComponent]: number } {
    return { ...this.messageCount };
  }

  resetMessageCounts(): void {
    Object.keys(this.messageCount).forEach(key => {
      this.messageCount[key as LogComponent] = 0;
    });
  }

  getQueueSize(): number {
    return this.messageQueue.length;
  }

  forceFlush(): void {
    this.flushQueue();
  }

  destroy(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    this.flushQueue();
  }
}

// Global logging instance
export const logger = new LoggingSystem();

// Convenience functions for common logging patterns
export const logTensorOperation = (message: string, data?: any) => 
  logger.verbose('tensorProcessing', message, data);

export const logDetectionResult = (message: string, data?: any) => 
  logger.debug('detection', message, data);

export const logCoordinateValidation = (message: string, data?: any) => 
  logger.verbose('coordinates', message, data);

export const logPerformanceMetric = (message: string, data?: any) => 
  logger.info('performance', message, data);

export const logMemoryOperation = (message: string, data?: any) => 
  logger.debug('memory', message, data);

export const logOverlayOperation = (message: string, data?: any) => 
  logger.debug('overlay', message, data);

// TASK 5: Enhanced production mode toggle with automatic initialization
export const enableProductionLogging = () => {
  console.log('🔧 LOGGING: Enabling production logging configuration');
  logger.updateConfig(PRODUCTION_LOG_CONFIG);
  console.log('🔧 LOGGING: Production logging enabled - output reduced to <50 lines per session');
};

export const enableDevelopmentLogging = () => {
  console.log('🔧 LOGGING: Enabling development logging configuration');
  logger.updateConfig(DEFAULT_LOG_CONFIG);
  console.log('🔧 LOGGING: Development logging enabled');
};

// TASK 5: Auto-enable production logging for performance
export const initializeOptimalLogging = () => {
  // Check if we're in a production-like environment
  const isProduction = process.env.NODE_ENV === 'production' ||
                      window.location.hostname !== 'localhost';

  if (isProduction) {
    enableProductionLogging();
  } else {
    // Even in development, use production logging for performance
    enableProductionLogging();
    console.log('🔧 LOGGING: Using production logging in development for performance optimization');
  }
};
