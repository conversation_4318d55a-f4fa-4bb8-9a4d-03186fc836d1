/**
 * Memory management system for TensorFlow.js operations
 * TASK 3: Optimize memory usage and implement cleanup strategies
 */

import * as tf from '@tensorflow/tfjs-core';
import { logger, logMemoryOperation } from './logging_system';

export interface MemoryStats {
  numTensors: number;
  numDataBuffers: number;
  numBytes: number;
  unreliable: boolean;
  reasons?: string[];
}

export interface MemoryConfig {
  enableAutoCleanup: boolean;
  cleanupInterval: number; // milliseconds
  maxTensors: number;
  maxMemoryMB: number;
  enableGarbageCollection: boolean;
  gcInterval: number; // milliseconds
  enableCaching: boolean;
  maxCacheSize: number;
  cacheExpiryMs: number;
}

export const DEFAULT_MEMORY_CONFIG: MemoryConfig = {
  enableAutoCleanup: true,
  cleanupInterval: 5000, // 5 seconds
  maxTensors: 1000,
  maxMemoryMB: 512,
  enableGarbageCollection: true,
  gcInterval: 10000, // 10 seconds
  enableCaching: true,
  maxCacheSize: 50,
  cacheExpiryMs: 30000 // 30 seconds
};

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccess: number;
}

class MemoryManager {
  private config: MemoryConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;
  // TASK 6: Add tensor tracking for efficient memory management
  private activeTensors: Set<tf.Tensor> = new Set();
  private tensorCreationTimes: Map<tf.Tensor, number> = new Map();
  private gcTimer: NodeJS.Timeout | null = null;
  private tensorRegistry: Set<tf.Tensor> = new Set();
  private cache: Map<string, CacheEntry<any>> = new Map();
  private memoryHistory: MemoryStats[] = [];
  private maxHistorySize = 100;

  constructor(config: MemoryConfig = DEFAULT_MEMORY_CONFIG) {
    this.config = config;
    this.startCleanupTimer();
    this.startGCTimer();
  }

  updateConfig(newConfig: Partial<MemoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.restartTimers();
  }

  // Tensor management
  registerTensor(tensor: tf.Tensor): tf.Tensor {
    this.tensorRegistry.add(tensor);
    this.checkMemoryLimits();
    return tensor;
  }

  disposeTensor(tensor: tf.Tensor | tf.Tensor[] | null | undefined): void {
    if (!tensor) return;

    try {
      if (Array.isArray(tensor)) {
        tensor.forEach(t => {
          if (t && typeof t.dispose === 'function') {
            this.tensorRegistry.delete(t);
            t.dispose();
          }
        });
      } else if (typeof tensor.dispose === 'function') {
        this.tensorRegistry.delete(tensor);
        tensor.dispose();
      }
    } catch (error) {
      logger.error('memory', 'Error disposing tensor', { error: error.message });
    }
  }

  // Memory monitoring
  getMemoryStats(): MemoryStats {
    try {
      const stats = tf.memory();
      this.memoryHistory.push(stats);
      
      if (this.memoryHistory.length > this.maxHistorySize) {
        this.memoryHistory.shift();
      }
      
      return stats;
    } catch (error) {
      logger.error('memory', 'Failed to get memory stats', { error: error.message });
      return {
        numTensors: 0,
        numDataBuffers: 0,
        numBytes: 0,
        unreliable: true,
        reasons: ['Failed to access memory stats']
      };
    }
  }

  getMemoryUsageMB(): number {
    const stats = this.getMemoryStats();
    return stats.numBytes / (1024 * 1024);
  }

  isMemoryLimitExceeded(): boolean {
    const stats = this.getMemoryStats();
    const memoryMB = stats.numBytes / (1024 * 1024);
    
    return stats.numTensors > this.config.maxTensors || 
           memoryMB > this.config.maxMemoryMB;
  }

  private checkMemoryLimits(): void {
    if (this.isMemoryLimitExceeded()) {
      logMemoryOperation('Memory limit exceeded, triggering cleanup');
      this.performCleanup();
    }
  }

  // Cleanup operations
  performCleanup(): void {
    const beforeStats = this.getMemoryStats();
    
    try {
      // Clean up registered tensors that are no longer referenced
      const tensorsToDispose: tf.Tensor[] = [];
      this.tensorRegistry.forEach(tensor => {
        try {
          // Check if tensor is still valid
          if (tensor.isDisposed) {
            tensorsToDispose.push(tensor);
          }
        } catch (error) {
          tensorsToDispose.push(tensor);
        }
      });
      
      tensorsToDispose.forEach(tensor => {
        this.tensorRegistry.delete(tensor);
      });
      
      // Clean expired cache entries
      this.cleanCache();
      
      // Force garbage collection if enabled
      if (this.config.enableGarbageCollection && typeof global !== 'undefined' && global.gc) {
        global.gc();
      }
      
      const afterStats = this.getMemoryStats();
      const memoryFreed = beforeStats.numBytes - afterStats.numBytes;
      
      if (memoryFreed > 0) {
        logMemoryOperation('Memory cleanup completed', {
          tensorsFreed: beforeStats.numTensors - afterStats.numTensors,
          memoryFreedMB: (memoryFreed / (1024 * 1024)).toFixed(2),
          remainingTensors: afterStats.numTensors,
          remainingMemoryMB: (afterStats.numBytes / (1024 * 1024)).toFixed(2)
        });
      }
      
    } catch (error) {
      logger.error('memory', 'Error during memory cleanup', { error: error.message });
    }
  }

  // Caching system
  setCache<T>(key: string, data: T): void {
    if (!this.config.enableCaching) return;
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.maxCacheSize) {
      const oldestKey = this.getOldestCacheKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      accessCount: 0,
      lastAccess: Date.now()
    });
  }

  getCache<T>(key: string): T | null {
    if (!this.config.enableCaching) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.config.cacheExpiryMs) {
      this.cache.delete(key);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccess = Date.now();
    
    return entry.data;
  }

  clearCache(): void {
    this.cache.clear();
    logMemoryOperation('Cache cleared');
  }

  private cleanCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > this.config.cacheExpiryMs) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      logMemoryOperation('Cleaned expired cache entries', { count: expiredKeys.length });
    }
  }

  private getOldestCacheKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();
    
    this.cache.forEach((entry, key) => {
      if (entry.lastAccess < oldestTime) {
        oldestTime = entry.lastAccess;
        oldestKey = key;
      }
    });
    
    return oldestKey;
  }

  // Timer management
  private startCleanupTimer(): void {
    if (!this.config.enableAutoCleanup) return;
    
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);
  }

  private startGCTimer(): void {
    if (!this.config.enableGarbageCollection) return;
    
    this.gcTimer = setInterval(() => {
      if (typeof global !== 'undefined' && global.gc) {
        global.gc();
        logMemoryOperation('Forced garbage collection');
      }
    }, this.config.gcInterval);
  }

  private restartTimers(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
    }
    
    this.startCleanupTimer();
    this.startGCTimer();
  }

  // Statistics and monitoring
  getCacheStats(): { size: number; hitRate: number; totalAccesses: number } {
    let totalAccesses = 0;
    this.cache.forEach(entry => {
      totalAccesses += entry.accessCount;
    });
    
    return {
      size: this.cache.size,
      hitRate: totalAccesses > 0 ? (this.cache.size / totalAccesses) : 0,
      totalAccesses
    };
  }

  getMemoryTrend(): 'increasing' | 'decreasing' | 'stable' {
    if (this.memoryHistory.length < 2) return 'stable';
    
    const recent = this.memoryHistory.slice(-5);
    const trend = recent[recent.length - 1].numBytes - recent[0].numBytes;
    
    if (Math.abs(trend) < 1024 * 1024) return 'stable'; // Less than 1MB change
    return trend > 0 ? 'increasing' : 'decreasing';
  }

  // TASK 6: Enhanced tensor tracking methods for performance optimization
  getActiveTensorCount(): number {
    return this.activeTensors.size;
  }

  disposeTensorSafe(tensor: tf.Tensor): void {
    if (this.activeTensors.has(tensor)) {
      try {
        tensor.dispose();
        this.activeTensors.delete(tensor);
        this.tensorCreationTimes.delete(tensor);
      } catch (error) {
        logger.warn('memory', 'Error disposing tensor safely', { error: error.message });
      }
    }
  }

  cleanupOldTensors(maxAgeMs: number = 30000): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [tensor, creationTime] of this.tensorCreationTimes.entries()) {
      if (now - creationTime > maxAgeMs) {
        this.disposeTensorSafe(tensor);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0 && this.config.enableAutoCleanup) {
      logMemoryOperation('Cleaned up old tensors', { count: cleanedCount });
    }

    return cleanedCount;
  }

  // Cleanup and disposal
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
    }
    
    this.performCleanup();
    this.clearCache();
    this.tensorRegistry.clear();
  }
}

// Global memory manager instance
export const memoryManager = new MemoryManager();

// Convenience functions
export const registerTensor = (tensor: tf.Tensor): tf.Tensor => 
  memoryManager.registerTensor(tensor);

export const disposeTensor = (tensor: tf.Tensor | tf.Tensor[] | null | undefined): void => 
  memoryManager.disposeTensor(tensor);

export const getMemoryUsage = (): number => 
  memoryManager.getMemoryUsageMB();

export const performMemoryCleanup = (): void => 
  memoryManager.performCleanup();

export const cacheResult = <T>(key: string, data: T): void =>
  memoryManager.setCache(key, data);

// TASK 6: Enhanced tensor management exports
export const disposeTensorSafe = (tensor: tf.Tensor): void =>
  memoryManager.disposeTensorSafe(tensor);

export const getActiveTensorCount = (): number =>
  memoryManager.getActiveTensorCount();

export const cleanupOldTensors = (maxAgeMs?: number): number =>
  memoryManager.cleanupOldTensors(maxAgeMs);

export const getCachedResult = <T>(key: string): T | null => 
  memoryManager.getCache<T>(key);
