/**
 * Canvas debugging utilities for BlazePose skeletal overlay
 * TASK 2: Fix missing skeletal overlay rendering on canvas
 */

export interface CanvasDebugInfo {
  canvasSize: { width: number; height: number };
  videoSize: { width: number; height: number };
  coordinateSystem: 'normalized' | 'canvas' | 'unknown';
  keypointsVisible: number;
  keypointsTotal: number;
  connectionsDrawn: number;
  drawingErrors: string[];
}

export interface KeypointDebugInfo {
  index: number;
  name: string;
  originalCoords: { x: number; y: number };
  canvasCoords: { x: number; y: number };
  score: number;
  isVisible: boolean;
  isInBounds: boolean;
}

export class CanvasDebugger {
  private debugInfo: CanvasDebugInfo;
  private keypointDebugInfo: KeypointDebugInfo[] = [];

  constructor() {
    this.debugInfo = {
      canvasSize: { width: 0, height: 0 },
      videoSize: { width: 0, height: 0 },
      coordinateSystem: 'unknown',
      keypointsVisible: 0,
      keypointsTotal: 0,
      connectionsDrawn: 0,
      drawingErrors: []
    };
  }

  updateCanvasInfo(canvas: HTMLCanvasElement, video: HTMLVideoElement): void {
    this.debugInfo.canvasSize = { width: canvas.width, height: canvas.height };
    this.debugInfo.videoSize = { width: video.videoWidth, height: video.videoHeight };
  }

  detectCoordinateSystem(keypoints: any[]): 'normalized' | 'canvas' | 'unknown' {
    if (!keypoints || keypoints.length === 0) return 'unknown';

    const sampleKeypoint = keypoints.find(kp => kp.x !== undefined && kp.y !== undefined);
    if (!sampleKeypoint) return 'unknown';

    // CRITICAL FIX: BlazePose ALWAYS outputs normalized coordinates (0-1)
    // The previous logic was backwards - it assumed pixel coordinates by default
    const detectionData = {
      sampleKeypoint: { x: sampleKeypoint.x, y: sampleKeypoint.y },
      canvasSize: this.debugInfo.canvasSize,
      isNormalized: (sampleKeypoint.x >= 0 && sampleKeypoint.x <= 1 && sampleKeypoint.y >= 0 && sampleKeypoint.y <= 1),
      isCanvas: (sampleKeypoint.x >= 0 && sampleKeypoint.x <= this.debugInfo.canvasSize.width && sampleKeypoint.y >= 0 && sampleKeypoint.y <= this.debugInfo.canvasSize.height)
    };
    console.log('🔍 COORDINATE SYSTEM DETECTION: ' + JSON.stringify(detectionData));

    // CRITICAL FIX: BlazePose detector outputs NORMALIZED coordinates (0-1)
    // Check for normalized coordinates FIRST (most likely for BlazePose)
    if (sampleKeypoint.x >= 0 && sampleKeypoint.x <= 1 &&
        sampleKeypoint.y >= 0 && sampleKeypoint.y <= 1) {
      this.debugInfo.coordinateSystem = 'normalized';
      console.log('🔍 COORDINATE SYSTEM: Detected NORMALIZED coordinates (BlazePose standard)');
      return 'normalized';
    }

    // Only check canvas coordinates if they're clearly outside normalized range
    if (sampleKeypoint.x > 1 || sampleKeypoint.y > 1) {
      this.debugInfo.coordinateSystem = 'canvas';
      console.log('🔍 COORDINATE SYSTEM: Detected CANVAS coordinates');
      return 'canvas';
    }

    this.debugInfo.coordinateSystem = 'unknown';
    console.log('🔍 COORDINATE SYSTEM: UNKNOWN coordinates detected');
    return 'unknown';
  }

  validateKeypoint(
    keypoint: any,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    scoreThreshold: number = 0.3
  ): KeypointDebugInfo {
    const originalCoords = { x: keypoint.x || 0, y: keypoint.y || 0 };
    const score = keypoint.score || 0;

    // CRITICAL FIX: BlazePose ALWAYS outputs normalized coordinates (0-1)
    // Always convert normalized coordinates to canvas pixel coordinates
    let canvasCoords = { ...originalCoords };

    // CRITICAL FIX: Always convert normalized coordinates to canvas coordinates
    if (this.debugInfo.coordinateSystem === 'normalized' ||
        (originalCoords.x >= 0 && originalCoords.x <= 1 && originalCoords.y >= 0 && originalCoords.y <= 1)) {
      canvasCoords.x = originalCoords.x * canvasWidth;
      canvasCoords.y = originalCoords.y * canvasHeight;

      const conversionData = {
        original: originalCoords,
        canvas: canvasCoords,
        canvasSize: `${canvasWidth}x${canvasHeight}`
      };
      console.log(`🔧 COORDINATE CONVERSION ${index}: ` + JSON.stringify(conversionData));
    }

    const isVisible = score > scoreThreshold;
    // TASK 2: More lenient bounds checking - allow slight overflow
    const isInBounds = canvasCoords.x >= -10 && canvasCoords.x <= canvasWidth + 10 &&
                      canvasCoords.y >= -10 && canvasCoords.y <= canvasHeight + 10;

    // Reduced keypoint debug logging for production

    const debugInfo: KeypointDebugInfo = {
      index,
      name: keypoint.name || `keypoint_${index}`,
      originalCoords,
      canvasCoords,
      score,
      isVisible,
      isInBounds
    };

    this.keypointDebugInfo.push(debugInfo);
    return debugInfo;
  }

  logDrawingAttempt(
    ctx: CanvasRenderingContext2D,
    keypoint: KeypointDebugInfo,
    drawType: 'circle' | 'line' | 'text'
  ): boolean {
    try {
      // Validate canvas context
      if (!ctx) {
        this.debugInfo.drawingErrors.push(`No canvas context for ${drawType} at keypoint ${keypoint.index}`);
        return false;
      }

      // Validate coordinates
      if (!isFinite(keypoint.canvasCoords.x) || !isFinite(keypoint.canvasCoords.y)) {
        this.debugInfo.drawingErrors.push(`Invalid coordinates for ${drawType} at keypoint ${keypoint.index}: (${keypoint.canvasCoords.x}, ${keypoint.canvasCoords.y})`);
        return false;
      }

      // Check if coordinates are in visible area
      if (!keypoint.isInBounds) {
        this.debugInfo.drawingErrors.push(`Out of bounds ${drawType} at keypoint ${keypoint.index}: (${keypoint.canvasCoords.x}, ${keypoint.canvasCoords.y})`);
        return false;
      }

      return true;
    } catch (error) {
      this.debugInfo.drawingErrors.push(`Error validating ${drawType} for keypoint ${keypoint.index}: ${error.message}`);
      return false;
    }
  }

  drawDebugKeypoint(
    ctx: CanvasRenderingContext2D,
    keypoint: KeypointDebugInfo,
    color: string = '#FF0000'
  ): boolean {
    if (!this.logDrawingAttempt(ctx, keypoint, 'circle')) return false;

    try {
      ctx.save();
      
      // Draw keypoint circle
      ctx.fillStyle = color;
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      
      ctx.beginPath();
      ctx.arc(keypoint.canvasCoords.x, keypoint.canvasCoords.y, 8, 0, 2 * Math.PI);
      ctx.fill();
      ctx.stroke();
      
      // Draw score text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 10px Arial';
      ctx.fillText(
        keypoint.score.toFixed(2), 
        keypoint.canvasCoords.x + 10, 
        keypoint.canvasCoords.y - 10
      );
      
      ctx.restore();
      return true;
    } catch (error) {
      this.debugInfo.drawingErrors.push(`Failed to draw debug keypoint ${keypoint.index}: ${error.message}`);
      return false;
    }
  }

  drawDebugGrid(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
    try {
      ctx.save();
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 1;
      
      // Draw grid lines every 50 pixels
      for (let x = 0; x <= canvas.width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }
      
      for (let y = 0; y <= canvas.height; y += 50) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
      
      // Draw center crosshair
      ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
      ctx.lineWidth = 2;
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      
      ctx.beginPath();
      ctx.moveTo(centerX - 20, centerY);
      ctx.lineTo(centerX + 20, centerY);
      ctx.moveTo(centerX, centerY - 20);
      ctx.lineTo(centerX, centerY + 20);
      ctx.stroke();
      
      ctx.restore();
    } catch (error) {
      this.debugInfo.drawingErrors.push(`Failed to draw debug grid: ${error.message}`);
    }
  }

  drawDebugInfo(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
    try {
      ctx.save();
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.fillRect(10, canvas.height - 120, 300, 110);
      
      ctx.fillStyle = '#00FF00';
      ctx.font = 'bold 12px Arial';
      
      const lines = [
        `Canvas: ${this.debugInfo.canvasSize.width}x${this.debugInfo.canvasSize.height}`,
        `Video: ${this.debugInfo.videoSize.width}x${this.debugInfo.videoSize.height}`,
        `Coords: ${this.debugInfo.coordinateSystem}`,
        `Keypoints: ${this.debugInfo.keypointsVisible}/${this.debugInfo.keypointsTotal}`,
        `Connections: ${this.debugInfo.connectionsDrawn}`,
        `Errors: ${this.debugInfo.drawingErrors.length}`
      ];
      
      lines.forEach((line, index) => {
        ctx.fillText(line, 15, canvas.height - 100 + (index * 15));
      });
      
      ctx.restore();
    } catch (error) {
      console.warn('Failed to draw debug info:', error);
    }
  }

  getDebugSummary(): CanvasDebugInfo {
    return { ...this.debugInfo };
  }

  getKeypointDebugInfo(): KeypointDebugInfo[] {
    return [...this.keypointDebugInfo];
  }

  reset(): void {
    this.debugInfo.keypointsVisible = 0;
    this.debugInfo.keypointsTotal = 0;
    this.debugInfo.connectionsDrawn = 0;
    this.debugInfo.drawingErrors = [];
    this.keypointDebugInfo = [];
  }

  logSummary(): void {
    console.log('🎨 CANVAS DEBUG SUMMARY: ' + JSON.stringify(this.debugInfo));

    if (this.debugInfo.drawingErrors.length > 0) {
      console.warn('🎨 CANVAS DRAWING ERRORS: ' + JSON.stringify(this.debugInfo.drawingErrors));
    }

    const visibleKeypoints = this.keypointDebugInfo.filter(kp => kp.isVisible && kp.isInBounds);
    if (visibleKeypoints.length > 0) {
      console.log('🎨 VISIBLE KEYPOINTS: ' + JSON.stringify(visibleKeypoints.slice(0, 5))); // Log first 5
    }
  }
}

// Global canvas debugger instance
export const globalCanvasDebugger = new CanvasDebugger();
