/**
 * Enhanced pose drawing utilities for BlazePose skeletal overlay
 * TASK 2: Fix missing skeletal overlay rendering with proper coordinate handling
 */

import { BLAZEPOSE_CONNECTIONS } from '@/shared/calculators/blazepose_constants';
import { globalCanvasDebugger, KeypointDebugInfo } from './canvas_debug';
import { logger } from './logging_system';

export interface DrawingConfig {
  keypointRadius: number;
  keypointColor: string;
  keypointStrokeColor: string;
  keypointStrokeWidth: number;
  connectionColor: string;
  connectionWidth: number;
  scoreThreshold: number;
  showLabels: boolean;
  showScores: boolean;
  enableDebugMode: boolean;
}

export const DEFAULT_DRAWING_CONFIG: DrawingConfig = {
  keypointRadius: 6,
  keypointColor: '#00FF00',
  keypointStrokeColor: '#FFFFFF',
  keypointStrokeWidth: 2,
  connectionColor: '#00FFFF',
  connectionWidth: 3,
  scoreThreshold: 0.2, // Lowered from 0.3 for better visibility
  showLabels: true,
  showScores: false,
  enableDebugMode: true
};

export interface PoseDrawingResult {
  keypointsDrawn: number;
  connectionsDrawn: number;
  coordinateSystem: 'normalized' | 'canvas' | 'unknown';
  errors: string[];
  boundingBox?: { x: number; y: number; width: number; height: number };
}

export class PoseDrawer {
  private config: DrawingConfig;

  constructor(config: Partial<DrawingConfig> = {}) {
    this.config = { ...DEFAULT_DRAWING_CONFIG, ...config };
  }

  /**
   * Main function to draw pose with automatic coordinate detection and conversion
   */
  drawPose(
    ctx: CanvasRenderingContext2D,
    pose: any,
    canvas: HTMLCanvasElement,
    video: HTMLVideoElement
  ): PoseDrawingResult {
    const result: PoseDrawingResult = {
      keypointsDrawn: 0,
      connectionsDrawn: 0,
      coordinateSystem: 'unknown',
      errors: []
    };

    try {
      // Reset debugger for this frame
      globalCanvasDebugger.reset();
      globalCanvasDebugger.updateCanvasInfo(canvas, video);

      const keypoints = pose.keypoints;
      if (!keypoints || keypoints.length === 0) {
        result.errors.push('No keypoints found in pose data');
        return result;
      }

      // Detect coordinate system
      result.coordinateSystem = globalCanvasDebugger.detectCoordinateSystem(keypoints);

      // TASK 1: Replace console.log with logging system
      if (this.config.enableDebugMode) {
        logger.debug('overlay', 'Starting pose rendering', {
          keypointsCount: keypoints.length,
          coordinateSystem: result.coordinateSystem,
          canvasSize: `${canvas.width}x${canvas.height}`,
          videoSize: `${video.videoWidth}x${video.videoHeight}`,
          firstKeypoint: keypoints[0] ? {
            x: keypoints[0].x,
            y: keypoints[0].y,
            score: keypoints[0].score
          } : null
        });
      }

      // Clear canvas with proper layering
      this.clearCanvasForPose(ctx, canvas);

      // Draw debug grid if enabled
      if (this.config.enableDebugMode) {
        globalCanvasDebugger.drawDebugGrid(ctx, canvas);
      }

      // Process and validate keypoints
      const processedKeypoints = this.processKeypoints(keypoints, canvas, result);
      
      // Draw skeleton connections first (behind keypoints)
      result.connectionsDrawn = this.drawConnections(ctx, processedKeypoints, canvas, result);
      
      // Draw keypoints on top
      result.keypointsDrawn = this.drawKeypoints(ctx, processedKeypoints, canvas, result);

      // Calculate bounding box
      result.boundingBox = this.calculateBoundingBox(processedKeypoints);

      // Draw debug info if enabled
      if (this.config.enableDebugMode) {
        globalCanvasDebugger.drawDebugInfo(ctx, canvas);
        globalCanvasDebugger.logSummary();
      }

      // TASK 1: Replace console.log with logging system
      if (this.config.enableDebugMode) {
        logger.debug('overlay', 'Pose drawing result', {
          keypointsDrawn: result.keypointsDrawn,
          connectionsDrawn: result.connectionsDrawn,
          errors: result.errors.length,
          boundingBox: result.boundingBox,
          success: result.keypointsDrawn > 0 || result.connectionsDrawn > 0
        });
      }

    } catch (error) {
      result.errors.push(`Pose drawing failed: ${error.message}`);
      console.error('🎨 POSE DRAWING ERROR:', error);
    }

    return result;
  }

  private clearCanvasForPose(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
    // Save current state
    ctx.save();
    
    // Clear only the pose area, preserving debug indicators if needed
    ctx.globalCompositeOperation = 'source-over';
    
    // Use a semi-transparent clear to allow debug indicators to show through
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Restore state
    ctx.restore();
  }

  private processKeypoints(
    keypoints: any[], 
    canvas: HTMLCanvasElement, 
    result: PoseDrawingResult
  ): KeypointDebugInfo[] {
    const processedKeypoints: KeypointDebugInfo[] = [];

    keypoints.forEach((keypoint, index) => {
      try {
        const debugInfo = globalCanvasDebugger.validateKeypoint(
          keypoint, 
          index, 
          canvas.width, 
          canvas.height, 
          this.config.scoreThreshold
        );

        processedKeypoints.push(debugInfo);

        // TASK 1: Replace console.log with logging system - reduced frequency
        if (index < 3 && this.config.enableDebugMode) {
          logger.verbose('overlay', `Keypoint ${index} processed`, {
            name: debugInfo.name,
            original: debugInfo.originalCoords,
            canvas: debugInfo.canvasCoords,
            score: debugInfo.score,
            visible: debugInfo.isVisible,
            inBounds: debugInfo.isInBounds
          });
        }

      } catch (error) {
        result.errors.push(`Failed to process keypoint ${index}: ${error.message}`);
      }
    });

    return processedKeypoints;
  }

  private drawKeypoints(
    ctx: CanvasRenderingContext2D, 
    keypoints: KeypointDebugInfo[], 
    canvas: HTMLCanvasElement,
    result: PoseDrawingResult
  ): number {
    let drawn = 0;

    keypoints.forEach((keypoint) => {
      // TASK 2: More permissive keypoint filtering - prioritize visibility over bounds
      if (!keypoint.isVisible) return;

      // CRITICAL FIX: Draw keypoint even if slightly out of bounds (clamp coordinates)
      // NOTE: canvas parameter needs to be passed to drawKeypoints method
      if (!keypoint.isInBounds) {
        // Skip drawing if coordinates are invalid to prevent canvas errors
        console.warn(`Skipping out-of-bounds keypoint ${keypoint.index} at (${keypoint.canvasCoords.x}, ${keypoint.canvasCoords.y})`);
        return;
      }

      try {
        ctx.save();

        // Draw keypoint circle
        ctx.fillStyle = this.config.keypointColor;
        ctx.strokeStyle = this.config.keypointStrokeColor;
        ctx.lineWidth = this.config.keypointStrokeWidth;

        ctx.beginPath();
        ctx.arc(
          keypoint.canvasCoords.x, 
          keypoint.canvasCoords.y, 
          this.config.keypointRadius, 
          0, 
          2 * Math.PI
        );
        ctx.fill();
        ctx.stroke();

        // Draw label if enabled
        if (this.config.showLabels) {
          ctx.fillStyle = '#FFFFFF';
          ctx.font = 'bold 10px Arial';
          ctx.strokeStyle = '#000000';
          ctx.lineWidth = 1;
          
          const label = keypoint.name.substring(0, 8);
          const textX = keypoint.canvasCoords.x + this.config.keypointRadius + 2;
          const textY = keypoint.canvasCoords.y - this.config.keypointRadius;
          
          ctx.strokeText(label, textX, textY);
          ctx.fillText(label, textX, textY);
        }

        // Draw score if enabled
        if (this.config.showScores) {
          ctx.fillStyle = '#FFFF00';
          ctx.font = 'bold 8px Arial';
          ctx.fillText(
            keypoint.score.toFixed(2),
            keypoint.canvasCoords.x - 10,
            keypoint.canvasCoords.y + this.config.keypointRadius + 12
          );
        }

        ctx.restore();
        drawn++;

      } catch (error) {
        result.errors.push(`Failed to draw keypoint ${keypoint.index}: ${error.message}`);
      }
    });

    return drawn;
  }

  private drawConnections(
    ctx: CanvasRenderingContext2D, 
    keypoints: KeypointDebugInfo[], 
    canvas: HTMLCanvasElement,
    result: PoseDrawingResult
  ): number {
    let drawn = 0;

    try {
      ctx.save();
      ctx.strokeStyle = this.config.connectionColor;
      ctx.lineWidth = this.config.connectionWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      BLAZEPOSE_CONNECTIONS.forEach(([startIdx, endIdx]) => {
        try {
          const startKeypoint = keypoints[startIdx];
          const endKeypoint = keypoints[endIdx];

          if (!startKeypoint || !endKeypoint) return;
          if (!startKeypoint.isVisible || !endKeypoint.isVisible) return;
          if (!startKeypoint.isInBounds || !endKeypoint.isInBounds) return;

          // Draw connection line
          ctx.beginPath();
          ctx.moveTo(startKeypoint.canvasCoords.x, startKeypoint.canvasCoords.y);
          ctx.lineTo(endKeypoint.canvasCoords.x, endKeypoint.canvasCoords.y);
          ctx.stroke();

          drawn++;

        } catch (error) {
          result.errors.push(`Failed to draw connection ${startIdx}-${endIdx}: ${error.message}`);
        }
      });

      ctx.restore();

    } catch (error) {
      result.errors.push(`Failed to draw connections: ${error.message}`);
    }

    return drawn;
  }

  private calculateBoundingBox(keypoints: KeypointDebugInfo[]): { x: number; y: number; width: number; height: number } | undefined {
    const visibleKeypoints = keypoints.filter(kp => kp.isVisible && kp.isInBounds);
    
    if (visibleKeypoints.length === 0) return undefined;

    const xs = visibleKeypoints.map(kp => kp.canvasCoords.x);
    const ys = visibleKeypoints.map(kp => kp.canvasCoords.y);

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  updateConfig(newConfig: Partial<DrawingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): DrawingConfig {
    return { ...this.config };
  }
}

// Global pose drawer instance
export const globalPoseDrawer = new PoseDrawer();
