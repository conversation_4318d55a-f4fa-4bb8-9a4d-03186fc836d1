/**
 * Detection optimization system for BlazePose pipeline
 * TASK 3: Implement confidence-based filtering and spatial optimization
 */

import { logger, logDetectionResult } from './logging_system';
import { memoryManager, cacheResult, getCachedResult } from './memory_manager';

export interface Detection {
  boundingBox: { xMin: number; yMin: number; xMax: number; yMax: number };
  score: number;
  locationData?: any;
  relativeBoundingBox?: any;
}

export interface OptimizationConfig {
  enableConfidenceFiltering: boolean;
  minConfidenceThreshold: number;
  highConfidenceThreshold: number;
  enableSpatialFiltering: boolean;
  spatialOverlapThreshold: number;
  enableResultCaching: boolean;
  cacheKeyFrames: boolean;
  frameSkipInterval: number;
  enableEarlyTermination: boolean;
  maxProcessingTimeMs: number;
  enableBatchProcessing: boolean;
  batchSize: number;
}

export const DEFAULT_OPTIMIZATION_CONFIG: OptimizationConfig = {
  enableConfidenceFiltering: true,
  minConfidenceThreshold: 0.3,
  highConfidenceThreshold: 0.7,
  enableSpatialFiltering: true,
  spatialOverlapThreshold: 0.5,
  enableResultCaching: true,
  cacheKeyFrames: true,
  frameSkipInterval: 2, // Process every 2nd frame
  enableEarlyTermination: true,
  maxProcessingTimeMs: 16, // 60fps target
  enableBatchProcessing: true,
  batchSize: 10
};

export const AGGRESSIVE_OPTIMIZATION_CONFIG: OptimizationConfig = {
  enableConfidenceFiltering: true,
  minConfidenceThreshold: 0.5,
  highConfidenceThreshold: 0.8,
  enableSpatialFiltering: true,
  spatialOverlapThreshold: 0.3,
  enableResultCaching: true,
  cacheKeyFrames: true,
  frameSkipInterval: 3, // Process every 3rd frame
  enableEarlyTermination: true,
  maxProcessingTimeMs: 12,
  enableBatchProcessing: true,
  batchSize: 5
};

export interface OptimizationResult<T> {
  data: T[];
  originalCount: number;
  filteredCount: number;
  processingTime: number;
  wasCached: boolean;
  wasSkipped: boolean;
  optimizations: string[];
}

class DetectionOptimizer {
  private config: OptimizationConfig;
  private frameCounter: number = 0;
  private lastProcessedFrame: number = -1;
  private processingHistory: number[] = [];
  private maxHistorySize = 50;

  constructor(config: OptimizationConfig = DEFAULT_OPTIMIZATION_CONFIG) {
    this.config = config;
  }

  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  optimizeDetections(
    detections: Detection[],
    frameNumber?: number
  ): OptimizationResult<Detection> {
    const startTime = performance.now();
    const optimizations: string[] = [];
    
    this.frameCounter++;
    const currentFrame = frameNumber ?? this.frameCounter;
    
    // Check if we should skip this frame
    if (this.shouldSkipFrame(currentFrame)) {
      const cachedResult = this.getCachedDetections(currentFrame);
      if (cachedResult) {
        return {
          data: cachedResult,
          originalCount: detections.length,
          filteredCount: cachedResult.length,
          processingTime: performance.now() - startTime,
          wasCached: true,
          wasSkipped: true,
          optimizations: ['frame_skipped', 'cache_hit']
        };
      }
    }

    let filteredDetections = [...detections];
    const originalCount = filteredDetections.length;

    // Early termination check
    if (this.config.enableEarlyTermination) {
      const elapsedTime = performance.now() - startTime;
      if (elapsedTime > this.config.maxProcessingTimeMs) {
        optimizations.push('early_termination');
        logDetectionResult('Early termination due to time limit', { 
          elapsedTime, 
          limit: this.config.maxProcessingTimeMs 
        });
        
        return {
          data: [],
          originalCount,
          filteredCount: 0,
          processingTime: elapsedTime,
          wasCached: false,
          wasSkipped: false,
          optimizations
        };
      }
    }

    // Confidence-based filtering
    if (this.config.enableConfidenceFiltering) {
      const beforeCount = filteredDetections.length;
      filteredDetections = this.filterByConfidence(filteredDetections);
      
      if (filteredDetections.length < beforeCount) {
        optimizations.push('confidence_filtering');
      }
    }

    // Spatial filtering for overlapping detections
    if (this.config.enableSpatialFiltering && filteredDetections.length > 1) {
      const beforeCount = filteredDetections.length;
      filteredDetections = this.filterSpatialOverlaps(filteredDetections);
      
      if (filteredDetections.length < beforeCount) {
        optimizations.push('spatial_filtering');
      }
    }

    // Batch processing for large detection sets
    if (this.config.enableBatchProcessing && filteredDetections.length > this.config.batchSize) {
      filteredDetections = this.processBatches(filteredDetections);
      optimizations.push('batch_processing');
    }

    const processingTime = performance.now() - startTime;
    this.recordProcessingTime(processingTime);

    // Cache results if enabled
    if (this.config.enableResultCaching) {
      this.cacheDetections(currentFrame, filteredDetections);
      optimizations.push('result_cached');
    }

    this.lastProcessedFrame = currentFrame;

    const result: OptimizationResult<Detection> = {
      data: filteredDetections,
      originalCount,
      filteredCount: filteredDetections.length,
      processingTime,
      wasCached: false,
      wasSkipped: false,
      optimizations
    };

    // Log optimization results (with sampling to reduce log volume)
    if (this.frameCounter % 30 === 0) { // Log every 30 frames
      logDetectionResult('Detection optimization summary', {
        frame: currentFrame,
        reductionRate: `${(((originalCount - filteredDetections.length) / originalCount) * 100).toFixed(1)}%`,
        processingTime: `${processingTime.toFixed(2)}ms`,
        optimizations: optimizations.join(', '),
        avgProcessingTime: `${this.getAverageProcessingTime().toFixed(2)}ms`
      });
    }

    return result;
  }

  private shouldSkipFrame(frameNumber: number): boolean {
    if (!this.config.cacheKeyFrames) return false;
    
    return frameNumber % this.config.frameSkipInterval !== 0;
  }

  private filterByConfidence(detections: Detection[]): Detection[] {
    return detections.filter(detection => {
      const score = detection.score || 0;
      return score >= this.config.minConfidenceThreshold;
    });
  }

  private filterSpatialOverlaps(detections: Detection[]): Detection[] {
    // Sort by confidence score (highest first)
    const sorted = detections.sort((a, b) => (b.score || 0) - (a.score || 0));
    const filtered: Detection[] = [];

    for (const detection of sorted) {
      let shouldKeep = true;

      for (const existing of filtered) {
        const overlap = this.calculateOverlap(detection.boundingBox, existing.boundingBox);
        if (overlap > this.config.spatialOverlapThreshold) {
          shouldKeep = false;
          break;
        }
      }

      if (shouldKeep) {
        filtered.push(detection);
      }
    }

    return filtered;
  }

  private calculateOverlap(box1: any, box2: any): number {
    const x1 = Math.max(box1.xMin, box2.xMin);
    const y1 = Math.max(box1.yMin, box2.yMin);
    const x2 = Math.min(box1.xMax, box2.xMax);
    const y2 = Math.min(box1.yMax, box2.yMax);

    if (x2 <= x1 || y2 <= y1) return 0;

    const intersectionArea = (x2 - x1) * (y2 - y1);
    const box1Area = (box1.xMax - box1.xMin) * (box1.yMax - box1.yMin);
    const box2Area = (box2.xMax - box2.xMin) * (box2.yMax - box2.yMin);
    const unionArea = box1Area + box2Area - intersectionArea;

    return unionArea > 0 ? intersectionArea / unionArea : 0;
  }

  private processBatches(detections: Detection[]): Detection[] {
    // Process detections in batches to prevent overwhelming the system
    const batches: Detection[][] = [];
    
    for (let i = 0; i < detections.length; i += this.config.batchSize) {
      batches.push(detections.slice(i, i + this.config.batchSize));
    }

    // For now, just return the first batch to limit processing
    // In a more sophisticated implementation, we could process batches asynchronously
    return batches[0] || [];
  }

  private cacheDetections(frameNumber: number, detections: Detection[]): void {
    const cacheKey = `detections_frame_${frameNumber}`;
    cacheResult(cacheKey, detections);
  }

  private getCachedDetections(frameNumber: number): Detection[] | null {
    // Try to get cached results from nearby frames
    for (let offset = 0; offset <= this.config.frameSkipInterval; offset++) {
      const cacheKey = `detections_frame_${frameNumber - offset}`;
      const cached = getCachedResult<Detection[]>(cacheKey);
      if (cached) {
        return cached;
      }
    }
    return null;
  }

  private recordProcessingTime(time: number): void {
    this.processingHistory.push(time);
    if (this.processingHistory.length > this.maxHistorySize) {
      this.processingHistory.shift();
    }
  }

  private getAverageProcessingTime(): number {
    if (this.processingHistory.length === 0) return 0;
    
    const sum = this.processingHistory.reduce((acc, time) => acc + time, 0);
    return sum / this.processingHistory.length;
  }

  // Statistics and monitoring
  getOptimizationStats(): {
    framesProcessed: number;
    averageProcessingTime: number;
    maxProcessingTime: number;
    minProcessingTime: number;
    cacheHitRate: number;
  } {
    const avgTime = this.getAverageProcessingTime();
    const maxTime = this.processingHistory.length > 0 ? Math.max(...this.processingHistory) : 0;
    const minTime = this.processingHistory.length > 0 ? Math.min(...this.processingHistory) : 0;

    return {
      framesProcessed: this.frameCounter,
      averageProcessingTime: avgTime,
      maxProcessingTime: maxTime,
      minProcessingTime: minTime,
      cacheHitRate: 0 // Would need to track cache hits/misses for accurate calculation
    };
  }

  reset(): void {
    this.frameCounter = 0;
    this.lastProcessedFrame = -1;
    this.processingHistory = [];
  }
}

// Global detection optimizer instance
export const detectionOptimizer = new DetectionOptimizer();

// Convenience functions
export const optimizeDetections = (detections: Detection[], frameNumber?: number): OptimizationResult<Detection> =>
  detectionOptimizer.optimizeDetections(detections, frameNumber);

export const enableAggressiveOptimization = (): void => {
  detectionOptimizer.updateConfig(AGGRESSIVE_OPTIMIZATION_CONFIG);
};

export const enableStandardOptimization = (): void => {
  detectionOptimizer.updateConfig(DEFAULT_OPTIMIZATION_CONFIG);
};
