/**
 * Performance monitoring utility for BlazePose detection pipeline
 * Task 1: Fix requestAnimationFrame violation by monitoring and limiting processing time
 */

import { PERFORMANCE_LIMITS } from './constants';

export interface PerformanceMetrics {
  frameCount: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
  maxProcessingTime: number;
  detectionsProcessed: number;
  detectionsSkipped: number;
  invalidDetections: number;
  memoryUsage?: number;
  timestamp: number;
}

export interface ProcessingResult<T> {
  data: T;
  processingTime: number;
  detectionsProcessed: number;
  detectionsSkipped: number;
  wasLimited: boolean;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private frameStartTime: number = 0;
  private consecutiveSlowFrames: number = 0;
  private lastLogTime: number = 0;

  constructor() {
    this.metrics = {
      frameCount: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0,
      maxProcessingTime: 0,
      detectionsProcessed: 0,
      detectionsSkipped: 0,
      invalidDetections: 0,
      timestamp: Date.now()
    };
  }

  startFrame(): void {
    this.frameStartTime = performance.now();
    this.metrics.frameCount++;
  }

  endFrame(): number {
    const processingTime = performance.now() - this.frameStartTime;
    this.updateMetrics(processingTime);
    return processingTime;
  }

  private updateMetrics(processingTime: number): void {
    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.frameCount;
    this.metrics.maxProcessingTime = Math.max(this.metrics.maxProcessingTime, processingTime);

    // Track consecutive slow frames
    if (processingTime > PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS) {
      this.consecutiveSlowFrames++;
    } else {
      this.consecutiveSlowFrames = 0;
    }

    // Log performance metrics periodically
    if (PERFORMANCE_LIMITS.ENABLE_PERFORMANCE_MONITORING && 
        this.metrics.frameCount % PERFORMANCE_LIMITS.PERFORMANCE_LOG_INTERVAL === 0) {
      this.logMetrics();
    }
  }

  shouldLimitProcessing(): boolean {
    const currentTime = performance.now();
    const elapsedTime = currentTime - this.frameStartTime;
    
    return elapsedTime > PERFORMANCE_LIMITS.MAX_PROCESSING_TIME_MS || 
           this.consecutiveSlowFrames > 3;
  }

  recordDetectionProcessed(): void {
    this.metrics.detectionsProcessed++;
  }

  recordDetectionSkipped(): void {
    this.metrics.detectionsSkipped++;
  }

  recordInvalidDetection(): void {
    this.metrics.invalidDetections++;
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  private logMetrics(): void {
    const now = Date.now();
    if (now - this.lastLogTime < 1000) return; // Limit logging to once per second
    
    this.lastLogTime = now;
    
    console.log('🔧 PERFORMANCE METRICS:', {
      frames: this.metrics.frameCount,
      avgTime: `${this.metrics.averageProcessingTime.toFixed(2)}ms`,
      maxTime: `${this.metrics.maxProcessingTime.toFixed(2)}ms`,
      processed: this.metrics.detectionsProcessed,
      skipped: this.metrics.detectionsSkipped,
      invalid: this.metrics.invalidDetections,
      consecutiveSlow: this.consecutiveSlowFrames
    });
  }

  reset(): void {
    this.metrics = {
      frameCount: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0,
      maxProcessingTime: 0,
      detectionsProcessed: 0,
      detectionsSkipped: 0,
      invalidDetections: 0,
      timestamp: Date.now()
    };
    this.consecutiveSlowFrames = 0;
  }
}

// Coordinate validation utilities
export function isValidCoordinate(value: number): boolean {
  return !isNaN(value) && 
         isFinite(value) && 
         value >= PERFORMANCE_LIMITS.MIN_COORDINATE_VALUE && 
         value <= PERFORMANCE_LIMITS.MAX_COORDINATE_VALUE;
}

export function isValidDetection(detection: any): boolean {
  if (!detection) return false;
  
  // Check if detection has required properties
  if (typeof detection.yCenter !== 'number' || 
      typeof detection.xCenter !== 'number' ||
      typeof detection.h !== 'number' ||
      typeof detection.w !== 'number') {
    return false;
  }
  
  // Validate coordinate values
  return isValidCoordinate(detection.yCenter) &&
         isValidCoordinate(detection.xCenter) &&
         isValidCoordinate(detection.h) &&
         isValidCoordinate(detection.w);
}

// Async processing utilities
export async function processDetectionsWithLimits<T>(
  detections: any[],
  processor: (detection: any, index: number) => T,
  monitor: PerformanceMonitor
): Promise<ProcessingResult<T[]>> {
  const startTime = performance.now();
  const results: T[] = [];
  let detectionsProcessed = 0;
  let detectionsSkipped = 0;
  let wasLimited = false;

  // Limit total detections to prevent freeze
  const maxDetections = Math.min(detections.length, PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME);
  
  if (detections.length > PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME) {
    wasLimited = true;
    detectionsSkipped = detections.length - PERFORMANCE_LIMITS.MAX_DETECTIONS_PER_FRAME;
  }

  // Process detections in chunks to allow for async breaks
  for (let i = 0; i < maxDetections; i += PERFORMANCE_LIMITS.PROCESSING_CHUNK_SIZE) {
    // Check if we should stop processing due to time limits
    if (monitor.shouldLimitProcessing()) {
      wasLimited = true;
      detectionsSkipped += maxDetections - i;
      break;
    }

    // Process chunk
    const chunkEnd = Math.min(i + PERFORMANCE_LIMITS.PROCESSING_CHUNK_SIZE, maxDetections);
    for (let j = i; j < chunkEnd; j++) {
      const detection = detections[j];
      
      // Validate detection before processing
      if (!isValidDetection(detection)) {
        monitor.recordInvalidDetection();
        continue;
      }

      try {
        const result = processor(detection, j);
        results.push(result);
        monitor.recordDetectionProcessed();
        detectionsProcessed++;
      } catch (error) {
        console.warn('🔧 PERFORMANCE: Detection processing error:', error);
        monitor.recordInvalidDetection();
      }
    }

    // Yield control to prevent blocking
    if (PERFORMANCE_LIMITS.USE_ASYNC_PROCESSING && i + PERFORMANCE_LIMITS.PROCESSING_CHUNK_SIZE < maxDetections) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  const processingTime = performance.now() - startTime;

  return {
    data: results,
    processingTime,
    detectionsProcessed,
    detectionsSkipped,
    wasLimited
  };
}

// Global performance monitor instance
export const globalPerformanceMonitor = new PerformanceMonitor();
