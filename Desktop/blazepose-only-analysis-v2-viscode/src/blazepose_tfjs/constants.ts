/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// Don't change the order. The order needs to be consistent with the model
// keypoint result list.
export const COCO_KEYPOINTS = [
  'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear', 'left_shoulder',
  'right_shoulder', 'left_elbow', 'right_elbow', 'left_wrist', 'right_wrist',
  'left_hip', 'right_hip', 'left_knee', 'right_knee', 'left_ankle',
  'right_ankle'
];
// Don't change the order. The order needs to be consistent with the model
// keypoint result list.
// TASK 2: Add Full model keypoint list (39 landmarks)
export const BLAZEPOSE_FULL_KEYPOINTS = [
  'nose',
  'left_eye_inner', 'left_eye', 'left_eye_outer',
  'right_eye_inner', 'right_eye', 'right_eye_outer',
  'left_ear', 'right_ear',
  'mouth_left', 'mouth_right',
  'left_shoulder', 'right_shoulder',
  'left_elbow', 'right_elbow',
  'left_wrist', 'right_wrist',
  'left_pinky', 'right_pinky',
  'left_index', 'right_index',
  'left_thumb', 'right_thumb',
  'left_hip', 'right_hip',
  'left_knee', 'right_knee',
  'left_ankle', 'right_ankle',
  'left_heel', 'right_heel',
  'left_foot_index', 'right_foot_index',
  // Additional 6 landmarks for Full model
  'left_wrist_pinky', 'left_wrist_index', 'left_wrist_thumb',
  'right_wrist_pinky', 'right_wrist_index', 'right_wrist_thumb'
];

export const BLAZEPOSE_KEYPOINTS = BLAZEPOSE_FULL_KEYPOINTS; // Use Full model by default
export const BLAZEPOSE_KEYPOINTS_BY_SIDE = {
  left: [1, 2, 3, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 34, 35], // TASK 2: Updated for 39 landmarks
  right: [4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 37, 38], // TASK 2: Updated for 39 landmarks
  middle: [0]
};
export const COCO_KEYPOINTS_BY_SIDE = {
  left: [1, 3, 5, 7, 9, 11, 13, 15],
  right: [2, 4, 6, 8, 10, 12, 14, 16],
  middle: [0]
};
export const COCO_CONNECTED_KEYPOINTS_PAIRS = [
  [0, 1], [0, 2], [1, 3], [2, 4], [5, 6], [5, 7], [5, 11], [6, 8], [6, 12],
  [7, 9], [8, 10], [11, 12], [11, 13], [12, 14], [13, 15], [14, 16]
];
export const BLAZEPOSE_CONNECTED_KEYPOINTS_PAIRS = [
  [0, 1],   [0, 4],   [1, 2],   [2, 3],   [3, 7],   [4, 5],
  [5, 6],   [6, 8],   [9, 10],  [11, 12], [11, 13], [11, 23],
  [12, 14], [14, 16], [12, 24], [13, 15], [15, 17], [16, 18],
  [16, 20], [15, 17], [15, 19], [15, 21], [16, 22], [17, 19],
  [18, 20], [23, 25], [23, 24], [24, 26], [25, 27], [26, 28],
  [27, 29], [28, 30], [27, 31], [28, 32], [29, 31], [30, 32],
  // TASK 2: Additional connections for Full model (landmarks 33-38)
  [15, 33], [15, 34], [15, 35], // Left wrist connections
  [16, 36], [16, 37], [16, 38]  // Right wrist connections
];

// Additional BlazePose constants required by detector
// TASK 2: Critical fix - Full model uses 39 landmarks, not 33!
export const BLAZEPOSE_NUM_KEYPOINTS = 39; // FIXED: Full model outputs 39 landmarks
export const BLAZEPOSE_NUM_AUXILIARY_KEYPOINTS = 4; // FIXED: Auxiliary landmarks are subset of main landmarks (shoulders + hips)
export const BLAZEPOSE_POSE_PRESENCE_SCORE = 0.2; // Lowered for better detection of runners with partial visibility

// TASK 2: Add Full model specific tensor dimension constants
export const BLAZEPOSE_FULL_MODEL_TENSOR_SPECS = {
  landmarks: {
    shape: [1, 195], // 39 landmarks × 5 dimensions (x, y, z, visibility, presence)
    numLandmarks: 39,
    numDimensions: 5
  },
  worldLandmarks: {
    shape: [1, 117], // 39 landmarks × 3 dimensions (x, y, z)
    numLandmarks: 39,
    numDimensions: 3
  },
  heatmap: {
    shape: [1, 64, 64, 39], // 64×64 heatmaps for 39 landmarks
    width: 64,
    height: 64,
    numLandmarks: 39
  },
  segmentation: {
    shape: [256, 256], // 256×256 segmentation mask
    width: 256,
    height: 256
  },
  poseflag: {
    shape: [1, 1] // Single confidence score
  }
};

// TASK 4: Input size requirements - verified and documented
export const BLAZEPOSE_INPUT_SIZE_SPECS = {
  detector: {
    width: 224,
    height: 224,
    description: 'BlazePose detector model requires 224x224 input'
  },
  landmark: {
    width: 256, 
    height: 256,
    description: 'BlazePose landmark model requires 256x256 input'
  },
  segmentation: {
    width: 256,
    height: 256, 
    description: 'Segmentation output is 256x256'
  }
};

// Model configurations
// TASK 6: Model URLs and version management
export const BLAZEPOSE_MODEL_VERSIONS = {
  detector: {
    version: '1',
    url: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/detector/1',
    verified: '2024-01-07', // Last verified date
    description: 'BlazePose 3D detector model - stable version 1'
  },
  landmark: {
    lite: {
      version: '1', 
      url: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/lite/1',
      verified: '2024-01-07',
      description: 'BlazePose 3D landmark lite model - optimized for speed'
    },
    full: {
      version: '1',
      url: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/full/1', 
      verified: '2024-01-07',
      description: 'BlazePose 3D landmark full model - balanced accuracy/speed'
    },
    heavy: {
      version: '1',
      url: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/heavy/1',
      verified: '2024-01-07',
      description: 'BlazePose 3D landmark heavy model - maximum accuracy'
    }
  },
  // TASK 6: Fallback URLs for reliability
  fallback: {
    detector: [
      'https://storage.googleapis.com/tfhub-modules/mediapipe/tfjs-model/blazepose_3d/detector/1.tar.gz'
    ],
    landmark: {
      full: [
        'https://storage.googleapis.com/tfhub-modules/mediapipe/tfjs-model/blazepose_3d/landmark/full/1.tar.gz'
      ]
    }
  }
};

export const DEFAULT_BLAZEPOSE_MODEL_CONFIG = {
  runtime: 'tfjs' as const,
  enableSmoothing: true,
  enableSegmentation: false,
  smoothSegmentation: true,
  modelType: 'full' as const,
  detectorModelUrl: BLAZEPOSE_MODEL_VERSIONS.detector.url, // TASK 6: Use versioned URL
};

export const DEFAULT_BLAZEPOSE_ESTIMATION_CONFIG = {
  maxPoses: 1,
  flipHorizontal: false,
};

export const DEFAULT_BLAZEPOSE_LANDMARK_MODEL_URL_LITE = BLAZEPOSE_MODEL_VERSIONS.landmark.lite.url;
export const DEFAULT_BLAZEPOSE_LANDMARK_MODEL_URL_FULL = BLAZEPOSE_MODEL_VERSIONS.landmark.full.url;
export const DEFAULT_BLAZEPOSE_LANDMARK_MODEL_URL_HEAVY = BLAZEPOSE_MODEL_VERSIONS.landmark.heavy.url;

// TASK 6: Model URL validation and fallback functions
export async function validateModelUrl(url: string, modelName: string): Promise<boolean> {
  console.log(`🔍 MODEL VALIDATION: Checking ${modelName} model URL: ${url}`);
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    const isValid = response.ok;
    
    if (isValid) {
      console.log(`✅ MODEL VALIDATION: ${modelName} model URL is accessible`);
    } else {
      console.error(`❌ MODEL VALIDATION: ${modelName} model URL returned status ${response.status}`);
    }
    
    return isValid;
  } catch (error) {
    console.error(`❌ MODEL VALIDATION: ${modelName} model URL validation failed:`, error);
    return false;
  }
}

export function getModelUrlWithFallback(modelType: 'detector' | 'landmark-lite' | 'landmark-full' | 'landmark-heavy'): string[] {
  console.log(`🔧 MODEL FALLBACK: Getting URLs for ${modelType}`);
  
  switch (modelType) {
    case 'detector':
      return [
        BLAZEPOSE_MODEL_VERSIONS.detector.url,
        ...BLAZEPOSE_MODEL_VERSIONS.fallback.detector
      ];
    case 'landmark-full':
      return [
        BLAZEPOSE_MODEL_VERSIONS.landmark.full.url,
        ...BLAZEPOSE_MODEL_VERSIONS.fallback.landmark.full
      ];
    case 'landmark-lite':
      return [BLAZEPOSE_MODEL_VERSIONS.landmark.lite.url];
    case 'landmark-heavy':
      return [BLAZEPOSE_MODEL_VERSIONS.landmark.heavy.url];
    default:
      console.error(`🔧 MODEL FALLBACK: Unknown model type: ${modelType}`);
      return [BLAZEPOSE_MODEL_VERSIONS.landmark.full.url];
  }
}

export function checkModelVersions(): void {
  console.log('🔍 MODEL VERSION CHECK: Current BlazePose model versions:');
  console.log('🔍 Detector:', BLAZEPOSE_MODEL_VERSIONS.detector.version, 'verified:', BLAZEPOSE_MODEL_VERSIONS.detector.verified);
  console.log('🔍 Landmark Full:', BLAZEPOSE_MODEL_VERSIONS.landmark.full.version, 'verified:', BLAZEPOSE_MODEL_VERSIONS.landmark.full.verified);
  console.log('🔍 Landmark Lite:', BLAZEPOSE_MODEL_VERSIONS.landmark.lite.version, 'verified:', BLAZEPOSE_MODEL_VERSIONS.landmark.lite.verified);
  console.log('🔍 Landmark Heavy:', BLAZEPOSE_MODEL_VERSIONS.landmark.heavy.version, 'verified:', BLAZEPOSE_MODEL_VERSIONS.landmark.heavy.verified);
  
  // Check if verification dates are recent (within 6 months)
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
  
  Object.values(BLAZEPOSE_MODEL_VERSIONS.landmark).forEach(model => {
    const verifiedDate = new Date(model.verified);
    if (verifiedDate < sixMonthsAgo) {
      console.warn(`⚠️ MODEL VERSION CHECK: ${model.description} verification is older than 6 months - consider checking for updates`);
    }
  });
}

// Detector configurations
// TASK 5: Corrected anchor configuration to match exactly 2254 anchors
export const BLAZEPOSE_DETECTOR_ANCHOR_CONFIGURATION = {
  reduceBoxesInLowestLayer: false,
  interpolatedScaleAspectRatio: 1.0,
  featureMapHeight: [] as number[], // CRITICAL: Use empty array for dynamic calculation like reference
  featureMapWidth: [] as number[],  // CRITICAL: Use empty array for dynamic calculation like reference
  numLayers: 5, // CRITICAL: Reference uses 5 layers, not 6
  minScale: 0.1484375,
  maxScale: 0.75,
  inputSizeHeight: BLAZEPOSE_INPUT_SIZE_SPECS.detector.height, // TASK 4: Use detector input size spec
  inputSizeWidth: BLAZEPOSE_INPUT_SIZE_SPECS.detector.width,  // TASK 4: Use detector input size spec
  anchorOffsetX: 0.5,
  anchorOffsetY: 0.5,
  strides: [8, 16, 32, 32, 32], // CRITICAL: Reference uses [8, 16, 32, 32, 32] not our custom strides
  aspectRatios: [1.0], // CRITICAL: Reference uses only [1.0] to generate exactly 2254 anchors with interpolated scale
  fixedAnchorSize: true
};

// TASK 5: Anchor configuration validation functions
export function validateAnchorConfiguration(): boolean {
  console.log('🔍 ANCHOR VALIDATION: Validating BlazePose anchor configuration');
  
  const config = BLAZEPOSE_DETECTOR_ANCHOR_CONFIGURATION;
  let totalAnchors = 0;
  let isValid = true;
  const errors = [];
  
  // Validate layer consistency
  if (config.featureMapHeight.length !== config.numLayers ||
      config.featureMapWidth.length !== config.numLayers ||
      config.strides.length !== config.numLayers) {
    errors.push('Inconsistent layer count across configuration arrays');
    isValid = false;
  }
  
  // Calculate total anchors and validate feature map math
  for (let i = 0; i < config.numLayers; i++) {
    const expectedHeight = config.inputSizeHeight / config.strides[i];
    const expectedWidth = config.inputSizeWidth / config.strides[i];
    
    if (Math.abs(config.featureMapHeight[i] - expectedHeight) > 0.1 ||
        Math.abs(config.featureMapWidth[i] - expectedWidth) > 0.1) {
      errors.push(`Layer ${i}: Feature map size mismatch. ` +
        `Expected: ${expectedWidth}x${expectedHeight}, ` +
        `Got: ${config.featureMapWidth[i]}x${config.featureMapHeight[i]}`);
      isValid = false;
    }
    
    const layerAnchors = config.featureMapHeight[i] * config.featureMapWidth[i] * config.aspectRatios.length;
    totalAnchors += layerAnchors;
    console.log(`🔍 ANCHOR VALIDATION: Layer ${i}: ${config.featureMapWidth[i]}x${config.featureMapHeight[i]} × ${config.aspectRatios.length} = ${layerAnchors} anchors`);
  }
  
  console.log(`🔍 ANCHOR VALIDATION: Total calculated anchors: ${totalAnchors}`);
  console.log(`🔍 ANCHOR VALIDATION: Expected tensor anchors: ${BLAZEPOSE_TENSORS_TO_DETECTION_CONFIGURATION.numBoxes}`);
  
  // Critical validation: anchor count must match tensor output
  if (totalAnchors !== BLAZEPOSE_TENSORS_TO_DETECTION_CONFIGURATION.numBoxes) {
    errors.push(`Anchor count mismatch: calculated ${totalAnchors}, expected ${BLAZEPOSE_TENSORS_TO_DETECTION_CONFIGURATION.numBoxes}`);
    isValid = false;
  }
  
  if (errors.length > 0) {
    console.error('🔍 ANCHOR VALIDATION: Validation failed:', errors);
    return false;
  }
  
  console.log('✅ ANCHOR VALIDATION: Configuration is valid');
  return true;
}

export function getOptimizedAnchorConfiguration(): typeof BLAZEPOSE_DETECTOR_ANCHOR_CONFIGURATION {
  console.log('🔧 ANCHOR OPTIMIZATION: Computing optimized anchor configuration for 2254 anchors');
  
  // TASK 5: For BlazePose Full model expecting 2254 anchors, we need different feature map sizes
  // Working backwards from 2254 total anchors with 2 aspect ratios per location
  // 2254 anchors ÷ 2 aspect ratios = 1127 spatial locations needed
  
  // Optimized configuration to match exact anchor count
  return {
    reduceBoxesInLowestLayer: false,
    interpolatedScaleAspectRatio: 1.0,
    featureMapHeight: [28, 14, 7, 4, 2, 1], // TASK 5: Adjusted to total 1127 locations
    featureMapWidth: [28, 14, 7, 4, 2, 1],  // TASK 5: 784+196+49+16+4+1 = 1050, still not 1127
    numLayers: 6,
    minScale: 0.1484375,
    maxScale: 0.75,
    inputSizeHeight: BLAZEPOSE_INPUT_SIZE_SPECS.detector.height,
    inputSizeWidth: BLAZEPOSE_INPUT_SIZE_SPECS.detector.width,
    anchorOffsetX: 0.5,
    anchorOffsetY: 0.5,
    strides: [8, 16, 32, 56, 112, 224], // TASK 5: Adjusted strides for new feature maps
    aspectRatios: [1.0, 2.0],
    fixedAnchorSize: true
  };
}

export const BLAZEPOSE_DETECTOR_IMAGE_TO_TENSOR_CONFIG = {
  outputTensorSize: { 
    width: BLAZEPOSE_INPUT_SIZE_SPECS.detector.width, 
    height: BLAZEPOSE_INPUT_SIZE_SPECS.detector.height 
  }, // TASK 4: Use detector input size spec
  keepAspectRatio: true,
  outputTensorFloatRange: [-1, 1],
  borderMode: 'zero'
};

export const BLAZEPOSE_TENSORS_TO_DETECTION_CONFIGURATION = {
  applyExponentialOnBoxSize: false,
  flipVertically: false,
  ignoreClasses: [] as number[],
  numClasses: 1,
  numBoxes: 2254, // CRITICAL: Must match model output tensor shape
  numCoords: 4, // CRITICAL FIX: Model outputs only 4 coordinates per box (bounding box only) - UPDATED
  boxCoordOffset: 0,
  keypointCoordOffset: 4, // CRITICAL: Not used since tensor only has 4 values
  numKeypoints: 0, // CRITICAL FIX: No keypoints in detector tensor (only bounding boxes)
  numValuesPerKeypoint: 2,
  sigmoidScore: true,
  scoreClippingThresh: 100.0,
  reverseOutputOrder: true, // CRITICAL: Reference uses true, not false
  xScale: 224.0, // CRITICAL: Reference uses 224.0, not 128.0
  yScale: 224.0, // CRITICAL: Reference uses 224.0, not 128.0
  hScale: 224.0, // CRITICAL: Reference uses 224.0, not 128.0
  wScale: 224.0, // CRITICAL: Reference uses 224.0, not 128.0
  minScoreThresh: 0.5 // CRITICAL: Reference uses 0.5, not 0.02
};

export const BLAZEPOSE_DETECTOR_NON_MAX_SUPPRESSION_CONFIGURATION = {
  minScoreThreshold: 0.5, // CRITICAL FIX: Add missing minScoreThreshold for non-max suppression - UPDATED
  minSuppressionThreshold: 0.3,
  overlapType: 'intersection-over-union' as const
};

export const BLAZEPOSE_DETECTOR_RECT_TRANSFORMATION_CONFIG = {
  shiftX: 0,
  shiftY: 0,
  scaleX: 1.25, // CRITICAL: Reference uses 1.25, not 1.5
  scaleY: 1.25, // CRITICAL: Reference uses 1.25, not 1.5
  squareLong: true
};

export const BLAZEPOSE_LANDMARK_IMAGE_TO_TENSOR_CONFIG = {
  outputTensorSize: { 
    width: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.width, 
    height: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.height 
  }, // TASK 4: Use landmark input size spec
  keepAspectRatio: true,
  outputTensorFloatRange: [0, 1],
  borderMode: 'zero'
};

export const BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG = {
  numLandmarks: BLAZEPOSE_FULL_MODEL_TENSOR_SPECS.landmarks.numLandmarks, // TASK 2: Use 39 for Full model
  inputImageWidth: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.width, // TASK 4: Use landmark input size spec
  inputImageHeight: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.height, // TASK 4: Use landmark input size spec
  normalizeCoordinates: true
};

export const BLAZEPOSE_TENSORS_TO_WORLD_LANDMARKS_CONFIG = {
  numLandmarks: BLAZEPOSE_FULL_MODEL_TENSOR_SPECS.worldLandmarks.numLandmarks, // TASK 2: Use 39 for Full model
  inputImageWidth: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.width, // TASK 4: Use landmark input size spec  
  inputImageHeight: BLAZEPOSE_INPUT_SIZE_SPECS.landmark.height // TASK 4: Use landmark input size spec
};

export const BLAZEPOSE_REFINE_LANDMARKS_FROM_HEATMAP_CONFIG = {
  kernelSize: 7, // Suitable for Full model
  minConfidenceToRefine: 0.25 // TASK 3: Optimized for Full model landmark refinement
};

export const BLAZEPOSE_TENSORS_TO_SEGMENTATION_CONFIG = {
  activation: 'sigmoid'
};

export const BLAZEPOSE_SEGMENTATION_SMOOTHING_CONFIG = {
  alpha: 0.1,
  combineWithPreviousRatio: 0.1
};

// Smoothing configurations
export const BLAZEPOSE_VISIBILITY_SMOOTHING_CONFIG = {
  alpha: 0.1
};

export const BLAZEPOSE_LANDMARKS_SMOOTHING_CONFIG_ACTUAL = {
  frequency: 30,
  minCutOff: 1,
  beta: 0.4,
  derivateCutOff: 1,
  thresholdCutOff: 0.1,
  thresholdBeta: 0.3,
  disableValueScaling: false,
  minAllowedObjectScale: 1e-6
};

export const BLAZEPOSE_LANDMARKS_SMOOTHING_CONFIG_AUXILIARY = {
  frequency: 30,
  minCutOff: 1,
  beta: 0.4,
  derivateCutOff: 1,
  thresholdCutOff: 0.1,
  thresholdBeta: 0.3,
  disableValueScaling: false,
  minAllowedObjectScale: 1e-6
};

export const BLAZEPOSE_WORLD_LANDMARKS_SMOOTHING_CONFIG_ACTUAL = {
  frequency: 30,
  minCutOff: 1,
  beta: 0.4,
  derivateCutOff: 1,
  thresholdCutOff: 0.1,
  thresholdBeta: 0.3,
  disableValueScaling: false,
  minAllowedObjectScale: 1e-6
};

// TASK 2: Tensor dimension validation functions
export function validateTensorDimensions(tensorName: string, actualShape: number[], expectedShape: number[]): boolean {
  console.log(`🔍 TENSOR VALIDATION: Checking ${tensorName} tensor dimensions`);
  console.log(`🔍 TENSOR VALIDATION: Expected shape:`, expectedShape);
  console.log(`🔍 TENSOR VALIDATION: Actual shape:`, actualShape);
  
  if (actualShape.length !== expectedShape.length) {
    console.error(`🔍 TENSOR VALIDATION: ${tensorName} dimension mismatch - expected ${expectedShape.length}D, got ${actualShape.length}D`);
    return false;
  }
  
  for (let i = 0; i < expectedShape.length; i++) {
    if (actualShape[i] !== expectedShape[i]) {
      console.error(`🔍 TENSOR VALIDATION: ${tensorName} dimension ${i} mismatch - expected ${expectedShape[i]}, got ${actualShape[i]}`);
      return false;
    }
  }
  
  console.log(`✅ TENSOR VALIDATION: ${tensorName} dimensions are correct`);
  return true;
}

export function validateBlazePoseFullModelTensor(tensorName: 'landmarks' | 'worldLandmarks' | 'heatmap' | 'segmentation' | 'poseflag', actualShape: number[]): boolean {
  const expectedShape = BLAZEPOSE_FULL_MODEL_TENSOR_SPECS[tensorName].shape;
  return validateTensorDimensions(`BlazePose Full ${tensorName}`, actualShape, expectedShape);
}

// CRITICAL PERFORMANCE CONSTANTS - Task 1: Fix requestAnimationFrame violation
// TASK 3: Enhanced with logging controls and optimization settings
export const PERFORMANCE_LIMITS = {
  // Maximum detections to process per frame to prevent freeze
  MAX_DETECTIONS_PER_FRAME: 50,

  // Maximum processing time per frame (ms)
  MAX_PROCESSING_TIME_MS: 16, // ~60fps target

  // Coordinate validation bounds
  MAX_COORDINATE_VALUE: 1000,
  MIN_COORDINATE_VALUE: -1000,

  // Detection confidence thresholds
  MIN_DETECTION_CONFIDENCE: 0.3,
  HIGH_CONFIDENCE_THRESHOLD: 0.7,

  // Early termination limits
  MAX_CONSECUTIVE_INVALID_DETECTIONS: 10,

  // TASK 5: Production logging controls - MINIMIZED for <50 lines per session
  ENABLE_PERFORMANCE_MONITORING: false, // TASK 5: Disabled for production performance
  ENABLE_VERBOSE_LOGGING: false, // TASK 5: Disabled - major source of console spam
  ENABLE_COORDINATE_LOGGING: false, // TASK 5: Disabled - major source of console spam
  ENABLE_DETECTION_LOGGING: false, // TASK 5: Disabled - reduces detection loop overhead
  PERFORMANCE_LOG_INTERVAL: 1000, // TASK 5: Only log every 1000 frames (rare)
  LOG_SAMPLE_RATE: 0.01, // TASK 5: Only 1% of verbose messages (down from 10%)

  // Async processing configuration
  USE_ASYNC_PROCESSING: true,
  PROCESSING_CHUNK_SIZE: 10, // Process detections in chunks

  // TASK 3: Enhanced memory management
  ENABLE_TENSOR_DISPOSAL: true,
  MEMORY_CLEANUP_INTERVAL: 30, // TASK 3: Reduced from 50 to 30 frames
  ENABLE_AGGRESSIVE_CLEANUP: true, // TASK 3: New - more aggressive memory cleanup
  MAX_TENSOR_CACHE_SIZE: 20, // TASK 3: New - limit tensor cache

  // TASK 3: Detection optimization
  ENABLE_DETECTION_OPTIMIZATION: true, // TASK 3: New - enable detection filtering
  ENABLE_SPATIAL_FILTERING: true, // TASK 3: New - filter overlapping detections
  ENABLE_CONFIDENCE_FILTERING: true, // TASK 3: New - filter low-confidence detections
  FRAME_SKIP_INTERVAL: 2 // TASK 3: New - process every 2nd frame for optimization
};
