// BlazePose Anchor Count Analysis
// This script calculates the exact anchor count for different configurations

function calculateScale(minScale, maxScale, strideIndex, numStrides) {
  if (numStrides === 1) {
    return (minScale + maxScale) * 0.5;
  } else {
    return minScale + (maxScale - minScale) * strideIndex / (numStrides - 1);
  }
}

function calculateAnchorCount(config) {
  console.log('\n🔧 ANALYZING CONFIG:', JSON.stringify(config, null, 2));
  
  let totalAnchors = 0;
  let layerId = 0;
  
  while (layerId < config.numLayers) {
    let aspectRatios = [];
    let scales = [];
    
    // For same strides, we merge the anchors in the same order.
    let lastSameStrideLayer = layerId;
    while (lastSameStrideLayer < config.strides.length &&
           config.strides[lastSameStrideLayer] === config.strides[layerId]) {
      
      const scale = calculateScale(config.minScale, config.maxScale, 
                                 lastSameStrideLayer, config.strides.length);
      
      if (lastSameStrideLayer === 0 && config.reduceBoxesInLowestLayer) {
        // For first layer, it can be specified to use predefined anchors.
        aspectRatios.push(1, 2, 0.5);
        scales.push(0.1, scale, scale);
      } else {
        for (let aspectRatioId = 0; aspectRatioId < config.aspectRatios.length; ++aspectRatioId) {
          aspectRatios.push(config.aspectRatios[aspectRatioId]);
          scales.push(scale);
        }
        if (config.interpolatedScaleAspectRatio > 0.0) {
          const scaleNext = lastSameStrideLayer === config.strides.length - 1 ?
            1.0 :
            calculateScale(config.minScale, config.maxScale,
                          lastSameStrideLayer + 1, config.strides.length);
          scales.push(Math.sqrt(scale * scaleNext));
          aspectRatios.push(config.interpolatedScaleAspectRatio);
        }
      }
      lastSameStrideLayer++;
    }
    
    // Calculate feature map dimensions
    let featureMapHeight = 0;
    let featureMapWidth = 0;
    
    if (config.featureMapHeight.length > 0) {
      featureMapHeight = config.featureMapHeight[layerId];
      featureMapWidth = config.featureMapWidth[layerId];
    } else {
      const stride = config.strides[layerId];
      featureMapHeight = Math.ceil(config.inputSizeHeight / stride);
      featureMapWidth = Math.ceil(config.inputSizeWidth / stride);
    }
    
    const layerAnchors = featureMapHeight * featureMapWidth * aspectRatios.length;
    totalAnchors += layerAnchors;
    
    console.log(`Layer ${layerId}: ${featureMapWidth}x${featureMapHeight} * ${aspectRatios.length} = ${layerAnchors} anchors`);
    console.log(`  Stride: ${config.strides[layerId]}, AspectRatios: ${aspectRatios.length} (${config.aspectRatios.length} standard + ${config.interpolatedScaleAspectRatio > 0 ? 1 : 0} interpolated)`);
    
    layerId = lastSameStrideLayer;
  }
  
  console.log(`\n📊 TOTAL ANCHORS: ${totalAnchors}`);
  return totalAnchors;
}

// Reference BlazePose configuration from TensorFlow.js
const REFERENCE_CONFIG = {
  reduceBoxesInLowestLayer: false,
  interpolatedScaleAspectRatio: 1.0,
  featureMapHeight: [],
  featureMapWidth: [],
  numLayers: 5,
  minScale: 0.1484375,
  maxScale: 0.75,
  inputSizeHeight: 224,
  inputSizeWidth: 224,
  anchorOffsetX: 0.5,
  anchorOffsetY: 0.5,
  strides: [8, 16, 32, 32, 32],
  aspectRatios: [1.0],  // CRITICAL: Reference uses only [1.0]
  fixedAnchorSize: true
};

// Our current configuration
const OUR_CONFIG = {
  reduceBoxesInLowestLayer: false,
  interpolatedScaleAspectRatio: 1.0,
  featureMapHeight: [],
  featureMapWidth: [],
  numLayers: 5,
  minScale: 0.1484375,
  maxScale: 0.75,
  inputSizeHeight: 224,
  inputSizeWidth: 224,
  anchorOffsetX: 0.5,
  anchorOffsetY: 0.5,
  strides: [8, 16, 32, 32, 32],
  aspectRatios: [1.0, 2.0],  // We have 2 aspect ratios
  fixedAnchorSize: true
};

console.log('='.repeat(80));
console.log('BLAZEPOSE ANCHOR COUNT ANALYSIS');
console.log('='.repeat(80));

console.log('\n🔍 REFERENCE CONFIGURATION (TensorFlow.js):');
const referenceCount = calculateAnchorCount(REFERENCE_CONFIG);

console.log('\n🔍 OUR CURRENT CONFIGURATION:');
const ourCount = calculateAnchorCount(OUR_CONFIG);

console.log('\n📋 SUMMARY:');
console.log(`Reference count: ${referenceCount}`);
console.log(`Our count: ${ourCount}`);
console.log(`Expected: 2254`);
console.log(`Reference matches expected: ${referenceCount === 2254}`);
console.log(`Our config matches expected: ${ourCount === 2254}`);

if (referenceCount === 2254) {
  console.log('\n✅ SOLUTION: Use reference configuration with aspectRatios: [1.0]');
} else {
  console.log('\n❌ PROBLEM: Reference configuration does not produce 2254 anchors');
}
