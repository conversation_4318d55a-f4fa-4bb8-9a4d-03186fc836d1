// BlazePose Anchor Analysis
// This script analyzes the exact anchor generation configuration for BlazePose

const BLAZEPOSE_DETECTOR_ANCHOR_CONFIGURATION = {
  reduceBoxesInLowestLayer: false,
  interpolatedScaleAspectRatio: 1.0,
  featureMapHeight: [],
  featureMapWidth: [],
  numLayers: 5,
  minScale: 0.1484375,
  maxScale: 0.75,
  inputSizeHeight: 224,
  inputSizeWidth: 224,
  anchorOffsetX: 0.5,
  anchorOffsetY: 0.5,
  strides: [8, 16, 32, 32, 32],
  aspectRatios: [1.0],
  fixedAnchorSize: true
};

function calculateScale(minScale, maxScale, strideIndex, numStrides) {
  if (numStrides === 1) {
    return (minScale + maxScale) * 0.5;
  } else {
    return minScale + (maxScale - minScale) * strideIndex / (numStrides - 1);
  }
}

function createSsdAnchors(config) {
  // Set defaults
  if (config.reduceBoxesInLowestLayer == null) {
    config.reduceBoxesInLowestLayer = false;
  }
  if (config.interpolatedScaleAspectRatio == null) {
    config.interpolatedScaleAspectRatio = 1.0;
  }
  if (config.fixedAnchorSize == null) {
    config.fixedAnchorSize = false;
  }

  const anchors = [];
  let layerId = 0;
  
  console.log('=== BlazePose Anchor Generation Analysis ===');
  console.log('Configuration:', config);
  console.log('');

  while (layerId < config.numLayers) {
    const anchorHeight = [];
    const anchorWidth = [];
    const aspectRatios = [];
    const scales = [];

    // For same strides, we merge the anchors in the same order
    let lastSameStrideLayer = layerId;
    console.log(`Processing layer ${layerId}, stride: ${config.strides[layerId]}`);
    
    while (lastSameStrideLayer < config.strides.length &&
           config.strides[lastSameStrideLayer] === config.strides[layerId]) {
      
      const scale = calculateScale(
          config.minScale, config.maxScale, lastSameStrideLayer,
          config.strides.length);
      
      console.log(`  Layer ${lastSameStrideLayer}: scale = ${scale}`);
      
      if (lastSameStrideLayer === 0 && config.reduceBoxesInLowestLayer) {
        // For first layer, it can be specified to use predefined anchors
        aspectRatios.push(1, 2, 0.5);
        scales.push(0.1, scale, scale);
      } else {
        // Add anchors for each aspect ratio
        for (let aspectRatioId = 0; aspectRatioId < config.aspectRatios.length; aspectRatioId++) {
          aspectRatios.push(config.aspectRatios[aspectRatioId]);
          scales.push(scale);
        }
        
        // Key insight: interpolatedScaleAspectRatio adds an EXTRA anchor!
        if (config.interpolatedScaleAspectRatio > 0.0) {
          const scaleNext = lastSameStrideLayer === config.strides.length - 1 ?
              1.0 :
              calculateScale(
                  config.minScale, config.maxScale, lastSameStrideLayer + 1,
                  config.strides.length);
          const interpolatedScale = Math.sqrt(scale * scaleNext);
          scales.push(interpolatedScale);
          aspectRatios.push(config.interpolatedScaleAspectRatio);
          
          console.log(`    Added interpolated anchor: scale = ${interpolatedScale}, aspectRatio = ${config.interpolatedScaleAspectRatio}`);
        }
      }
      lastSameStrideLayer++;
    }

    // Calculate anchor dimensions
    for (let i = 0; i < aspectRatios.length; i++) {
      const ratioSqrt = Math.sqrt(aspectRatios[i]);
      anchorHeight.push(scales[i] / ratioSqrt);
      anchorWidth.push(scales[i] * ratioSqrt);
    }

    // Calculate feature map size
    let featureMapHeight = 0;
    let featureMapWidth = 0;
    if (config.featureMapHeight.length > 0) {
      featureMapHeight = config.featureMapHeight[layerId];
      featureMapWidth = config.featureMapWidth[layerId];
    } else {
      const stride = config.strides[layerId];
      featureMapHeight = Math.ceil(config.inputSizeHeight / stride);
      featureMapWidth = Math.ceil(config.inputSizeWidth / stride);
    }

    console.log(`  Feature map size: ${featureMapWidth} x ${featureMapHeight}`);
    console.log(`  Anchors per cell: ${anchorHeight.length}`);
    console.log(`  Total anchors for this layer: ${featureMapWidth * featureMapHeight * anchorHeight.length}`);

    // Generate anchors for this layer
    for (let y = 0; y < featureMapHeight; y++) {
      for (let x = 0; x < featureMapWidth; x++) {
        for (let anchorId = 0; anchorId < anchorHeight.length; anchorId++) {
          const xCenter = (x + config.anchorOffsetX) / featureMapWidth;
          const yCenter = (y + config.anchorOffsetY) / featureMapHeight;

          const newAnchor = {
            xCenter, 
            yCenter, 
            width: config.fixedAnchorSize ? 1.0 : anchorWidth[anchorId],
            height: config.fixedAnchorSize ? 1.0 : anchorHeight[anchorId]
          };
          
          anchors.push(newAnchor);
        }
      }
    }

    layerId = lastSameStrideLayer;
    console.log('');
  }

  return anchors;
}

// Run the analysis
const anchors = createSsdAnchors(BLAZEPOSE_DETECTOR_ANCHOR_CONFIGURATION);

console.log('=== Summary ===');
console.log(`Total anchors generated: ${anchors.length}`);
console.log(`Expected from constants: 2254`);
console.log(`Match: ${anchors.length === 2254 ? 'YES' : 'NO'}`);
console.log('');

// Let's also calculate manually to understand the doubling
console.log('=== Manual Calculation ===');
const strides = [8, 16, 32, 32, 32];
const inputSize = 224;
let totalAnchors = 0;

for (let i = 0; i < strides.length; i++) {
  const stride = strides[i];
  const featureMapSize = Math.ceil(inputSize / stride);
  const cellsPerLayer = featureMapSize * featureMapSize;
  
  // Each layer has 1 anchor per aspect ratio + 1 interpolated anchor
  const anchorsPerCell = 1 + 1; // 1 for aspectRatio[1.0] + 1 for interpolatedScaleAspectRatio
  const layerAnchors = cellsPerLayer * anchorsPerCell;
  
  console.log(`Layer ${i}: stride=${stride}, featureMap=${featureMapSize}x${featureMapSize}, cells=${cellsPerLayer}, anchors/cell=${anchorsPerCell}, total=${layerAnchors}`);
  totalAnchors += layerAnchors;
}

console.log(`Manual total: ${totalAnchors}`);
console.log('');
console.log('=== Key Insight ===');
console.log('The doubling factor comes from interpolatedScaleAspectRatio = 1.0');
console.log('This setting adds an extra anchor per cell, effectively doubling the count');
console.log('Basic calculation: 1127 anchors (1 per cell)');
console.log('With interpolation: 2254 anchors (2 per cell)');