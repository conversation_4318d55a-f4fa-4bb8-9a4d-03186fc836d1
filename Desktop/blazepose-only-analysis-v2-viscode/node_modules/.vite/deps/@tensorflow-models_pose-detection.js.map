{"version": 3, "sources": ["../../@mediapipe/pose/pose.js", "../../@tensorflow-models/pose-detection/dist/pose-detection.esm.js"], "sourcesContent": ["(function(){/*\n\n Copyright The Closure Library Authors.\n SPDX-License-Identifier: Apache-2.0\n*/\n'use strict';var x;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba=\"function\"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};\nfunction ca(a){a=[\"object\"==typeof globalThis&&globalThis,a,\"object\"==typeof window&&window,\"object\"==typeof self&&self,\"object\"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error(\"Cannot find global object\");}var y=ca(this);function z(a,b){if(b)a:{var c=y;a=a.split(\".\");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}\nz(\"Symbol\",function(a){function b(g){if(this instanceof b)throw new TypeError(\"Symbol is not a constructor\");return new c(d+(g||\"\")+\"_\"+e++,g)}function c(g,f){this.h=g;ba(this,\"description\",{configurable:!0,writable:!0,value:f})}if(a)return a;c.prototype.toString=function(){return this.h};var d=\"jscomp_symbol_\"+(1E9*Math.random()>>>0)+\"_\",e=0;return b});\nz(\"Symbol.iterator\",function(a){if(a)return a;a=Symbol(\"Symbol.iterator\");for(var b=\"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array\".split(\" \"),c=0;c<b.length;c++){var d=y[b[c]];\"function\"===typeof d&&\"function\"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return da(aa(this))}})}return a});function da(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}\nfunction A(a){var b=\"undefined\"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}function ea(a){if(!(a instanceof Array)){a=A(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}var fa=\"function\"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};z(\"Object.assign\",function(a){return a||fa});\nvar ha=\"function\"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ia;if(\"function\"==typeof Object.setPrototypeOf)ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},la={};try{la.__proto__=ka;ja=la.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+\" is not extensible\");return a}:null}var ma=ia;\nfunction na(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(ma)ma(a,b);else for(var c in b)if(\"prototype\"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.za=b.prototype}function oa(){this.m=!1;this.j=null;this.i=void 0;this.h=1;this.v=this.s=0;this.l=null}function pa(a){if(a.m)throw new TypeError(\"Generator is already running\");a.m=!0}oa.prototype.u=function(a){this.i=a};\nfunction qa(a,b){a.l={ma:b,na:!0};a.h=a.s||a.v}oa.prototype.return=function(a){this.l={return:a};this.h=this.v};function D(a,b,c){a.h=c;return{value:b}}function ra(a){this.h=new oa;this.i=a}function sa(a,b){pa(a.h);var c=a.h.j;if(c)return ta(a,\"return\"in c?c[\"return\"]:function(d){return{value:d,done:!0}},b,a.h.return);a.h.return(b);return ua(a)}\nfunction ta(a,b,c,d){try{var e=b.call(a.h.j,c);if(!(e instanceof Object))throw new TypeError(\"Iterator result \"+e+\" is not an object\");if(!e.done)return a.h.m=!1,e;var g=e.value}catch(f){return a.h.j=null,qa(a.h,f),ua(a)}a.h.j=null;d.call(a.h,g);return ua(a)}function ua(a){for(;a.h.h;)try{var b=a.i(a.h);if(b)return a.h.m=!1,{value:b.value,done:!1}}catch(c){a.h.i=void 0,qa(a.h,c)}a.h.m=!1;if(a.h.l){b=a.h.l;a.h.l=null;if(b.na)throw b.ma;return{value:b.return,done:!0}}return{value:void 0,done:!0}}\nfunction va(a){this.next=function(b){pa(a.h);a.h.j?b=ta(a,a.h.j.next,b,a.h.u):(a.h.u(b),b=ua(a));return b};this.throw=function(b){pa(a.h);a.h.j?b=ta(a,a.h.j[\"throw\"],b,a.h.u):(qa(a.h,b),b=ua(a));return b};this.return=function(b){return sa(a,b)};this[Symbol.iterator]=function(){return this}}function wa(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function g(f){f.done?d(f.value):Promise.resolve(f.value).then(b,c).then(g,e)}g(a.next())})}\nfunction E(a){return wa(new va(new ra(a)))}\nz(\"Promise\",function(a){function b(f){this.i=0;this.j=void 0;this.h=[];this.u=!1;var h=this.l();try{f(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.h=null}function d(f){return f instanceof b?f:new b(function(h){h(f)})}if(a)return a;c.prototype.i=function(f){if(null==this.h){this.h=[];var h=this;this.j(function(){h.m()})}this.h.push(f)};var e=y.setTimeout;c.prototype.j=function(f){e(f,0)};c.prototype.m=function(){for(;this.h&&this.h.length;){var f=this.h;this.h=[];for(var h=0;h<f.length;++h){var k=\nf[h];f[h]=null;try{k()}catch(l){this.l(l)}}}this.h=null};c.prototype.l=function(f){this.j(function(){throw f;})};b.prototype.l=function(){function f(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:f(this.I),reject:f(this.m)}};b.prototype.I=function(f){if(f===this)this.m(new TypeError(\"A Promise cannot resolve to itself\"));else if(f instanceof b)this.L(f);else{a:switch(typeof f){case \"object\":var h=null!=f;break a;case \"function\":h=!0;break a;default:h=!1}h?this.F(f):this.s(f)}};\nb.prototype.F=function(f){var h=void 0;try{h=f.then}catch(k){this.m(k);return}\"function\"==typeof h?this.M(h,f):this.s(f)};b.prototype.m=function(f){this.v(2,f)};b.prototype.s=function(f){this.v(1,f)};b.prototype.v=function(f,h){if(0!=this.i)throw Error(\"Cannot settle(\"+f+\", \"+h+\"): Promise already settled in state\"+this.i);this.i=f;this.j=h;2===this.i&&this.K();this.H()};b.prototype.K=function(){var f=this;e(function(){if(f.D()){var h=y.console;\"undefined\"!==typeof h&&h.error(f.j)}},1)};b.prototype.D=\nfunction(){if(this.u)return!1;var f=y.CustomEvent,h=y.Event,k=y.dispatchEvent;if(\"undefined\"===typeof k)return!0;\"function\"===typeof f?f=new f(\"unhandledrejection\",{cancelable:!0}):\"function\"===typeof h?f=new h(\"unhandledrejection\",{cancelable:!0}):(f=y.document.createEvent(\"CustomEvent\"),f.initCustomEvent(\"unhandledrejection\",!1,!0,f));f.promise=this;f.reason=this.j;return k(f)};b.prototype.H=function(){if(null!=this.h){for(var f=0;f<this.h.length;++f)g.i(this.h[f]);this.h=null}};var g=new c;b.prototype.L=\nfunction(f){var h=this.l();f.T(h.resolve,h.reject)};b.prototype.M=function(f,h){var k=this.l();try{f.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(f,h){function k(p,n){return\"function\"==typeof p?function(q){try{l(p(q))}catch(t){m(t)}}:n}var l,m,r=new b(function(p,n){l=p;m=n});this.T(k(f,l),k(h,m));return r};b.prototype.catch=function(f){return this.then(void 0,f)};b.prototype.T=function(f,h){function k(){switch(l.i){case 1:f(l.j);break;case 2:h(l.j);break;default:throw Error(\"Unexpected state: \"+\nl.i);}}var l=this;null==this.h?g.i(k):this.h.push(k);this.u=!0};b.resolve=d;b.reject=function(f){return new b(function(h,k){k(f)})};b.race=function(f){return new b(function(h,k){for(var l=A(f),m=l.next();!m.done;m=l.next())d(m.value).T(h,k)})};b.all=function(f){var h=A(f),k=h.next();return k.done?d([]):new b(function(l,m){function r(q){return function(t){p[q]=t;n--;0==n&&l(p)}}var p=[],n=0;do p.push(void 0),n++,d(k.value).T(r(p.length-1),m),k=h.next();while(!k.done)})};return b});\nfunction xa(a,b){a instanceof String&&(a+=\"\");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var g=c++;return{value:b(g,a[g]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}z(\"Array.prototype.keys\",function(a){return a?a:function(){return xa(this,function(b){return b})}});\nz(\"Array.prototype.fill\",function(a){return a?a:function(b,c,d){var e=this.length||0;0>c&&(c=Math.max(0,e+c));if(null==d||d>e)d=e;d=Number(d);0>d&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});function F(a){return a?a:Array.prototype.fill}z(\"Int8Array.prototype.fill\",F);z(\"Uint8Array.prototype.fill\",F);z(\"Uint8ClampedArray.prototype.fill\",F);z(\"Int16Array.prototype.fill\",F);z(\"Uint16Array.prototype.fill\",F);z(\"Int32Array.prototype.fill\",F);\nz(\"Uint32Array.prototype.fill\",F);z(\"Float32Array.prototype.fill\",F);z(\"Float64Array.prototype.fill\",F);z(\"Object.is\",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});z(\"Array.prototype.includes\",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||Object.is(g,b))return!0}return!1}});\nz(\"String.prototype.includes\",function(a){return a?a:function(b,c){if(null==this)throw new TypeError(\"The 'this' value for String.prototype.includes must not be null or undefined\");if(b instanceof RegExp)throw new TypeError(\"First argument to String.prototype.includes must not be a regular expression\");return-1!==this.indexOf(b,c||0)}});var ya=this||self;\nfunction G(a,b){a=a.split(\".\");var c=ya;a[0]in c||\"undefined\"==typeof c.execScript||c.execScript(\"var \"+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function Aa(a){var b;a:{if(b=ya.navigator)if(b=b.userAgent)break a;b=\"\"}return-1!=b.indexOf(a)};var Ba=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e=\"string\"===typeof a?a.split(\"\"):a,g=0;g<c;g++)g in e&&(d[g]=b.call(void 0,e[g],g,a));return d};var Ca={},Da=null;function Ea(a){var b=a.length,c=3*b/4;c%3?c=Math.floor(c):-1!=\"=.\".indexOf(a[b-1])&&(c=-1!=\"=.\".indexOf(a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;Fa(a,function(g){d[e++]=g});return e!==c?d.subarray(0,e):d}\nfunction Fa(a,b){function c(k){for(;d<a.length;){var l=a.charAt(d++),m=Da[l];if(null!=m)return m;if(!/^[\\s\\xa0]*$/.test(l))throw Error(\"Unknown base64 encoding at char: \"+l);}return k}Ga();for(var d=0;;){var e=c(-1),g=c(0),f=c(64),h=c(64);if(64===h&&-1===e)break;b(e<<2|g>>4);64!=f&&(b(g<<4&240|f>>2),64!=h&&b(f<<6&192|h))}}\nfunction Ga(){if(!Da){Da={};for(var a=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\"),b=[\"+/=\",\"+/\",\"-_=\",\"-_.\",\"-_\"],c=0;5>c;c++){var d=a.concat(b[c].split(\"\"));Ca[c]=d;for(var e=0;e<d.length;e++){var g=d[e];void 0===Da[g]&&(Da[g]=e)}}}};var Ha=\"undefined\"!==typeof Uint8Array,Ia=!(Aa(\"Trident\")||Aa(\"MSIE\"))&&\"function\"===typeof ya.btoa;\nfunction Ja(a){if(!Ia){var b;void 0===b&&(b=0);Ga();b=Ca[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||\"\",e=0,g=0;e<a.length-2;e+=3){var f=a[e],h=a[e+1],k=a[e+2],l=b[f>>2];f=b[(f&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[g++]=l+f+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[g]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join(\"\")}for(b=\"\";10240<a.length;)b+=String.fromCharCode.apply(null,a.subarray(0,10240)),a=a.subarray(10240);b+=String.fromCharCode.apply(null,\na);return btoa(b)}var Ka=RegExp(\"[-_.]\",\"g\");function La(a){switch(a){case \"-\":return\"+\";case \"_\":return\"/\";case \".\":return\"=\";default:return\"\"}}function Ma(a){if(!Ia)return Ea(a);Ka.test(a)&&(a=a.replace(Ka,La));a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b}var Na;function Oa(){return Na||(Na=new Uint8Array(0))}var Pa={};var Qa=\"function\"===typeof Uint8Array.prototype.slice,H=0,K=0;function Ra(a){var b=0>a;a=Math.abs(a);var c=a>>>0;a=Math.floor((a-c)/4294967296);b&&(c=A(Sa(c,a)),b=c.next().value,a=c.next().value,c=b);H=c>>>0;K=a>>>0}var Ta=\"function\"===typeof BigInt;function Sa(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function Ua(a,b){this.i=a>>>0;this.h=b>>>0}\nfunction Va(a){if(!a)return Wa||(Wa=new Ua(0,0));if(!/^-?\\d+$/.test(a))return null;if(16>a.length)Ra(Number(a));else if(Ta)a=BigInt(a),H=Number(a&BigInt(4294967295))>>>0,K=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(\"-\"===a[0]);K=H=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),K*=1E6,H=1E6*H+d,4294967296<=H&&(K+=H/4294967296|0,H%=4294967296);b&&(b=A(Sa(H,K)),a=b.next().value,b=b.next().value,H=a,K=b)}return new Ua(H,K)}var Wa;function Xa(a,b){return Error(\"Invalid wire type: \"+a+\" (at position \"+b+\")\")}function Ya(){return Error(\"Failed to read varint, encoding is invalid.\")}function Za(a,b){return Error(\"Tried to read past the end of the data \"+b+\" > \"+a)};function L(){throw Error(\"Invalid UTF8\");}function $a(a,b){b=String.fromCharCode.apply(null,b);return null==a?b:a+b}var ab=void 0,bb,cb=\"undefined\"!==typeof TextDecoder,db,eb=\"undefined\"!==typeof TextEncoder;var fb;function gb(a){if(a!==Pa)throw Error(\"illegal external caller\");}function hb(a,b){gb(b);this.V=a;if(null!=a&&0===a.length)throw Error(\"ByteString should be constructed with non-empty values\");}function ib(){return fb||(fb=new hb(null,Pa))}function jb(a){gb(Pa);var b=a.V;b=null==b||Ha&&null!=b&&b instanceof Uint8Array?b:\"string\"===typeof b?Ma(b):null;return null==b?b:a.V=b};function kb(a){if(\"string\"===typeof a)return{buffer:Ma(a),C:!1};if(Array.isArray(a))return{buffer:new Uint8Array(a),C:!1};if(a.constructor===Uint8Array)return{buffer:a,C:!1};if(a.constructor===ArrayBuffer)return{buffer:new Uint8Array(a),C:!1};if(a.constructor===hb)return{buffer:jb(a)||Oa(),C:!0};if(a instanceof Uint8Array)return{buffer:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),C:!1};throw Error(\"Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers\");\n};function lb(a,b){this.i=null;this.m=!1;this.h=this.j=this.l=0;mb(this,a,b)}function mb(a,b,c){c=void 0===c?{}:c;a.S=void 0===c.S?!1:c.S;b&&(b=kb(b),a.i=b.buffer,a.m=b.C,a.l=0,a.j=a.i.length,a.h=a.l)}lb.prototype.reset=function(){this.h=this.l};function M(a,b){a.h=b;if(b>a.j)throw Za(a.j,b);}\nfunction nb(a){var b=a.i,c=a.h,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw Ya();M(a,c);return e}function ob(a,b){if(0>b)throw Error(\"Tried to read a negative byte length: \"+b);var c=a.h,d=c+b;if(d>a.j)throw Za(b,a.j-c);a.h=d;return c}var pb=[];function qb(){this.h=[]}qb.prototype.length=function(){return this.h.length};qb.prototype.end=function(){var a=this.h;this.h=[];return a};function rb(a,b,c){for(;0<c||127<b;)a.h.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.h.push(b)}function N(a,b){for(;127<b;)a.h.push(b&127|128),b>>>=7;a.h.push(b)};function sb(a,b){if(pb.length){var c=pb.pop();mb(c,a,b);a=c}else a=new lb(a,b);this.h=a;this.j=this.h.h;this.i=this.l=-1;this.setOptions(b)}sb.prototype.setOptions=function(a){a=void 0===a?{}:a;this.ca=void 0===a.ca?!1:a.ca};sb.prototype.reset=function(){this.h.reset();this.j=this.h.h;this.i=this.l=-1};\nfunction tb(a){var b=a.h;if(b.h==b.j)return!1;a.j=a.h.h;var c=nb(a.h)>>>0;b=c>>>3;c&=7;if(!(0<=c&&5>=c))throw Xa(c,a.j);if(1>b)throw Error(\"Invalid field number: \"+b+\" (at position \"+a.j+\")\");a.l=b;a.i=c;return!0}\nfunction ub(a){switch(a.i){case 0:if(0!=a.i)ub(a);else a:{a=a.h;for(var b=a.h,c=b+10,d=a.i;b<c;)if(0===(d[b++]&128)){M(a,b);break a}throw Ya();}break;case 1:a=a.h;M(a,a.h+8);break;case 2:2!=a.i?ub(a):(b=nb(a.h)>>>0,a=a.h,M(a,a.h+b));break;case 5:a=a.h;M(a,a.h+4);break;case 3:b=a.l;do{if(!tb(a))throw Error(\"Unmatched start-group tag: stream EOF\");if(4==a.i){if(a.l!=b)throw Error(\"Unmatched end-group tag\");break}ub(a)}while(1);break;default:throw Xa(a.i,a.j);}}var vb=[];function wb(){this.j=[];this.i=0;this.h=new qb}function O(a,b){0!==b.length&&(a.j.push(b),a.i+=b.length)}function xb(a,b){if(b=b.R){O(a,a.h.end());for(var c=0;c<b.length;c++)O(a,jb(b[c])||Oa())}};var P=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol()?Symbol():void 0;function Q(a,b){if(P)return a[P]|=b;if(void 0!==a.A)return a.A|=b;Object.defineProperties(a,{A:{value:b,configurable:!0,writable:!0,enumerable:!1}});return b}function yb(a,b){P?a[P]&&(a[P]&=~b):void 0!==a.A&&(a.A&=~b)}function R(a){var b;P?b=a[P]:b=a.A;return null==b?0:b}function S(a,b){P?a[P]=b:void 0!==a.A?a.A=b:Object.defineProperties(a,{A:{value:b,configurable:!0,writable:!0,enumerable:!1}})}\nfunction zb(a){Q(a,1);return a}function Ab(a,b){S(b,(a|0)&-51)}function Bb(a,b){S(b,(a|18)&-41)};var Cb={};function Db(a){return null!==a&&\"object\"===typeof a&&!Array.isArray(a)&&a.constructor===Object}var Eb,Fb=[];S(Fb,23);Eb=Object.freeze(Fb);function Gb(a){if(R(a.o)&2)throw Error(\"Cannot mutate an immutable Message\");}function Hb(a){var b=a.length;(b=b?a[b-1]:void 0)&&Db(b)?b.g=1:(b={},a.push((b.g=1,b)))};function Ib(a){var b=a.i+a.G;return a.B||(a.B=a.o[b]={})}function T(a,b){return-1===b?null:b>=a.i?a.B?a.B[b]:void 0:a.o[b+a.G]}function V(a,b,c,d){Gb(a);Jb(a,b,c,d)}function Jb(a,b,c,d){a.j&&(a.j=void 0);b>=a.i||d?Ib(a)[b]=c:(a.o[b+a.G]=c,(a=a.B)&&b in a&&delete a[b])}function Kb(a,b,c,d){var e=T(a,b);Array.isArray(e)||(e=Eb);var g=R(e);g&1||zb(e);if(d)g&2||Q(e,2),c&1||Object.freeze(e);else{d=!(c&2);var f=g&2;c&1||!f?d&&g&16&&!f&&yb(e,16):(e=zb(Array.prototype.slice.call(e)),Jb(a,b,e))}return e}\nfunction Lb(a,b){var c=T(a,b);var d=null==c?c:\"number\"===typeof c||\"NaN\"===c||\"Infinity\"===c||\"-Infinity\"===c?Number(c):void 0;null!=d&&d!==c&&Jb(a,b,d);return d}\nfunction Mb(a,b,c,d,e){a.h||(a.h={});var g=a.h[c],f=Kb(a,c,3,e);if(!g){var h=f;g=[];var k=!!(R(a.o)&16);f=!!(R(h)&2);var l=h;!e&&f&&(h=Array.prototype.slice.call(h));for(var m=f,r=0;r<h.length;r++){var p=h[r];var n=b,q=!1;q=void 0===q?!1:q;p=Array.isArray(p)?new n(p):q?new n:void 0;if(void 0!==p){n=p.o;var t=q=R(n);f&&(t|=2);k&&(t|=16);t!=q&&S(n,t);n=t;m=m||!!(2&n);g.push(p)}}a.h[c]=g;k=R(h);b=k|33;b=m?b&-9:b|8;k!=b&&(m=h,Object.isFrozen(m)&&(m=Array.prototype.slice.call(m)),S(m,b),h=m);l!==h&&Jb(a,\nc,h);(e||d&&f)&&Q(g,2);d&&Object.freeze(g);return g}e||(e=Object.isFrozen(g),d&&!e?Object.freeze(g):!d&&e&&(g=Array.prototype.slice.call(g),a.h[c]=g));return g}function Nb(a,b,c){var d=!!(R(a.o)&2);b=Mb(a,b,c,d,d);a=Kb(a,c,3,d);if(!(d||R(a)&8)){for(d=0;d<b.length;d++){c=b[d];if(R(c.o)&2){var e=Ob(c,!1);e.j=c}else e=c;c!==e&&(b[d]=e,a[d]=e.o)}Q(a,8)}return b}\nfunction W(a,b,c){if(null!=c&&\"number\"!==typeof c)throw Error(\"Value of float/double field must be a number|null|undefined, found \"+typeof c+\": \"+c);V(a,b,c)}function Pb(a,b,c,d,e){Gb(a);var g=Mb(a,c,b,!1,!1);c=null!=d?d:new c;a=Kb(a,b,2,!1);void 0!=e?(g.splice(e,0,c),a.splice(e,0,c.o)):(g.push(c),a.push(c.o));c.C()&&yb(a,8);return c}function Qb(a,b){return null==a?b:a}function X(a,b,c){c=void 0===c?0:c;return Qb(Lb(a,b),c)};var Rb;function Sb(a){switch(typeof a){case \"number\":return isFinite(a)?a:String(a);case \"object\":if(a)if(Array.isArray(a)){if(0!==(R(a)&128))return a=Array.prototype.slice.call(a),Hb(a),a}else{if(Ha&&null!=a&&a instanceof Uint8Array)return Ja(a);if(a instanceof hb){var b=a.V;return null==b?\"\":\"string\"===typeof b?b:a.V=Ja(b)}}}return a};function Tb(a,b,c,d){if(null!=a){if(Array.isArray(a))a=Ub(a,b,c,void 0!==d);else if(Db(a)){var e={},g;for(g in a)e[g]=Tb(a[g],b,c,d);a=e}else a=b(a,d);return a}}function Ub(a,b,c,d){var e=R(a);d=d?!!(e&16):void 0;a=Array.prototype.slice.call(a);for(var g=0;g<a.length;g++)a[g]=Tb(a[g],b,c,d);c(e,a);return a}function Vb(a){return a.ja===Cb?a.toJSON():Sb(a)}function Wb(a,b){a&128&&Hb(b)};function Xb(a,b,c){c=void 0===c?Bb:c;if(null!=a){if(Ha&&a instanceof Uint8Array)return a.length?new hb(new Uint8Array(a),Pa):ib();if(Array.isArray(a)){var d=R(a);if(d&2)return a;if(b&&!(d&32)&&(d&16||0===d))return S(a,d|2),a;a=Ub(a,Xb,d&4?Bb:c,!0);b=R(a);b&4&&b&2&&Object.freeze(a);return a}return a.ja===Cb?Yb(a):a}}\nfunction Zb(a,b,c,d,e,g,f){if(a=a.h&&a.h[c]){d=R(a);d&2?d=a:(g=Ba(a,Yb),Bb(d,g),Object.freeze(g),d=g);Gb(b);f=null==d?Eb:zb([]);if(null!=d){g=!!d.length;for(a=0;a<d.length;a++){var h=d[a];g=g&&!(R(h.o)&2);f[a]=h.o}g=(g?8:0)|1;a=R(f);(a&g)!==g&&(Object.isFrozen(f)&&(f=Array.prototype.slice.call(f)),S(f,a|g));b.h||(b.h={});b.h[c]=d}else b.h&&(b.h[c]=void 0);Jb(b,c,f,e)}else V(b,c,Xb(d,g,f),e)}function Yb(a){if(R(a.o)&2)return a;a=Ob(a,!0);Q(a.o,2);return a}\nfunction Ob(a,b){var c=a.o,d=[];Q(d,16);var e=a.constructor.h;e&&d.push(e);e=a.B;if(e){d.length=c.length;d.fill(void 0,d.length,c.length);var g={};d[d.length-1]=g}0!==(R(c)&128)&&Hb(d);b=b||a.C()?Bb:Ab;g=a.constructor;Rb=d;d=new g(d);Rb=void 0;a.R&&(d.R=a.R.slice());g=!!(R(c)&16);for(var f=e?c.length-1:c.length,h=0;h<f;h++)Zb(a,d,h-a.G,c[h],!1,g,b);if(e)for(var k in e)Zb(a,d,+k,e[k],!0,g,b);return d};function Y(a,b,c){null==a&&(a=Rb);Rb=void 0;var d=this.constructor.i||0,e=0<d,g=this.constructor.h,f=!1;if(null==a){a=g?[g]:[];var h=48;var k=!0;e&&(d=0,h|=128);S(a,h)}else{if(!Array.isArray(a))throw Error();if(g&&g!==a[0])throw Error();var l=h=Q(a,0);if(k=0!==(16&l))(f=0!==(32&l))||(l|=32);if(e)if(128&l)d=0;else{if(0<a.length){var m=a[a.length-1];if(Db(m)&&\"g\"in m){d=0;l|=128;delete m.g;var r=!0,p;for(p in m){r=!1;break}r&&a.pop()}}}else if(128&l)throw Error();h!==l&&S(a,l)}this.G=(g?0:-1)-d;this.h=\nvoid 0;this.o=a;a:{g=this.o.length;d=g-1;if(g&&(g=this.o[d],Db(g))){this.B=g;this.i=d-this.G;break a}void 0!==b&&-1<b?(this.i=Math.max(b,d+1-this.G),this.B=void 0):this.i=Number.MAX_VALUE}if(!e&&this.B&&\"g\"in this.B)throw Error('Unexpected \"g\" flag in sparse object of message that is not a group type.');if(c){b=k&&!f&&!0;e=this.i;var n;for(k=0;k<c.length;k++)f=c[k],f<e?(f+=this.G,(d=a[f])?$b(d,b):a[f]=Eb):(n||(n=Ib(this)),(d=n[f])?$b(d,b):n[f]=Eb)}}\nY.prototype.toJSON=function(){return Ub(this.o,Vb,Wb)};Y.prototype.C=function(){return!!(R(this.o)&2)};function $b(a,b){if(Array.isArray(a)){var c=R(a),d=1;!b||c&2||(d|=16);(c&d)!==d&&S(a,c|d)}}Y.prototype.ja=Cb;Y.prototype.toString=function(){return this.o.toString()};function ac(a,b,c){if(c){var d={},e;for(e in c){var g=c[e],f=g.ra;f||(d.J=g.xa||g.oa.W,g.ia?(d.aa=bc(g.ia),f=function(h){return function(k,l,m){return h.J(k,l,m,h.aa)}}(d)):g.ka?(d.Z=cc(g.da.P,g.ka),f=function(h){return function(k,l,m){return h.J(k,l,m,h.Z)}}(d)):f=d.J,g.ra=f);f(b,a,g.da);d={J:d.J,aa:d.aa,Z:d.Z}}}xb(b,a)}var dc=Symbol();function ec(a,b,c){return a[dc]||(a[dc]=function(d,e){return b(d,e,c)})}\nfunction fc(a){var b=a[dc];if(!b){var c=gc(a);b=function(d,e){return hc(d,e,c)};a[dc]=b}return b}function ic(a){var b=a.ia;if(b)return fc(b);if(b=a.wa)return ec(a.da.P,b,a.ka)}function jc(a){var b=ic(a),c=a.da,d=a.oa.U;return b?function(e,g){return d(e,g,c,b)}:function(e,g){return d(e,g,c)}}function kc(a,b){var c=a[b];\"function\"==typeof c&&0===c.length&&(c=c(),a[b]=c);return Array.isArray(c)&&(lc in c||mc in c||0<c.length&&\"function\"==typeof c[0])?c:void 0}\nfunction nc(a,b,c,d,e,g){b.P=a[0];var f=1;if(a.length>f&&\"number\"!==typeof a[f]){var h=a[f++];c(b,h)}for(;f<a.length;){c=a[f++];for(var k=f+1;k<a.length&&\"number\"!==typeof a[k];)k++;h=a[f++];k-=f;switch(k){case 0:d(b,c,h);break;case 1:(k=kc(a,f))?(f++,e(b,c,h,k)):d(b,c,h,a[f++]);break;case 2:k=f++;k=kc(a,k);e(b,c,h,k,a[f++]);break;case 3:g(b,c,h,a[f++],a[f++],a[f++]);break;case 4:g(b,c,h,a[f++],a[f++],a[f++],a[f++]);break;default:throw Error(\"unexpected number of binary field arguments: \"+k);}}return b}\nvar oc=Symbol();function bc(a){var b=a[oc];if(!b){var c=pc(a);b=function(d,e){return qc(d,e,c)};a[oc]=b}return b}function cc(a,b){var c=a[oc];c||(c=function(d,e){return ac(d,e,b)},a[oc]=c);return c}var mc=Symbol();function rc(a,b){a.push(b)}function sc(a,b,c){a.push(b,c.W)}function tc(a,b,c,d){var e=bc(d),g=pc(d).P,f=c.W;a.push(b,function(h,k,l){return f(h,k,l,g,e)})}function uc(a,b,c,d,e,g){var f=cc(d,g),h=c.W;a.push(b,function(k,l,m){return h(k,l,m,d,f)})}\nfunction pc(a){var b=a[mc];if(b)return b;b=nc(a,a[mc]=[],rc,sc,tc,uc);lc in a&&mc in a&&(a.length=0);return b}var lc=Symbol();function vc(a,b){a[0]=b}function wc(a,b,c,d){var e=c.U;a[b]=d?function(g,f,h){return e(g,f,h,d)}:e}function xc(a,b,c,d,e){var g=c.U,f=fc(d),h=gc(d).P;a[b]=function(k,l,m){return g(k,l,m,h,f,e)}}function yc(a,b,c,d,e,g,f){var h=c.U,k=ec(d,e,g);a[b]=function(l,m,r){return h(l,m,r,d,k,f)}}\nfunction gc(a){var b=a[lc];if(b)return b;b=nc(a,a[lc]={},vc,wc,xc,yc);lc in a&&mc in a&&(a.length=0);return b}\nfunction hc(a,b,c){for(;tb(b)&&4!=b.i;){var d=b.l,e=c[d];if(!e){var g=c[0];g&&(g=g[d])&&(e=c[d]=jc(g))}if(!e||!e(b,a,d)){e=b;d=a;g=e.j;ub(e);var f=e;if(!f.ca){e=f.h.h-g;f.h.h=g;f=f.h;if(0==e)e=ib();else{g=ob(f,e);if(f.S&&f.m)e=f.i.subarray(g,g+e);else{f=f.i;var h=g;e=g+e;e=h===e?Oa():Qa?f.slice(h,e):new Uint8Array(f.subarray(h,e))}e=0==e.length?ib():new hb(e,Pa)}(g=d.R)?g.push(e):d.R=[e]}}}return a}\nfunction qc(a,b,c){for(var d=c.length,e=1==d%2,g=e?1:0;g<d;g+=2)(0,c[g+1])(b,a,c[g]);ac(a,b,e?c[0]:void 0)}function zc(a,b){return{U:a,W:b}}\nvar Z=zc(function(a,b,c){if(5!==a.i)return!1;a=a.h;var d=a.i,e=a.h,g=d[e];var f=d[e+1];var h=d[e+2];d=d[e+3];M(a,a.h+4);f=(g<<0|f<<8|h<<16|d<<24)>>>0;a=2*(f>>31)+1;g=f>>>23&255;f&=8388607;V(b,c,255==g?f?NaN:Infinity*a:0==g?a*Math.pow(2,-149)*f:a*Math.pow(2,g-150)*(f+Math.pow(2,23)));return!0},function(a,b,c){b=Lb(b,c);if(null!=b){N(a.h,8*c+5);a=a.h;var d=+b;0===d?0<1/d?H=K=0:(K=0,H=2147483648):isNaN(d)?(K=0,H=2147483647):(d=(c=0>d?-2147483648:0)?-d:d,3.4028234663852886E38<d?(K=0,H=(c|2139095040)>>>\n0):1.1754943508222875E-38>d?(d=Math.round(d/Math.pow(2,-149)),K=0,H=(c|d)>>>0):(b=Math.floor(Math.log(d)/Math.LN2),d*=Math.pow(2,-b),d=Math.round(8388608*d),16777216<=d&&++b,K=0,H=(c|b+127<<23|d&8388607)>>>0));c=H;a.h.push(c>>>0&255);a.h.push(c>>>8&255);a.h.push(c>>>16&255);a.h.push(c>>>24&255)}}),Ac=zc(function(a,b,c){if(0!==a.i)return!1;var d=a.h,e=0,g=a=0,f=d.i,h=d.h;do{var k=f[h++];e|=(k&127)<<g;g+=7}while(32>g&&k&128);32<g&&(a|=(k&127)>>4);for(g=3;32>g&&k&128;g+=7)k=f[h++],a|=(k&127)<<g;M(d,\nh);if(128>k){d=e>>>0;k=a>>>0;if(a=k&2147483648)d=~d+1>>>0,k=~k>>>0,0==d&&(k=k+1>>>0);d=4294967296*k+(d>>>0)}else throw Ya();V(b,c,a?-d:d);return!0},function(a,b,c){b=T(b,c);null!=b&&(\"string\"===typeof b&&Va(b),null!=b&&(N(a.h,8*c),\"number\"===typeof b?(a=a.h,Ra(b),rb(a,H,K)):(c=Va(b),rb(a.h,c.i,c.h))))}),Bc=zc(function(a,b,c){if(0!==a.i)return!1;V(b,c,nb(a.h));return!0},function(a,b,c){b=T(b,c);if(null!=b&&null!=b)if(N(a.h,8*c),a=a.h,c=b,0<=c)N(a,c);else{for(b=0;9>b;b++)a.h.push(c&127|128),c>>=7;a.h.push(1)}}),\nCc=zc(function(a,b,c){if(2!==a.i)return!1;var d=nb(a.h)>>>0;a=a.h;var e=ob(a,d);a=a.i;if(cb){var g=a,f;(f=bb)||(f=bb=new TextDecoder(\"utf-8\",{fatal:!0}));a=e+d;g=0===e&&a===g.length?g:g.subarray(e,a);try{var h=f.decode(g)}catch(r){if(void 0===ab){try{f.decode(new Uint8Array([128]))}catch(p){}try{f.decode(new Uint8Array([97])),ab=!0}catch(p){ab=!1}}!ab&&(bb=void 0);throw r;}}else{h=e;d=h+d;e=[];for(var k=null,l,m;h<d;)l=a[h++],128>l?e.push(l):224>l?h>=d?L():(m=a[h++],194>l||128!==(m&192)?(h--,L()):\ne.push((l&31)<<6|m&63)):240>l?h>=d-1?L():(m=a[h++],128!==(m&192)||224===l&&160>m||237===l&&160<=m||128!==((g=a[h++])&192)?(h--,L()):e.push((l&15)<<12|(m&63)<<6|g&63)):244>=l?h>=d-2?L():(m=a[h++],128!==(m&192)||0!==(l<<28)+(m-144)>>30||128!==((g=a[h++])&192)||128!==((f=a[h++])&192)?(h--,L()):(l=(l&7)<<18|(m&63)<<12|(g&63)<<6|f&63,l-=65536,e.push((l>>10&1023)+55296,(l&1023)+56320))):L(),8192<=e.length&&(k=$a(k,e),e.length=0);h=$a(k,e)}V(b,c,h);return!0},function(a,b,c){b=T(b,c);if(null!=b){var d=!1;\nd=void 0===d?!1:d;if(eb){if(d&&/(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])/.test(b))throw Error(\"Found an unpaired surrogate\");b=(db||(db=new TextEncoder)).encode(b)}else{for(var e=0,g=new Uint8Array(3*b.length),f=0;f<b.length;f++){var h=b.charCodeAt(f);if(128>h)g[e++]=h;else{if(2048>h)g[e++]=h>>6|192;else{if(55296<=h&&57343>=h){if(56319>=h&&f<b.length){var k=b.charCodeAt(++f);if(56320<=k&&57343>=k){h=1024*(h-55296)+k-56320+65536;g[e++]=h>>18|240;g[e++]=h>>12&63|128;\ng[e++]=h>>6&63|128;g[e++]=h&63|128;continue}else f--}if(d)throw Error(\"Found an unpaired surrogate\");h=65533}g[e++]=h>>12|224;g[e++]=h>>6&63|128}g[e++]=h&63|128}}b=e===g.length?g:g.subarray(0,e)}N(a.h,8*c+2);N(a.h,b.length);O(a,a.h.end());O(a,b)}}),Dc=zc(function(a,b,c,d,e){if(2!==a.i)return!1;b=Pb(b,c,d);c=a.h.j;d=nb(a.h)>>>0;var g=a.h.h+d,f=g-c;0>=f&&(a.h.j=g,e(b,a,void 0,void 0,void 0),f=g-a.h.h);if(f)throw Error(\"Message parsing ended unexpectedly. Expected to read \"+(d+\" bytes, instead read \"+\n(d-f)+\" bytes, either the data ended unexpectedly or the message misreported its own length\"));a.h.h=g;a.h.j=c;return!0},function(a,b,c,d,e){b=Nb(b,d,c);if(null!=b)for(d=0;d<b.length;d++){var g=a;N(g.h,8*c+2);var f=g.h.end();O(g,f);f.push(g.i);g=f;e(b[d],a);f=a;var h=g.pop();for(h=f.i+f.h.length()-h;127<h;)g.push(h&127|128),h>>>=7,f.i++;g.push(h);f.i++}});function Ec(a){return function(b,c){a:{if(vb.length){var d=vb.pop();d.setOptions(c);mb(d.h,b,c);b=d}else b=new sb(b,c);try{var e=gc(a);var g=hc(new e.P,b,e);break a}finally{e=b.h,e.i=null,e.m=!1,e.l=0,e.j=0,e.h=0,e.S=!1,b.l=-1,b.i=-1,100>vb.length&&vb.push(b)}g=void 0}return g}}function Fc(a){return function(){var b=new wb;qc(this,b,pc(a));O(b,b.h.end());for(var c=new Uint8Array(b.i),d=b.j,e=d.length,g=0,f=0;f<e;f++){var h=d[f];c.set(h,g);g+=h.length}b.j=[c];return c}};function Gc(a){Y.call(this,a)}na(Gc,Y);var Hc=[Gc,1,Bc,2,Z,3,Cc,4,Cc];Gc.prototype.l=Fc(Hc);function Ic(a){Y.call(this,a,-1,Jc)}na(Ic,Y);Ic.prototype.addClassification=function(a,b){Pb(this,1,Gc,a,b);return this};var Jc=[1],Kc=Ec([Ic,1,Dc,Hc]);function Lc(a){Y.call(this,a)}na(Lc,Y);var Mc=[Lc,1,Z,2,Z,3,Z,4,Z,5,Z];Lc.prototype.l=Fc(Mc);function Nc(a){Y.call(this,a,-1,Oc)}na(Nc,Y);var Oc=[1],Pc=Ec([Nc,1,Dc,Mc]);function Qc(a){Y.call(this,a)}na(Qc,Y);var Rc=[Qc,1,Z,2,Z,3,Z,4,Z,5,Z,6,Ac],Sc=Ec(Rc);Qc.prototype.l=Fc(Rc);function Tc(a,b,c){c=a.createShader(0===c?a.VERTEX_SHADER:a.FRAGMENT_SHADER);a.shaderSource(c,b);a.compileShader(c);if(!a.getShaderParameter(c,a.COMPILE_STATUS))throw Error(\"Could not compile WebGL shader.\\n\\n\"+a.getShaderInfoLog(c));return c};function Uc(a){return Nb(a,Gc,1).map(function(b){var c=T(b,1);return{index:null==c?0:c,qa:X(b,2),label:null!=T(b,3)?Qb(T(b,3),\"\"):void 0,displayName:null!=T(b,4)?Qb(T(b,4),\"\"):void 0}})};function Vc(a){return{x:X(a,1),y:X(a,2),z:X(a,3),visibility:null!=Lb(a,4)?X(a,4):void 0}}function Wc(a){return Nb(Pc(a),Lc,1).map(Vc)};function Xc(a,b){this.i=a;this.h=b;this.m=0}\nfunction Yc(a,b,c){Zc(a,b);if(\"function\"===typeof a.h.canvas.transferToImageBitmap)return Promise.resolve(a.h.canvas.transferToImageBitmap());if(c)return Promise.resolve(a.h.canvas);if(\"function\"===typeof createImageBitmap)return createImageBitmap(a.h.canvas);void 0===a.j&&(a.j=document.createElement(\"canvas\"));return new Promise(function(d){a.j.height=a.h.canvas.height;a.j.width=a.h.canvas.width;a.j.getContext(\"2d\",{}).drawImage(a.h.canvas,0,0,a.h.canvas.width,a.h.canvas.height);d(a.j)})}\nfunction Zc(a,b){var c=a.h;if(void 0===a.s){var d=Tc(c,\"\\n  attribute vec2 aVertex;\\n  attribute vec2 aTex;\\n  varying vec2 vTex;\\n  void main(void) {\\n    gl_Position = vec4(aVertex, 0.0, 1.0);\\n    vTex = aTex;\\n  }\",0),e=Tc(c,\"\\n  precision mediump float;\\n  varying vec2 vTex;\\n  uniform sampler2D sampler0;\\n  void main(){\\n    gl_FragColor = texture2D(sampler0, vTex);\\n  }\",1),g=c.createProgram();c.attachShader(g,d);c.attachShader(g,e);c.linkProgram(g);if(!c.getProgramParameter(g,c.LINK_STATUS))throw Error(\"Could not compile WebGL program.\\n\\n\"+\nc.getProgramInfoLog(g));d=a.s=g;c.useProgram(d);e=c.getUniformLocation(d,\"sampler0\");a.l={O:c.getAttribLocation(d,\"aVertex\"),N:c.getAttribLocation(d,\"aTex\"),ya:e};a.v=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,a.v);c.enableVertexAttribArray(a.l.O);c.vertexAttribPointer(a.l.O,2,c.FLOAT,!1,0,0);c.bufferData(c.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),c.STATIC_DRAW);c.bindBuffer(c.ARRAY_BUFFER,null);a.u=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,a.u);c.enableVertexAttribArray(a.l.N);c.vertexAttribPointer(a.l.N,\n2,c.FLOAT,!1,0,0);c.bufferData(c.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),c.STATIC_DRAW);c.bindBuffer(c.ARRAY_BUFFER,null);c.uniform1i(e,0)}d=a.l;c.useProgram(a.s);c.canvas.width=b.width;c.canvas.height=b.height;c.viewport(0,0,b.width,b.height);c.activeTexture(c.TEXTURE0);a.i.bindTexture2d(b.glName);c.enableVertexAttribArray(d.O);c.bindBuffer(c.ARRAY_BUFFER,a.v);c.vertexAttribPointer(d.O,2,c.FLOAT,!1,0,0);c.enableVertexAttribArray(d.N);c.bindBuffer(c.ARRAY_BUFFER,a.u);c.vertexAttribPointer(d.N,\n2,c.FLOAT,!1,0,0);c.bindFramebuffer(c.DRAW_FRAMEBUFFER?c.DRAW_FRAMEBUFFER:c.FRAMEBUFFER,null);c.clearColor(0,0,0,0);c.clear(c.COLOR_BUFFER_BIT);c.colorMask(!0,!0,!0,!0);c.drawArrays(c.TRIANGLE_FAN,0,4);c.disableVertexAttribArray(d.O);c.disableVertexAttribArray(d.N);c.bindBuffer(c.ARRAY_BUFFER,null);a.i.bindTexture2d(0)}function $c(a){this.h=a};var ad=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function bd(a,b){return b+a}function cd(a,b){window[a]=b}function dd(a){var b=document.createElement(\"script\");b.setAttribute(\"src\",a);b.setAttribute(\"crossorigin\",\"anonymous\");return new Promise(function(c){b.addEventListener(\"load\",function(){c()},!1);b.addEventListener(\"error\",function(){c()},!1);document.body.appendChild(b)})}\nfunction ed(){return E(function(a){switch(a.h){case 1:return a.s=2,D(a,WebAssembly.instantiate(ad),4);case 4:a.h=3;a.s=0;break;case 2:return a.s=0,a.l=null,a.return(!1);case 3:return a.return(!0)}})}\nfunction fd(a){this.h=a;this.listeners={};this.l={};this.L={};this.s={};this.v={};this.M=this.u=this.ga=!0;this.I=Promise.resolve();this.fa=\"\";this.D={};this.locateFile=a&&a.locateFile||bd;if(\"object\"===typeof window)var b=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf(\"/\"))+\"/\";else if(\"undefined\"!==typeof location)b=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf(\"/\"))+\"/\";else throw Error(\"solutions can only be loaded on a web page or in a web worker\");\nthis.ha=b;if(a.options){b=A(Object.keys(a.options));for(var c=b.next();!c.done;c=b.next()){c=c.value;var d=a.options[c].default;void 0!==d&&(this.l[c]=\"function\"===typeof d?d():d)}}}x=fd.prototype;x.close=function(){this.j&&this.j.delete();return Promise.resolve()};\nfunction gd(a){var b,c,d,e,g,f,h,k,l,m,r;return E(function(p){switch(p.h){case 1:if(!a.ga)return p.return();b=void 0===a.h.files?[]:\"function\"===typeof a.h.files?a.h.files(a.l):a.h.files;return D(p,ed(),2);case 2:c=p.i;if(\"object\"===typeof window)return cd(\"createMediapipeSolutionsWasm\",{locateFile:a.locateFile}),cd(\"createMediapipeSolutionsPackedAssets\",{locateFile:a.locateFile}),f=b.filter(function(n){return void 0!==n.data}),h=b.filter(function(n){return void 0===n.data}),k=Promise.all(f.map(function(n){var q=\nhd(a,n.url);if(void 0!==n.path){var t=n.path;q=q.then(function(w){a.overrideFile(t,w);return Promise.resolve(w)})}return q})),l=Promise.all(h.map(function(n){return void 0===n.simd||n.simd&&c||!n.simd&&!c?dd(a.locateFile(n.url,a.ha)):Promise.resolve()})).then(function(){var n,q,t;return E(function(w){if(1==w.h)return n=window.createMediapipeSolutionsWasm,q=window.createMediapipeSolutionsPackedAssets,t=a,D(w,n(q),2);t.i=w.i;w.h=0})}),m=function(){return E(function(n){a.h.graph&&a.h.graph.url?n=D(n,\nhd(a,a.h.graph.url),0):(n.h=0,n=void 0);return n})}(),D(p,Promise.all([l,k,m]),7);if(\"function\"!==typeof importScripts)throw Error(\"solutions can only be loaded on a web page or in a web worker\");d=b.filter(function(n){return void 0===n.simd||n.simd&&c||!n.simd&&!c}).map(function(n){return a.locateFile(n.url,a.ha)});importScripts.apply(null,ea(d));e=a;return D(p,createMediapipeSolutionsWasm(Module),6);case 6:e.i=p.i;a.m=new OffscreenCanvas(1,1);a.i.canvas=a.m;g=a.i.GL.createContext(a.m,{antialias:!1,\nalpha:!1,va:\"undefined\"!==typeof WebGL2RenderingContext?2:1});a.i.GL.makeContextCurrent(g);p.h=4;break;case 7:a.m=document.createElement(\"canvas\");r=a.m.getContext(\"webgl2\",{});if(!r&&(r=a.m.getContext(\"webgl\",{}),!r))return alert(\"Failed to create WebGL canvas context when passing video frame.\"),p.return();a.K=r;a.i.canvas=a.m;a.i.createContext(a.m,!0,!0,{});case 4:a.j=new a.i.SolutionWasm,a.ga=!1,p.h=0}})}\nfunction id(a){var b,c,d,e,g,f,h,k;return E(function(l){if(1==l.h){if(a.h.graph&&a.h.graph.url&&a.fa===a.h.graph.url)return l.return();a.u=!0;if(!a.h.graph||!a.h.graph.url){l.h=2;return}a.fa=a.h.graph.url;return D(l,hd(a,a.h.graph.url),3)}2!=l.h&&(b=l.i,a.j.loadGraph(b));c=A(Object.keys(a.D));for(d=c.next();!d.done;d=c.next())e=d.value,a.j.overrideFile(e,a.D[e]);a.D={};if(a.h.listeners)for(g=A(a.h.listeners),f=g.next();!f.done;f=g.next())h=f.value,jd(a,h);k=a.l;a.l={};a.setOptions(k);l.h=0})}\nx.reset=function(){var a=this;return E(function(b){a.j&&(a.j.reset(),a.s={},a.v={});b.h=0})};\nx.setOptions=function(a,b){var c=this;if(b=b||this.h.options){for(var d=[],e=[],g={},f=A(Object.keys(a)),h=f.next();!h.done;g={X:g.X,Y:g.Y},h=f.next())if(h=h.value,!(h in this.l&&this.l[h]===a[h])){this.l[h]=a[h];var k=b[h];void 0!==k&&(k.onChange&&(g.X=k.onChange,g.Y=a[h],d.push(function(l){return function(){var m;return E(function(r){if(1==r.h)return D(r,l.X(l.Y),2);m=r.i;!0===m&&(c.u=!0);r.h=0})}}(g))),k.graphOptionXref&&(h=Object.assign({},{calculatorName:\"\",calculatorIndex:0},k.graphOptionXref,\n{valueNumber:1===k.type?a[h]:0,valueBoolean:0===k.type?a[h]:!1,valueString:2===k.type?a[h]:\"\"}),e.push(h)))}if(0!==d.length||0!==e.length)this.u=!0,this.H=(void 0===this.H?[]:this.H).concat(e),this.F=(void 0===this.F?[]:this.F).concat(d)}};\nfunction kd(a){var b,c,d,e,g,f,h;return E(function(k){switch(k.h){case 1:if(!a.u)return k.return();if(!a.F){k.h=2;break}b=A(a.F);c=b.next();case 3:if(c.done){k.h=5;break}d=c.value;return D(k,d(),4);case 4:c=b.next();k.h=3;break;case 5:a.F=void 0;case 2:if(a.H){e=new a.i.GraphOptionChangeRequestList;g=A(a.H);for(f=g.next();!f.done;f=g.next())h=f.value,e.push_back(h);a.j.changeOptions(e);e.delete();a.H=void 0}a.u=!1;k.h=0}})}\nx.initialize=function(){var a=this;return E(function(b){return 1==b.h?D(b,gd(a),2):3!=b.h?D(b,id(a),3):D(b,kd(a),0)})};function hd(a,b){var c,d;return E(function(e){if(b in a.L)return e.return(a.L[b]);c=a.locateFile(b,\"\");d=fetch(c).then(function(g){return g.arrayBuffer()});a.L[b]=d;return e.return(d)})}x.overrideFile=function(a,b){this.j?this.j.overrideFile(a,b):this.D[a]=b};x.clearOverriddenFiles=function(){this.D={};this.j&&this.j.clearOverriddenFiles()};\nx.send=function(a,b){var c=this,d,e,g,f,h,k,l,m,r;return E(function(p){switch(p.h){case 1:if(!c.h.inputs)return p.return();d=1E3*(void 0===b||null===b?performance.now():b);return D(p,c.I,2);case 2:return D(p,c.initialize(),3);case 3:e=new c.i.PacketDataList;g=A(Object.keys(a));for(f=g.next();!f.done;f=g.next())if(h=f.value,k=c.h.inputs[h]){a:{var n=a[h];switch(k.type){case \"video\":var q=c.s[k.stream];q||(q=new Xc(c.i,c.K),c.s[k.stream]=q);0===q.m&&(q.m=q.i.createTexture());if(\"undefined\"!==typeof HTMLVideoElement&&\nn instanceof HTMLVideoElement){var t=n.videoWidth;var w=n.videoHeight}else\"undefined\"!==typeof HTMLImageElement&&n instanceof HTMLImageElement?(t=n.naturalWidth,w=n.naturalHeight):(t=n.width,w=n.height);w={glName:q.m,width:t,height:w};t=q.h;t.canvas.width=w.width;t.canvas.height=w.height;t.activeTexture(t.TEXTURE0);q.i.bindTexture2d(q.m);t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,n);q.i.bindTexture2d(0);q=w;break a;case \"detections\":q=c.s[k.stream];q||(q=new $c(c.i),c.s[k.stream]=q);\nq.data||(q.data=new q.h.DetectionListData);q.data.reset(n.length);for(w=0;w<n.length;++w){t=n[w];var v=q.data,B=v.setBoundingBox,J=w;var I=t.la;var u=new Qc;W(u,1,I.sa);W(u,2,I.ta);W(u,3,I.height);W(u,4,I.width);W(u,5,I.rotation);V(u,6,I.pa);I=u.l();B.call(v,J,I);if(t.ea)for(v=0;v<t.ea.length;++v){u=t.ea[v];B=q.data;J=B.addNormalizedLandmark;I=w;u=Object.assign({},u,{visibility:u.visibility?u.visibility:0});var C=new Lc;W(C,1,u.x);W(C,2,u.y);W(C,3,u.z);u.visibility&&W(C,4,u.visibility);u=C.l();J.call(B,\nI,u)}if(t.ba)for(v=0;v<t.ba.length;++v)B=q.data,J=B.addClassification,I=w,u=t.ba[v],C=new Gc,W(C,2,u.qa),u.index&&V(C,1,u.index),u.label&&V(C,3,u.label),u.displayName&&V(C,4,u.displayName),u=C.l(),J.call(B,I,u)}q=q.data;break a;default:q={}}}l=q;m=k.stream;switch(k.type){case \"video\":e.pushTexture2d(Object.assign({},l,{stream:m,timestamp:d}));break;case \"detections\":r=l;r.stream=m;r.timestamp=d;e.pushDetectionList(r);break;default:throw Error(\"Unknown input config type: '\"+k.type+\"'\");}}c.j.send(e);\nreturn D(p,c.I,4);case 4:e.delete(),p.h=0}})};\nfunction ld(a,b,c){var d,e,g,f,h,k,l,m,r,p,n,q,t,w;return E(function(v){switch(v.h){case 1:if(!c)return v.return(b);d={};e=0;g=A(Object.keys(c));for(f=g.next();!f.done;f=g.next())h=f.value,k=c[h],\"string\"!==typeof k&&\"texture\"===k.type&&void 0!==b[k.stream]&&++e;1<e&&(a.M=!1);l=A(Object.keys(c));f=l.next();case 2:if(f.done){v.h=4;break}m=f.value;r=c[m];if(\"string\"===typeof r)return t=d,w=m,D(v,md(a,m,b[r]),14);p=b[r.stream];if(\"detection_list\"===r.type){if(p){var B=p.getRectList();for(var J=p.getLandmarksList(),\nI=p.getClassificationsList(),u=[],C=0;C<B.size();++C){var U=Sc(B.get(C)),pd=X(U,1),qd=X(U,2),rd=X(U,3),sd=X(U,4),td=X(U,5,0),za=void 0;za=void 0===za?0:za;U={la:{sa:pd,ta:qd,height:rd,width:sd,rotation:td,pa:Qb(T(U,6),za)},ea:Wc(J.get(C)),ba:Uc(Kc(I.get(C)))};u.push(U)}B=u}else B=[];d[m]=B;v.h=7;break}if(\"proto_list\"===r.type){if(p){B=Array(p.size());for(J=0;J<p.size();J++)B[J]=p.get(J);p.delete()}else B=[];d[m]=B;v.h=7;break}if(void 0===p){v.h=3;break}if(\"float_list\"===r.type){d[m]=p;v.h=7;break}if(\"proto\"===\nr.type){d[m]=p;v.h=7;break}if(\"texture\"!==r.type)throw Error(\"Unknown output config type: '\"+r.type+\"'\");n=a.v[m];n||(n=new Xc(a.i,a.K),a.v[m]=n);return D(v,Yc(n,p,a.M),13);case 13:q=v.i,d[m]=q;case 7:r.transform&&d[m]&&(d[m]=r.transform(d[m]));v.h=3;break;case 14:t[w]=v.i;case 3:f=l.next();v.h=2;break;case 4:return v.return(d)}})}\nfunction md(a,b,c){var d;return E(function(e){return\"number\"===typeof c||c instanceof Uint8Array||c instanceof a.i.Uint8BlobList?e.return(c):c instanceof a.i.Texture2dDataOut?(d=a.v[b],d||(d=new Xc(a.i,a.K),a.v[b]=d),e.return(Yc(d,c,a.M))):e.return(void 0)})}\nfunction jd(a,b){for(var c=b.name||\"$\",d=[].concat(ea(b.wants)),e=new a.i.StringList,g=A(b.wants),f=g.next();!f.done;f=g.next())e.push_back(f.value);g=a.i.PacketListener.implement({onResults:function(h){for(var k={},l=0;l<b.wants.length;++l)k[d[l]]=h.get(l);var m=a.listeners[c];m&&(a.I=ld(a,k,b.outs).then(function(r){r=m(r);for(var p=0;p<b.wants.length;++p){var n=k[d[p]];\"object\"===typeof n&&n.hasOwnProperty&&n.hasOwnProperty(\"delete\")&&n.delete()}r&&(a.I=r)}))}});a.j.attachMultiListener(e,g);e.delete()}\nx.onResults=function(a,b){this.listeners[b||\"$\"]=a};G(\"Solution\",fd);G(\"OptionType\",{BOOL:0,NUMBER:1,ua:2,0:\"BOOL\",1:\"NUMBER\",2:\"STRING\"});function nd(a){void 0===a&&(a=0);switch(a){case 1:return\"pose_landmark_full.tflite\";case 2:return\"pose_landmark_heavy.tflite\";default:return\"pose_landmark_lite.tflite\"}}\nfunction od(a){var b=this;a=a||{};this.h=new fd({locateFile:a.locateFile,files:function(c){return[{url:\"pose_solution_packed_assets_loader.js\"},{simd:!1,url:\"pose_solution_wasm_bin.js\"},{simd:!0,url:\"pose_solution_simd_wasm_bin.js\"},{data:!0,url:nd(c.modelComplexity)}]},graph:{url:\"pose_web.binarypb\"},listeners:[{wants:[\"pose_landmarks\",\"world_landmarks\",\"segmentation_mask\",\"image_transformed\"],outs:{image:{type:\"texture\",stream:\"image_transformed\"},poseLandmarks:{type:\"proto\",stream:\"pose_landmarks\",\ntransform:Wc},poseWorldLandmarks:{type:\"proto\",stream:\"world_landmarks\",transform:Wc},segmentationMask:{type:\"texture\",stream:\"segmentation_mask\"}}}],inputs:{image:{type:\"video\",stream:\"input_frames_gpu\"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:\"InferenceCalculator\",fieldName:\"use_cpu_inference\"},default:\"object\"!==typeof window||void 0===window.navigator?!1:\"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod\".split(\";\").includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\n\"ontouchend\"in document},selfieMode:{type:0,graphOptionXref:{calculatorType:\"GlScalerCalculator\",calculatorIndex:1,fieldName:\"flip_horizontal\"}},modelComplexity:{type:1,graphOptionXref:{calculatorType:\"ConstantSidePacketCalculator\",calculatorName:\"ConstantSidePacketCalculatorModelComplexity\",fieldName:\"int_value\"},onChange:function(c){var d,e,g;return E(function(f){if(1==f.h)return d=nd(c),e=\"third_party/mediapipe/modules/pose_landmark/\"+d,D(f,hd(b.h,d),2);g=f.i;b.h.overrideFile(e,g);return f.return(!0)})}},\nsmoothLandmarks:{type:0,graphOptionXref:{calculatorType:\"ConstantSidePacketCalculator\",calculatorName:\"ConstantSidePacketCalculatorSmoothLandmarks\",fieldName:\"bool_value\"}},enableSegmentation:{type:0,graphOptionXref:{calculatorType:\"ConstantSidePacketCalculator\",calculatorName:\"ConstantSidePacketCalculatorEnableSegmentation\",fieldName:\"bool_value\"}},smoothSegmentation:{type:0,graphOptionXref:{calculatorType:\"ConstantSidePacketCalculator\",calculatorName:\"ConstantSidePacketCalculatorSmoothSegmentation\",\nfieldName:\"bool_value\"}},minDetectionConfidence:{type:1,graphOptionXref:{calculatorType:\"TensorsToDetectionsCalculator\",calculatorName:\"poselandmarkgpu__posedetectiongpu__TensorsToDetectionsCalculator\",fieldName:\"min_score_thresh\"}},minTrackingConfidence:{type:1,graphOptionXref:{calculatorType:\"ThresholdingCalculator\",calculatorName:\"poselandmarkgpu__poselandmarkbyroigpu__tensorstoposelandmarksandsegmentation__ThresholdingCalculator\",fieldName:\"threshold\"}}}})}x=od.prototype;x.reset=function(){this.h.reset()};\nx.close=function(){this.h.close();return Promise.resolve()};x.onResults=function(a){this.h.onResults(a)};x.initialize=function(){var a=this;return E(function(b){return D(b,a.h.initialize(),0)})};x.send=function(a,b){var c=this;return E(function(d){return D(d,c.h.send(a,b),0)})};x.setOptions=function(a){this.h.setOptions(a)};G(\"Pose\",od);\nG(\"POSE_CONNECTIONS\",[[0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]]);\nG(\"POSE_LANDMARKS\",{NOSE:0,LEFT_EYE_INNER:1,LEFT_EYE:2,LEFT_EYE_OUTER:3,RIGHT_EYE_INNER:4,RIGHT_EYE:5,RIGHT_EYE_OUTER:6,LEFT_EAR:7,RIGHT_EAR:8,LEFT_RIGHT:9,RIGHT_LEFT:10,LEFT_SHOULDER:11,RIGHT_SHOULDER:12,LEFT_ELBOW:13,RIGHT_ELBOW:14,LEFT_WRIST:15,RIGHT_WRIST:16,LEFT_PINKY:17,RIGHT_PINKY:18,LEFT_INDEX:19,RIGHT_INDEX:20,LEFT_THUMB:21,RIGHT_THUMB:22,LEFT_HIP:23,RIGHT_HIP:24,LEFT_KNEE:25,RIGHT_KNEE:26,LEFT_ANKLE:27,RIGHT_ANKLE:28,LEFT_HEEL:29,RIGHT_HEEL:30,LEFT_FOOT_INDEX:31,RIGHT_FOOT_INDEX:32});\nG(\"POSE_LANDMARKS_LEFT\",{LEFT_EYE_INNER:1,LEFT_EYE:2,LEFT_EYE_OUTER:3,LEFT_EAR:7,LEFT_RIGHT:9,LEFT_SHOULDER:11,LEFT_ELBOW:13,LEFT_WRIST:15,LEFT_PINKY:17,LEFT_INDEX:19,LEFT_THUMB:21,LEFT_HIP:23,LEFT_KNEE:25,LEFT_ANKLE:27,LEFT_HEEL:29,LEFT_FOOT_INDEX:31});\nG(\"POSE_LANDMARKS_RIGHT\",{RIGHT_EYE_INNER:4,RIGHT_EYE:5,RIGHT_EYE_OUTER:6,RIGHT_EAR:8,RIGHT_LEFT:10,RIGHT_SHOULDER:12,RIGHT_ELBOW:14,RIGHT_WRIST:16,RIGHT_PINKY:18,RIGHT_INDEX:20,RIGHT_THUMB:22,RIGHT_HIP:24,RIGHT_KNEE:26,RIGHT_ANKLE:28,RIGHT_HEEL:30,RIGHT_FOOT_INDEX:32});G(\"POSE_LANDMARKS_NEUTRAL\",{NOSE:0});G(\"VERSION\",\"0.5.**********\");}).call(this);\n", "/**\n    * @license\n    * Copyright 2023 Google LLC. All Rights Reserved.\n    * Licensed under the Apache License, Version 2.0 (the \"License\");\n    * you may not use this file except in compliance with the License.\n    * You may obtain a copy of the License at\n    *\n    * http://www.apache.org/licenses/LICENSE-2.0\n    *\n    * Unless required by applicable law or agreed to in writing, software\n    * distributed under the License is distributed on an \"AS IS\" BASIS,\n    * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n    * See the License for the specific language governing permissions and\n    * limitations under the License.\n    * =============================================================================\n    */\nimport{Pose as t}from\"@mediapipe/pose\";import{Tensor as e,browser as n,util as i,tidy as r,add as o,mul as a,tensor2d as s,image as u,expandDims as h,cast as l,slice as c,squeeze as p,dispose as f,tensor1d as d,getBackend as m,engine as g,sub as y,square as v,minimum as x,backend as w,div as k,exp as b,concat as M,reshape as S,clipByValue as T,sigmoid as P,pad as F,mirrorPad as _,env as O,zeros as I,scalar as A,argMax as z}from\"@tensorflow/tfjs-core\";import{loadGraphModel as C}from\"@tensorflow/tfjs-converter\";import{webgpu_util as E,WebGPUBackend as R}from\"@tensorflow/tfjs-backend-webgpu\";var L=function(t,e){return(L=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function V(t,e){if(\"function\"!=typeof e&&null!==e)throw new TypeError(\"Class extends value \"+String(e)+\" is not a constructor or null\");function n(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var B=function(){return(B=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function N(t,e,n,i){return new(n||(n=Promise))((function(r,o){function a(t){try{u(i.next(t))}catch(t){o(t)}}function s(t){try{u(i.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((i=i.apply(t,e||[])).next())}))}function D(t,e){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},\"function\"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError(\"Generator is already executing.\");for(;a;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function K(t,e,n){if(n||2===arguments.length)for(var i,r=0,o=e.length;r<o;r++)!i&&r in e||(i||(i=Array.prototype.slice.call(e,0,r)),i[r]=e[r]);return t.concat(i||Array.prototype.slice.call(e))}var U=[\"nose\",\"left_eye\",\"right_eye\",\"left_ear\",\"right_ear\",\"left_shoulder\",\"right_shoulder\",\"left_elbow\",\"right_elbow\",\"left_wrist\",\"right_wrist\",\"left_hip\",\"right_hip\",\"left_knee\",\"right_knee\",\"left_ankle\",\"right_ankle\"],j=[\"nose\",\"left_eye_inner\",\"left_eye\",\"left_eye_outer\",\"right_eye_inner\",\"right_eye\",\"right_eye_outer\",\"left_ear\",\"right_ear\",\"mouth_left\",\"mouth_right\",\"left_shoulder\",\"right_shoulder\",\"left_elbow\",\"right_elbow\",\"left_wrist\",\"right_wrist\",\"left_pinky\",\"right_pinky\",\"left_index\",\"right_index\",\"left_thumb\",\"right_thumb\",\"left_hip\",\"right_hip\",\"left_knee\",\"right_knee\",\"left_ankle\",\"right_ankle\",\"left_heel\",\"right_heel\",\"left_foot_index\",\"right_foot_index\"],H={left:[1,2,3,7,9,11,13,15,17,19,21,23,25,27,29,31],right:[4,5,6,8,10,12,14,16,18,20,22,24,26,28,30,32],middle:[0]},q={left:[1,3,5,7,9,11,13,15],right:[2,4,6,8,10,12,14,16],middle:[0]},X=[[0,1],[0,2],[1,3],[2,4],[5,6],[5,7],[5,11],[6,8],[6,12],[7,9],[8,10],[11,12],[11,13],[12,14],[13,15],[14,16]],Y=[[0,1],[0,4],[1,2],[2,3],[3,7],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[11,23],[12,14],[14,16],[12,24],[13,15],[15,17],[16,18],[16,20],[15,17],[15,19],[15,21],[16,22],[17,19],[18,20],[23,25],[23,24],[24,26],[25,27],[26,28],[27,29],[28,30],[27,31],[28,32],[29,31],[30,32]];function W(t){return t instanceof SVGAnimatedLength?t.baseVal.value:t}function G(t){return N(this,void 0,void 0,(function(){var i,r;return D(this,(function(o){switch(o.label){case 0:return i=document.createElement(\"canvas\"),t instanceof e?[4,n.toPixels(t,i)]:[3,2];case 1:return o.sent(),[3,3];case 2:i.width=W(t.width),i.height=W(t.height),r=i.getContext(\"2d\"),t instanceof ImageData?r.putImageData(t,0,0):r.drawImage(t,0,0),o.label=3;case 3:return[2,i]}}))}))}function Q(t){return N(this,void 0,void 0,(function(){var i,r,o,a,s,u;return D(this,(function(h){switch(h.label){case 0:return t instanceof e?(i=t.shape.slice(0,2),r=i[0],o=i[1],a=ImageData.bind,[4,n.toPixels(t)]):[3,2];case 1:return[2,new(a.apply(ImageData,[void 0,h.sent(),o,r]))];case 2:return s=document.createElement(\"canvas\"),u=s.getContext(\"2d\"),s.width=W(t.width),s.height=W(t.height),u.drawImage(t,0,0),[2,u.getImageData(0,0,s.width,s.height)]}}))}))}function Z(t){return N(this,void 0,void 0,(function(){var e,i;return D(this,(function(r){switch(r.label){case 0:return t instanceof SVGImageElement||t instanceof OffscreenCanvas?[4,G(t)]:[3,2];case 1:return i=r.sent(),[3,3];case 2:i=t,r.label=3;case 3:return e=i,[2,n.fromPixels(e,4)]}}))}))}function $(t){if(t<0||t>=256)throw new Error(\"Mask value must be in range [0, 255] but got \".concat(t));if(!Number.isInteger(t))throw new Error(\"Mask value must be an integer but got \".concat(t))}var J={runtime:\"mediapipe\",enableSmoothing:!0,enableSegmentation:!1,smoothSegmentation:!0,modelType:\"full\"};var tt=function(){function t(t){this.mask=t}return t.prototype.toCanvasImageSource=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,this.mask]}))}))},t.prototype.toImageData=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,Q(this.mask)]}))}))},t.prototype.toTensor=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,Z(this.mask)]}))}))},t.prototype.getUnderlyingType=function(){return\"canvasimagesource\"},t}();function et(t){return $(t),\"person\"}var nt=function(){function i(e){var n,i=this;switch(this.width=0,this.height=0,this.selfieMode=!1,this.poseSolution=new t({locateFile:function(t,n){if(e.solutionPath){var i=e.solutionPath.replace(/\\/+$/,\"\");return\"\".concat(i,\"/\").concat(t)}return\"\".concat(n,\"/\").concat(t)}}),e.modelType){case\"lite\":n=0;break;case\"heavy\":n=2;break;case\"full\":default:n=1}this.poseSolution.setOptions({modelComplexity:n,smoothLandmarks:e.enableSmoothing,enableSegmentation:e.enableSegmentation,smoothSegmentation:e.smoothSegmentation,selfieMode:this.selfieMode}),this.poseSolution.onResults((function(t){if(i.height=t.image.height,i.width=t.image.width,null==t.poseLandmarks)i.poses=[];else{var e=i.translateOutput(t.poseLandmarks,t.poseWorldLandmarks);t.segmentationMask&&(e.segmentation={maskValueToLabel:et,mask:new tt(t.segmentationMask)}),i.poses=[e]}}))}return i.prototype.translateOutput=function(t,e){var n=this,i={keypoints:t.map((function(t,e){return{x:t.x*n.width,y:t.y*n.height,z:t.z,score:t.visibility,name:j[e]}}))};return null!=e&&(i.keypoints3D=e.map((function(t,e){return{x:t.x,y:t.y,z:t.z,score:t.visibility,name:j[e]}}))),i},i.prototype.estimatePoses=function(t,i,r){return N(this,void 0,void 0,(function(){var o,a;return D(this,(function(s){switch(s.label){case 0:return i&&i.flipHorizontal&&i.flipHorizontal!==this.selfieMode&&(this.selfieMode=i.flipHorizontal,this.poseSolution.setOptions({selfieMode:this.selfieMode})),t instanceof e?(a=ImageData.bind,[4,n.toPixels(t)]):[3,2];case 1:return o=new(a.apply(ImageData,[void 0,s.sent(),t.shape[1],t.shape[0]])),[3,3];case 2:o=t,s.label=3;case 3:return t=o,[4,this.poseSolution.send({image:t},r)];case 4:return s.sent(),[2,this.poses]}}))}))},i.prototype.dispose=function(){this.poseSolution.close()},i.prototype.reset=function(){this.poseSolution.reset()},i.prototype.initialize=function(){return this.poseSolution.initialize()},i}();function it(t){return N(this,void 0,void 0,(function(){var e,n;return D(this,(function(i){switch(i.label){case 0:return e=function(t){if(null==t)return B({},J);var e=B({},t);return e.runtime=\"mediapipe\",null==e.enableSegmentation&&(e.enableSegmentation=J.enableSegmentation),null==e.enableSmoothing&&(e.enableSmoothing=J.enableSmoothing),null==e.smoothSegmentation&&(e.smoothSegmentation=J.smoothSegmentation),null==e.modelType&&(e.modelType=J.modelType),e}(t),[4,(n=new nt(e)).initialize()];case 1:return i.sent(),[2,n]}}))}))}function rt(t){return t instanceof e?{height:t.shape[0],width:t.shape[1]}:{height:t.height,width:t.width}}function ot(t){return t-2*Math.PI*Math.floor((t+Math.PI)/(2*Math.PI))}function at(t){return t instanceof e?t:n.fromPixels(t)}function st(t,e,n){return ut(n,\"inputResolution\"),[1/n.width*t[0][0]*e.width,1/n.height*t[0][1]*e.width,t[0][3]*e.width,1/n.width*t[1][0]*e.height,1/n.height*t[1][1]*e.height,t[1][3]*e.height,0,0]}function ut(t,e){i.assert(0!==t.width,(function(){return\"\".concat(e,\" width cannot be 0.\")})),i.assert(0!==t.height,(function(){return\"\".concat(e,\" height cannot be 0.\")}))}function ht(t,e,n){var i=n.rotationVectorStartKeypointIndex,r=n.rotationVectorEndKeypointIndex,o=t.locationData,a=o.relativeKeypoints[i].x*e.width,s=o.relativeKeypoints[i].y*e.height,u=o.relativeKeypoints[r].x*e.width,h=o.relativeKeypoints[r].y*e.height,l=2*Math.sqrt((u-a)*(u-a)+(h-s)*(h-s)),c=function(t,e,n){var i,r=t.locationData,o=n.rotationVectorStartKeypointIndex,a=n.rotationVectorEndKeypointIndex;i=n.rotationVectorTargetAngle?n.rotationVectorTargetAngle:Math.PI*n.rotationVectorTargetAngleDegree/180;var s=r.relativeKeypoints[o].x*e.width,u=r.relativeKeypoints[o].y*e.height,h=r.relativeKeypoints[a].x*e.width,l=r.relativeKeypoints[a].y*e.height;return ot(i-Math.atan2(-(l-u),h-s))}(t,e,n);return{xCenter:a/e.width,yCenter:s/e.height,width:l/e.width,height:l/e.height,rotation:c}}function lt(t){if(16!==t.length)throw new Error(\"Array length must be 16 but got \".concat(t.length));return[[t[0],t[1],t[2],t[3]],[t[4],t[5],t[6],t[7]],[t[8],t[9],t[10],t[11]],[t[12],t[13],t[14],t[15]]]}function ct(t,e,n,i,r,o,a){return t[e][r]*(t[n][o]*t[i][a]-t[n][a]*t[i][o])}function pt(t,e,n){var i=(e+1)%4,r=(e+2)%4,o=(e+3)%4,a=(n+1)%4,s=(n+2)%4,u=(n+3)%4;return ct(t,i,r,o,a,s,u)+ct(t,r,o,i,a,s,u)+ct(t,o,i,r,a,s,u)}function ft(t,e,n){void 0===n&&(n={ignoreRotation:!1});for(var i=[],r=0,o=t;r<o.length;r++){var a=o[r],s=a.x-.5,u=a.y-.5,h=n.ignoreRotation?0:e.rotation,l=Math.cos(h)*s-Math.sin(h)*u,c=Math.sin(h)*s+Math.cos(h)*u;l=l*e.width+e.xCenter,c=c*e.height+e.yCenter;var p=a.z*e.width,f=B({},a);f.x=l,f.y=c,f.z=p,i.push(f)}return i}function dt(t,e){var n=function(t,e,n,i){var r=e-t,o=i-n;if(0===r)throw new Error(\"Original min and max are both \".concat(t,\", range cannot be 0.\"));var a=o/r;return{scale:a,offset:n-t*a}}(0,255,e[0],e[1]);return r((function(){return o(a(t,n.scale),n.offset)}))}function mt(t,e,n){var i,o,a,c,p,f,d,m,g,y,v,x,w,k,b=e.outputTensorSize,M=e.keepAspectRatio,S=e.borderMode,T=e.outputTensorFloatRange,P=rt(t),F=function(t,e){return e?{xCenter:e.xCenter*t.width,yCenter:e.yCenter*t.height,width:e.width*t.width,height:e.height*t.height,rotation:e.rotation}:{xCenter:.5*t.width,yCenter:.5*t.height,width:t.width,height:t.height,rotation:0}}(P,n),_=function(t,e,n){if(void 0===n&&(n=!1),!n)return{top:0,left:0,right:0,bottom:0};var i=e.height,r=e.width;ut(e,\"targetSize\"),ut(t,\"roi\");var o,a,s=i/r,u=t.height/t.width,h=0,l=0;return s>u?(o=t.width,a=t.width*s,l=(1-u/s)/2):(o=t.height/s,a=t.height,h=(1-s/u)/2),t.width=o,t.height=a,{top:l,left:h,right:h,bottom:l}}(F,b,M),O=(i=F,o=P.width,a=P.height,c=!1,p=i.width,f=i.height,d=c?-1:1,m=Math.cos(i.rotation),g=Math.sin(i.rotation),y=i.xCenter,v=i.yCenter,x=1/o,w=1/a,(k=new Array(16))[0]=p*m*d*x,k[1]=-f*g*x,k[2]=0,k[3]=(-.5*p*m*d+.5*f*g+y)*x,k[4]=p*g*d*w,k[5]=f*m*w,k[6]=0,k[7]=(-.5*f*m-.5*p*g*d+v)*w,k[8]=0,k[9]=0,k[10]=p*x,k[11]=0,k[12]=0,k[13]=0,k[14]=0,k[15]=1,lt(k));return{imageTensor:r((function(){var e=at(t),n=s(st(O,P,b),[1,8]),i=\"zero\"===S?\"constant\":\"nearest\",r=u.transform(h(l(e,\"float32\")),n,\"bilinear\",i,0,[b.height,b.width]);return null!=T?dt(r,T):r})),padding:_,transformationMatrix:O}}function gt(t,e,n,i){return 1===i?.5*(t+e):t+(e-t)*n/(i-1)}function yt(t){return r((function(){var e=function(t){return r((function(){return[c(t,[0,0,0],[1,-1,1]),c(t,[0,0,1],[1,-1,-1])]}))}(t),n=e[0],i=e[1];return{boxes:p(i),logits:p(n)}}))}function vt(t){return null!=t&&null!=t.currentTime}function xt(t){for(var e={locationData:{relativeKeypoints:[]}},n=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,a=0;a<t.length;++a){var s=t[a];n=Math.min(n,s.x),i=Math.max(i,s.x),r=Math.min(r,s.y),o=Math.max(o,s.y),e.locationData.relativeKeypoints.push({x:s.x,y:s.y})}return e.locationData.relativeBoundingBox={xMin:n,yMin:r,xMax:i,yMax:o,width:i-n,height:o-r},e}function wt(t,e,n,i){return N(this,void 0,void 0,(function(){var i,r,o,a,h;return D(this,(function(l){switch(l.label){case 0:return t.sort((function(t,e){return Math.max.apply(Math,e.score)-Math.max.apply(Math,t.score)})),i=s(t.map((function(t){return[t.locationData.relativeBoundingBox.yMin,t.locationData.relativeBoundingBox.xMin,t.locationData.relativeBoundingBox.yMax,t.locationData.relativeBoundingBox.xMax]}))),r=d(t.map((function(t){return t.score[0]}))),[4,u.nonMaxSuppressionAsync(i,r,e,n)];case 1:return[4,(o=l.sent()).array()];case 2:return a=l.sent(),h=t.filter((function(t,e){return a.indexOf(e)>-1})),f([i,r,o]),[2,h]}}))}))}function kt(t,e){return t.map((function(t){var n=B(B({},t),{x:t.x*e.width,y:t.y*e.height});return null!=t.z&&(n.z=t.z*e.width),n}))}function bt(t,e,n){return N(this,void 0,void 0,(function(){var i,r,o,a,s,u,h,l,c,f,d,m,g,y,v,x,w,k,b,M,S,T,P,F;return D(this,(function(_){switch(_.label){case 0:if(i=p(e,[0]),r=i.shape,o=r[0],a=r[1],s=r[2],t.length!==s)throw new Error(\"Expected heatmap to have same number of channels as the number of landmarks. But got landmarks length: \"+\"\".concat(t.length,\", heatmap length: \").concat(s));return u=[],[4,i.buffer()];case 1:for(h=_.sent(),l=0;l<t.length;l++)if(c=t[l],f=B({},c),u.push(f),d=Math.trunc(f.x*a),m=Math.trunc(f.y*o),!(d<0||d>=a||m<0||d>=o)){for(g=Math.trunc((n.kernelSize-1)/2),y=Math.max(0,d-g),v=Math.min(a,d+g+1),x=Math.max(0,m-g),w=Math.min(o,m+g+1),k=0,b=0,M=0,S=0,T=x;T<w;++T)for(P=y;P<v;++P)F=h.get(T,P,l),k+=F,S=Math.max(S,F),b+=P*F,M+=T*F;S>=n.minConfidenceToRefine&&k>0&&(f.x=b/a/k,f.y=M/o/k)}return i.dispose(),[2,u]}}))}))}function Mt(t,e){var n=e.left,i=e.top,r=e.left+e.right,o=e.top+e.bottom;return t.map((function(t){return B(B({},t),{x:(t.x-n)/(1-r),y:(t.y-i)/(1-o),z:t.z/(1-r)})}))}function St(t,e,n){return\"webgl\"===m()?function(t,e,n){var i=n.combineWithPreviousRatio.toFixed(2),o={variableNames:[\"prevMask\",\"newMask\"],outputShape:t.shape,userCode:\"\\n  void main() {\\n      ivec2 coords = getOutputCoords();\\n      int height = coords[0];\\n      int width = coords[1];\\n\\n      float prevMaskValue = getPrevMask(height, width);\\n      float newMaskValue = getNewMask(height, width);\\n\\n      /*\\n      * Assume p := newMaskValue\\n      * H(p) := 1 + (p * log(p) + (1-p) * log(1-p)) / log(2)\\n      * uncertainty alpha(p) =\\n      *   Clamp(1 - (1 - H(p)) * (1 - H(p)), 0, 1) [squaring the\\n      * uncertainty]\\n      *\\n      * The following polynomial approximates uncertainty alpha as a\\n      * function of (p + 0.5):\\n      */\\n      const float c1 = 5.68842;\\n      const float c2 = -0.748699;\\n      const float c3 = -57.8051;\\n      const float c4 = 291.309;\\n      const float c5 = -624.717;\\n      float t = newMaskValue - 0.5;\\n      float x = t * t;\\n\\n      float uncertainty =\\n        1.0 - min(1.0, x * (c1 + x * (c2 + x * (c3 + x * (c4 + x * c5)))));\\n\\n      float outputValue = newMaskValue + (prevMaskValue - newMaskValue) *\\n                             (uncertainty * \".concat(i,\");\\n\\n      setOutput(outputValue);\\n    }\\n\")},a=w();return r((function(){var n=a.compileAndRun(o,[t,e]);return g().makeTensorFromDataId(n.dataId,n.shape,n.dtype)}))}(t,e,n):r((function(){var i=y(e,.5),r=v(i),s=y(1,x(1,a(r,o(5.68842,a(r,o(-.748699,a(r,o(-57.8051,a(r,o(291.309,a(r,-624.717)))))))))));return o(e,a(y(t,e),a(s,n.combineWithPreviousRatio)))}))}function Tt(t,e,n){return N(this,void 0,void 0,(function(){var i,s,u,h,l;return D(this,(function(d){switch(d.label){case 0:return i=t[0],s=t[1],u=function(t,e,n){return r((function(){var i,r,s,u;n.reverseOutputOrder?(r=p(c(t,[0,n.boxCoordOffset+0],[-1,1])),i=p(c(t,[0,n.boxCoordOffset+1],[-1,1])),u=p(c(t,[0,n.boxCoordOffset+2],[-1,1])),s=p(c(t,[0,n.boxCoordOffset+3],[-1,1]))):(i=p(c(t,[0,n.boxCoordOffset+0],[-1,1])),r=p(c(t,[0,n.boxCoordOffset+1],[-1,1])),s=p(c(t,[0,n.boxCoordOffset+2],[-1,1])),u=p(c(t,[0,n.boxCoordOffset+3],[-1,1]))),r=o(a(k(r,n.xScale),e.w),e.x),i=o(a(k(i,n.yScale),e.h),e.y),n.applyExponentialOnBoxSize?(s=a(b(k(s,n.hScale)),e.h),u=a(b(k(u,n.wScale)),e.w)):(s=a(k(s,n.hScale),e.h),u=a(k(u,n.wScale),e.h));var h=y(i,k(s,2)),l=y(r,k(u,2)),f=o(i,k(s,2)),d=o(r,k(u,2)),m=M([S(h,[n.numBoxes,1]),S(l,[n.numBoxes,1]),S(f,[n.numBoxes,1]),S(d,[n.numBoxes,1])],1);if(n.numKeypoints)for(var g=0;g<n.numKeypoints;++g){var v=n.keypointCoordOffset+g*n.numValuesPerKeypoint,x=void 0,w=void 0;n.reverseOutputOrder?(x=p(c(t,[0,v],[-1,1])),w=p(c(t,[0,v+1],[-1,1]))):(w=p(c(t,[0,v],[-1,1])),x=p(c(t,[0,v+1],[-1,1])));var T=o(a(k(x,n.xScale),e.w),e.x),P=o(a(k(w,n.yScale),e.h),e.y);m=M([m,S(T,[n.numBoxes,1]),S(P,[n.numBoxes,1])],1)}return m}))}(s,e,n),h=r((function(){var t=i;return n.sigmoidScore?(null!=n.scoreClippingThresh&&(t=T(i,-n.scoreClippingThresh,n.scoreClippingThresh)),t=P(t)):t})),[4,Pt(u,h,n)];case 1:return l=d.sent(),f([u,h]),[2,l]}}))}))}function Pt(t,e,n){return N(this,void 0,void 0,(function(){var i,r,o,a,s,u,h,l,c,p,f,d;return D(this,(function(m){switch(m.label){case 0:return i=[],[4,t.data()];case 1:return r=m.sent(),[4,e.data()];case 2:for(o=m.sent(),a=0;a<n.numBoxes;++a)if(!(null!=n.minScoreThresh&&o[a]<n.minScoreThresh||(s=a*n.numCoords,u=Ft(r[s+0],r[s+1],r[s+2],r[s+3],o[a],n.flipVertically,a),(h=u.locationData.relativeBoundingBox).width<0||h.height<0))){if(n.numKeypoints>0)for((l=u.locationData).relativeKeypoints=[],c=n.numKeypoints*n.numValuesPerKeypoint,p=0;p<c;p+=n.numValuesPerKeypoint)f=s+n.keypointCoordOffset+p,d={x:r[f+0],y:n.flipVertically?1-r[f+1]:r[f+1]},l.relativeKeypoints.push(d);i.push(u)}return[2,i]}}))}))}function Ft(t,e,n,i,r,o,a){return{score:[r],ind:a,locationData:{relativeBoundingBox:{xMin:e,yMin:o?1-n:t,xMax:i,yMax:o?1-t:n,width:i-e,height:n-t}}}}function _t(t,e){return\"none\"===t?e:function(t){return 1/(1+Math.exp(-t))}(e)}function Ot(t,e,n,i){return N(this,void 0,void 0,(function(){var r,o,a,s,u,h,l,c;return D(this,(function(p){switch(p.label){case 0:return n=n||e.flipHorizontally||!1,i=i||e.flipVertically||!1,r=t.size,o=r/e.numLandmarks,[4,t.data()];case 1:for(a=p.sent(),s=[],u=0;u<e.numLandmarks;++u)h=u*o,(c={x:0,y:0}).x=n?e.inputImageWidth-a[h]:a[h],o>1&&(c.y=i?e.inputImageHeight-a[h+1]:a[h+1]),o>2&&(c.z=a[h+2]),o>3&&(c.score=_t(e.visibilityActivation,a[h+3])),s.push(c);for(l=0;l<s.length;++l)(c=s[l]).x=c.x/e.inputImageWidth,c.y=c.y/e.inputImageHeight,c.z=c.z/e.inputImageWidth/(e.normalizeZ||1);return[2,s]}}))}))}function It(t,e,n){var i=t.width,r=t.height,o=t.rotation;if(null==n.rotation&&null==n.rotationDegree||(o=function(t,e){null!=e.rotation?t+=e.rotation:null!=e.rotationDegree&&(t+=Math.PI*e.rotationDegree/180);return ot(t)}(o,n)),0===o)t.xCenter=t.xCenter+i*n.shiftX,t.yCenter=t.yCenter+r*n.shiftY;else{var a=(e.width*i*n.shiftX*Math.cos(o)-e.height*r*n.shiftY*Math.sin(o))/e.width,s=(e.width*i*n.shiftX*Math.sin(o)+e.height*r*n.shiftY*Math.cos(o))/e.height;t.xCenter=t.xCenter+a,t.yCenter=t.yCenter+s}if(n.squareLong){var u=Math.max(i*e.width,r*e.height);i=u/e.width,r=u/e.height}else if(n.squareShort){var h=Math.min(i*e.width,r*e.height);i=h/e.width,r=h/e.height}return t.width=i*n.scaleX,t.height=r*n.scaleY,t}function At(t,e){return t.map((function(t){var n=B(B({},t),{x:t.x/e.width,y:t.y/e.height});return null!=t.z&&(t.z=t.z/e.width),n}))}var zt=function(){function t(t){this.alpha=t,this.initialized=!1}return t.prototype.apply=function(t,e){var n;return this.initialized?n=null==e?this.storedValue+this.alpha*(t-this.storedValue):this.storedValue+this.alpha*e*Math.asinh((t-this.storedValue)/e):(n=t,this.initialized=!0),this.rawValue=t,this.storedValue=n,n},t.prototype.applyWithAlpha=function(t,e,n){return this.alpha=e,this.apply(t,n)},t.prototype.hasLastRawValue=function(){return this.initialized},t.prototype.lastRawValue=function(){return this.rawValue},t.prototype.reset=function(){this.initialized=!1},t}(),Ct=function(){function t(t){this.frequency=t.frequency,this.minCutOff=t.minCutOff,this.beta=t.beta,this.thresholdCutOff=t.thresholdCutOff,this.thresholdBeta=t.thresholdBeta,this.derivateCutOff=t.derivateCutOff,this.x=new zt(this.getAlpha(this.minCutOff)),this.dx=new zt(this.getAlpha(this.derivateCutOff)),this.lastTimestamp=0}return t.prototype.apply=function(t,e,n){if(null==t)return t;var i=Math.trunc(e);if(this.lastTimestamp>=i)return t;0!==this.lastTimestamp&&0!==i&&(this.frequency=1/(1e-6*(i-this.lastTimestamp))),this.lastTimestamp=i;var r=this.x.hasLastRawValue()?(t-this.x.lastRawValue())*n*this.frequency:0,o=this.dx.applyWithAlpha(r,this.getAlpha(this.derivateCutOff)),a=this.minCutOff+this.beta*Math.abs(o),s=null!=this.thresholdCutOff?this.thresholdCutOff+this.thresholdBeta*Math.abs(o):null;return this.x.applyWithAlpha(t,this.getAlpha(a),s)},t.prototype.getAlpha=function(t){return 1/(1+this.frequency/(2*Math.PI*t))},t}(),Et=function(){function t(t){this.config=t}return t.prototype.apply=function(t,e,n){var i=this;if(null==t)return this.reset(),null;this.initializeFiltersIfEmpty(t);var r=1;if(!this.config.disableValueScaling){if(n<this.config.minAllowedObjectScale)return K([],t,!0);r=1/n}return t.map((function(t,n){var o=B(B({},t),{x:i.xFilters[n].apply(t.x,e,r),y:i.yFilters[n].apply(t.y,e,r)});return null!=t.z&&(o.z=i.zFilters[n].apply(t.z,e,r)),o}))},t.prototype.reset=function(){this.xFilters=null,this.yFilters=null,this.zFilters=null},t.prototype.initializeFiltersIfEmpty=function(t){var e=this;null!=this.xFilters&&this.xFilters.length===t.length||(this.xFilters=t.map((function(t){return new Ct(e.config)})),this.yFilters=t.map((function(t){return new Ct(e.config)})),this.zFilters=t.map((function(t){return new Ct(e.config)})))},t}(),Rt=function(){function t(t){this.config=t,this.window=[],this.lowPassFilter=new zt(1),this.lastValue=0,this.lastValueScale=1,this.lastTimestamp=-1}return t.prototype.apply=function(t,e,n){if(null==t)return t;var i,r=Math.trunc(e);if(this.lastTimestamp>=r)return t;if(-1===this.lastTimestamp)i=1;else{for(var o=t*n-this.lastValue*this.lastValueScale,a=r-this.lastTimestamp,s=o,u=a,h=(1+this.window.length)*(1e6/30),l=0,c=this.window;l<c.length;l++){var p=c[l];if(u+p.duration>h)break;s+=p.distance,u+=p.duration}var f=s/(1e-6*u);i=1-1/(1+this.config.velocityScale*Math.abs(f)),this.window.unshift({distance:o,duration:a}),this.window.length>this.config.windowSize&&this.window.pop()}return this.lastValue=t,this.lastValueScale=n,this.lastTimestamp=r,this.lowPassFilter.applyWithAlpha(t,i)},t}(),Lt=function(){function t(t){this.config=t}return t.prototype.apply=function(t,e,n){var i=this;if(null==t)return this.reset(),null;var r=1;if(!this.config.disableValueScaling){if(n<this.config.minAllowedObjectScale)return K([],t,!0);r=1/n}return this.initializeFiltersIfEmpty(t),t.map((function(t,n){var o=B(B({},t),{x:i.xFilters[n].apply(t.x,e,r),y:i.yFilters[n].apply(t.y,e,r)});return null!=t.z&&(o.z=i.zFilters[n].apply(t.z,e,r)),o}))},t.prototype.reset=function(){this.xFilters=null,this.yFilters=null,this.zFilters=null},t.prototype.initializeFiltersIfEmpty=function(t){var e=this;null!=this.xFilters&&this.xFilters.length===t.length||(this.xFilters=t.map((function(t){return new Rt(e.config)})),this.yFilters=t.map((function(t){return new Rt(e.config)})),this.zFilters=t.map((function(t){return new Rt(e.config)})))},t}(),Vt=function(){function t(t){if(null!=t.velocityFilter)this.keypointsFilter=new Lt(t.velocityFilter);else{if(null==t.oneEuroFilter)throw new Error(\"Either configure velocityFilter or oneEuroFilter, but got \"+\"\".concat(t,\".\"));this.keypointsFilter=new Et(t.oneEuroFilter)}}return t.prototype.apply=function(t,e,n,i,r){if(void 0===i&&(i=!1),null==t)return this.keypointsFilter.reset(),null;var o=null!=r?function(t,e){return(t.width*e.width+t.height*e.height)/2}(r,n):1,a=i?kt(t,n):t,s=this.keypointsFilter.apply(a,e,o);return i?At(s,n):s},t}(),Bt=function(){function t(t){this.alpha=t.alpha}return t.prototype.apply=function(t){var e=this;if(null==t)return this.visibilityFilters=null,null;null!=this.visibilityFilters&&this.visibilityFilters.length===t.length||(this.visibilityFilters=t.map((function(t){return new zt(e.alpha)})));for(var n=[],i=0;i<t.length;++i){var r=t[i],o=B({},r);o.score=this.visibilityFilters[i].apply(r.score),n.push(o)}return n},t}(),Nt={reduceBoxesInLowestlayer:!1,interpolatedScaleAspectRatio:1,featureMapHeight:[],featureMapWidth:[],numLayers:5,minScale:.1484375,maxScale:.75,inputSizeHeight:224,inputSizeWidth:224,anchorOffsetX:.5,anchorOffsetY:.5,strides:[8,16,32,32,32],aspectRatios:[1],fixedAnchorSize:!0},Dt={runtime:\"tfjs\",modelType:\"full\",enableSmoothing:!0,enableSegmentation:!1,smoothSegmentation:!0,detectorModelUrl:\"https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/detector/1\",landmarkModelUrl:\"https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/full/2\"},Kt={maxPoses:1,flipHorizontal:!1},Ut={applyExponentialOnBoxSize:!1,flipVertically:!1,ignoreClasses:[],numClasses:1,numBoxes:2254,numCoords:12,boxCoordOffset:0,keypointCoordOffset:4,numKeypoints:4,numValuesPerKeypoint:2,sigmoidScore:!0,scoreClippingThresh:100,reverseOutputOrder:!0,xScale:224,yScale:224,hScale:224,wScale:224,minScoreThresh:.5},jt=.3,Ht={shiftX:0,shiftY:0,scaleX:1.25,scaleY:1.25,squareLong:!0},qt={outputTensorSize:{width:224,height:224},keepAspectRatio:!0,outputTensorFloatRange:[-1,1],borderMode:\"zero\"},Xt={outputTensorSize:{width:256,height:256},keepAspectRatio:!0,outputTensorFloatRange:[0,1],borderMode:\"zero\"},Yt={numLandmarks:39,inputImageWidth:256,inputImageHeight:256,visibilityActivation:\"sigmoid\",flipHorizontally:!1,flipVertically:!1},Wt={numLandmarks:39,inputImageWidth:1,inputImageHeight:1,visibilityActivation:\"sigmoid\",flipHorizontally:!1,flipVertically:!1},Gt={kernelSize:7,minConfidenceToRefine:.5},Qt={alpha:.1},Zt={oneEuroFilter:{frequency:30,minCutOff:.05,beta:80,derivateCutOff:1,minAllowedObjectScale:1e-6}},$t={oneEuroFilter:{frequency:30,minCutOff:.01,beta:10,derivateCutOff:1,minAllowedObjectScale:1e-6}},Jt={oneEuroFilter:{frequency:30,minCutOff:.1,beta:40,derivateCutOff:1,minAllowedObjectScale:1e-6,disableValueScaling:!0}},te={activation:\"none\"},ee={combineWithPreviousRatio:.7};var ne=function(){function t(t){this.mask=t}return t.prototype.toCanvasImageSource=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,G(this.mask)]}))}))},t.prototype.toImageData=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,Q(this.mask)]}))}))},t.prototype.toTensor=function(){return N(this,void 0,void 0,(function(){return D(this,(function(t){return[2,this.mask]}))}))},t.prototype.getUnderlyingType=function(){return\"tensor\"},t}();function ie(t){return $(t),\"person\"}var re=function(){function t(t,e,n,i,r,o){this.detectorModel=t,this.landmarkModel=e,this.enableSmoothing=n,this.enableSegmentation=i,this.smoothSegmentation=r,this.modelType=o,this.regionOfInterest=null,this.prevFilteredSegmentationMask=null,this.anchors=function(t){null==t.reduceBoxesInLowestLayer&&(t.reduceBoxesInLowestLayer=!1),null==t.interpolatedScaleAspectRatio&&(t.interpolatedScaleAspectRatio=1),null==t.fixedAnchorSize&&(t.fixedAnchorSize=!1);for(var e=[],n=0;n<t.numLayers;){for(var i=[],r=[],o=[],a=[],s=n;s<t.strides.length&&t.strides[s]===t.strides[n];){var u=gt(t.minScale,t.maxScale,s,t.strides.length);if(0===s&&t.reduceBoxesInLowestLayer)o.push(1),o.push(2),o.push(.5),a.push(.1),a.push(u),a.push(u);else{for(var h=0;h<t.aspectRatios.length;++h)o.push(t.aspectRatios[h]),a.push(u);if(t.interpolatedScaleAspectRatio>0){var l=s===t.strides.length-1?1:gt(t.minScale,t.maxScale,s+1,t.strides.length);a.push(Math.sqrt(u*l)),o.push(t.interpolatedScaleAspectRatio)}}s++}for(var c=0;c<o.length;++c){var p=Math.sqrt(o[c]);i.push(a[c]/p),r.push(a[c]*p)}var f=0,d=0;if(t.featureMapHeight.length>0)f=t.featureMapHeight[n],d=t.featureMapWidth[n];else{var m=t.strides[n];f=Math.ceil(t.inputSizeHeight/m),d=Math.ceil(t.inputSizeWidth/m)}for(var g=0;g<f;++g)for(var y=0;y<d;++y)for(var v=0;v<i.length;++v){var x={xCenter:(y+t.anchorOffsetX)/d,yCenter:(g+t.anchorOffsetY)/f,width:0,height:0};t.fixedAnchorSize?(x.width=1,x.height=1):(x.width=r[v],x.height=i[v]),e.push(x)}n=s}return e}(Nt);var a=d(this.anchors.map((function(t){return t.width}))),u=d(this.anchors.map((function(t){return t.height}))),h=d(this.anchors.map((function(t){return t.xCenter}))),l=d(this.anchors.map((function(t){return t.yCenter})));this.anchorTensor={x:h,y:l,w:a,h:u},this.prevFilteredSegmentationMask=this.enableSegmentation?s([],[0,0]):null}return t.prototype.estimatePoses=function(t,e,n){return N(this,void 0,void 0,(function(){var i,o,a,s,u,c,p,d,m,g,y,v,x,w,k,b,M,S,T,P,O,I,A;return D(this,(function(z){switch(z.label){case 0:return i=function(t){var e;if(null==(e=null==t?Kt:B({},t)).maxPoses&&(e.maxPoses=1),e.maxPoses<=0)throw new Error(\"Invalid maxPoses \".concat(e.maxPoses,\". Should be > 0.\"));if(e.maxPoses>1)throw new Error(\"Multi-pose detection is not implemented yet. Please set maxPoses to 1.\");return e}(e),null==t?(this.reset(),[2,[]]):(this.maxPoses=i.maxPoses,this.timestamp=null!=n?1e3*n:vt(t)?1e6*t.currentTime:null,o=rt(t),a=r((function(){return l(at(t),\"float32\")})),null!=(s=this.regionOfInterest)?[3,2]:[4,this.detectPose(a)]);case 1:if(0===(u=z.sent()).length)return this.reset(),a.dispose(),[2,[]];c=u[0],s=this.poseDetectionToRoi(c,o),z.label=2;case 2:return[4,this.poseLandmarksByRoi(s,a)];case 3:return p=z.sent(),a.dispose(),null==p?(this.reset(),[2,[]]):(d=p.landmarks,m=p.auxiliaryLandmarks,g=p.poseScore,y=p.worldLandmarks,v=p.segmentationMask,x=this.poseLandmarkFiltering(d,m,y,o),w=x.actualLandmarksFiltered,k=x.auxiliaryLandmarksFiltered,b=x.actualWorldLandmarksFiltered,M=this.poseLandmarksToRoi(k,o),this.regionOfInterest=M,S=this.smoothSegmentation&&null!=v?this.poseSegmentationFiltering(v):v,null!=(T=null!=w?kt(w,o):null)&&T.forEach((function(t,e){t.name=j[e]})),null!=(P=b)&&P.forEach((function(t,e){t.name=j[e]})),O={score:g,keypoints:T,keypoints3D:P},null!==S&&(I=r((function(){var t=h(S,2),e=F(t,[[0,0],[0,0],[0,1]]);return _(e,[[0,0],[0,0],[0,2]],\"symmetric\")})),this.smoothSegmentation||f(S),A={maskValueToLabel:ie,mask:new ne(I)},O.segmentation=A),[2,[O]])}}))}))},t.prototype.poseSegmentationFiltering=function(t){var e=this.prevFilteredSegmentationMask;return 0===e.size?this.prevFilteredSegmentationMask=t:(this.prevFilteredSegmentationMask=St(e,t,ee),f(t)),f(e),this.prevFilteredSegmentationMask},t.prototype.dispose=function(){this.detectorModel.dispose(),this.landmarkModel.dispose(),f([this.anchorTensor.x,this.anchorTensor.y,this.anchorTensor.w,this.anchorTensor.h,this.prevFilteredSegmentationMask])},t.prototype.reset=function(){this.regionOfInterest=null,this.enableSegmentation&&(f(this.prevFilteredSegmentationMask),this.prevFilteredSegmentationMask=s([],[0,0])),this.visibilitySmoothingFilterActual=null,this.visibilitySmoothingFilterAuxiliary=null,this.landmarksSmoothingFilterActual=null,this.landmarksSmoothingFilterAuxiliary=null},t.prototype.detectPose=function(t){return N(this,void 0,void 0,(function(){var e,n,i,r,o,a,s,u,h,l;return D(this,(function(c){switch(c.label){case 0:return e=mt(t,qt),n=e.imageTensor,i=e.padding,r=this.detectorModel.predict(n),o=yt(r),a=o.boxes,[4,Tt([s=o.logits,a],this.anchorTensor,Ut)];case 1:return 0===(u=c.sent()).length?(f([n,r,s,a]),[2,u]):[4,wt(u,this.maxPoses,jt)];case 2:return h=c.sent(),l=function(t,e){void 0===t&&(t=[]);for(var n=e.left,i=e.top,r=e.left+e.right,o=e.top+e.bottom,a=0;a<t.length;a++){var s=t[a],u=s.locationData.relativeBoundingBox,h=(u.xMin-n)/(1-r),l=(u.yMin-i)/(1-o),c=u.width/(1-r),p=u.height/(1-o);u.xMin=h,u.yMin=l,u.width=c,u.height=p,u.xMax=h+c,u.yMax=l+p;var f=s.locationData.relativeKeypoints;f&&f.forEach((function(t){var e=(t.x-n)/(1-r),a=(t.y-i)/(1-o);t.x=e,t.y=a}))}return t}(h,i),f([n,r,s,a]),[2,l]}}))}))},t.prototype.poseDetectionToRoi=function(t,e){return 0,1,It(ht(t,e,{rotationVectorEndKeypointIndex:1,rotationVectorStartKeypointIndex:0,rotationVectorTargetAngleDegree:90}),e,Ht)},t.prototype.poseLandmarksByRoi=function(t,e){return N(this,void 0,void 0,(function(){var n,i,r,o,a,s,u,h,l,c,p,d,m,g;return D(this,(function(y){switch(y.label){case 0:if(n=rt(e),i=mt(e,Xt,t),r=i.imageTensor,o=i.padding,a=i.transformationMatrix,\"lite\"!==this.modelType&&\"full\"!==this.modelType&&\"heavy\"!==this.modelType)throw new Error(\"Model type must be one of lite, full or heavy,\"+\"but got \".concat(this.modelType));return s=[\"ld_3d\",\"output_poseflag\",\"activation_heatmap\",\"world_3d\"],this.enableSegmentation&&s.push(\"activation_segmentation\"),u=this.landmarkModel.execute(r,s),[4,this.tensorsToPoseLandmarksAndSegmentation(u)];case 1:return null==(h=y.sent())?(f(u),f(r),[2,null]):(l=h.landmarks,c=h.auxiliaryLandmarks,p=h.poseScore,d=h.worldLandmarks,m=h.segmentationMask,[4,this.poseLandmarksAndSegmentationInverseProjection(n,t,o,a,l,c,d,m)]);case 2:return g=y.sent(),f(u),f(r),[2,B({poseScore:p},g)]}}))}))},t.prototype.poseLandmarksAndSegmentationInverseProjection=function(t,e,n,i,o,a,h,l){return N(this,void 0,void 0,(function(){var c,d,m,g,y,v;return D(this,(function(x){return c=Mt(o,n),d=Mt(a,n),m=ft(c,e),g=ft(d,e),y=function(t,e){for(var n=[],i=0,r=t;i<r.length;i++){var o=r[i],a=o.x,s=o.y,u=e.rotation,h=Math.cos(u)*a-Math.sin(u)*s,l=Math.sin(u)*a+Math.cos(u)*s,c=B({},o);c.x=h,c.y=l,n.push(c)}return n}(h,e),v=null,this.enableSegmentation&&(v=r((function(){var e=l.shape,n=e[0],r=e[1],o=function(t){var e=lt(new Array(16).fill(0));e[0][0]=pt(t,0,0),e[1][0]=-pt(t,0,1),e[2][0]=pt(t,0,2),e[3][0]=-pt(t,0,3),e[0][2]=pt(t,2,0),e[1][2]=-pt(t,2,1),e[2][2]=pt(t,2,2),e[3][2]=-pt(t,2,3),e[0][1]=-pt(t,1,0),e[1][1]=pt(t,1,1),e[2][1]=-pt(t,1,2),e[3][1]=pt(t,1,3),e[0][3]=-pt(t,3,0),e[1][3]=pt(t,3,1),e[2][3]=-pt(t,3,2),e[3][3]=pt(t,3,3);for(var n=t[0][0]*e[0][0]+t[1][0]*e[0][1]+t[2][0]*e[0][2]+t[3][0]*e[0][3],i=0;i<e.length;i++)for(var r=0;r<e.length;r++)e[i][r]/=n;return e}(i),a=s(st(o,{width:r,height:n},t),[1,8]),h=[1,n,r,1];return p(u.transform(S(l,h),a,\"bilinear\",\"constant\",0,[t.height,t.width]),[0,3])})),f(l)),[2,{landmarks:m,auxiliaryLandmarks:g,worldLandmarks:y,segmentationMask:v}]}))}))},t.prototype.tensorsToPoseLandmarksAndSegmentation=function(t){return N(this,void 0,void 0,(function(){var e,n,i,o,a,s,h,l,c,f,d,m,g;return D(this,(function(y){switch(y.label){case 0:return e=t[0],n=t[1],i=t[2],o=t[3],a=this.enableSegmentation?t[4]:null,[4,n.data()];case 1:return(s=y.sent()[0])<.5?[2,null]:[4,Ot(e,Yt)];case 2:return[4,bt(y.sent(),i,Gt)];case 3:return h=y.sent(),l=h.slice(0,33),c=h.slice(33,35),[4,Ot(o,Wt)];case 4:return f=y.sent(),d=f.slice(0,33),m=function(t,e,n){void 0===n&&(n=!0);for(var i=[],r=0;r<t.length;r++){var o=B({},e[r]);n&&(o.score=t[r].score),i.push(o)}return i}(l,d,!0),g=this.enableSegmentation?function(t,e,n){return r((function(){var i=p(t,[0]),r=i.shape[2];if(1===r){var o=i;switch(e.activation){case\"none\":break;case\"sigmoid\":o=P(o);break;case\"softmax\":throw new Error(\"Softmax activation requires two channels.\");default:throw new Error(\"Activation not supported (\".concat(e.activation,\")\"))}var a=n?u.resizeBilinear(o,[n.height,n.width]):o;return p(a,[2])}throw new Error(\"Unsupported number of tensor channels \".concat(r))}))}(a,te):null,[2,{landmarks:l,auxiliaryLandmarks:c,poseScore:s,worldLandmarks:m,segmentationMask:g}]}}))}))},t.prototype.poseLandmarksToRoi=function(t,e){return It(ht(xt(t),e,{rotationVectorStartKeypointIndex:0,rotationVectorEndKeypointIndex:1,rotationVectorTargetAngleDegree:90}),e,Ht)},t.prototype.poseLandmarkFiltering=function(t,e,n,i){var r,o,a;if(null!=this.timestamp&&this.enableSmoothing){var s=ht(xt(e),i,{rotationVectorEndKeypointIndex:0,rotationVectorStartKeypointIndex:1,rotationVectorTargetAngleDegree:90});null==this.visibilitySmoothingFilterActual&&(this.visibilitySmoothingFilterActual=new Bt(Qt)),r=this.visibilitySmoothingFilterActual.apply(t),null==this.visibilitySmoothingFilterAuxiliary&&(this.visibilitySmoothingFilterAuxiliary=new Bt(Qt)),o=this.visibilitySmoothingFilterAuxiliary.apply(e),a=this.visibilitySmoothingFilterActual.apply(n),null==this.landmarksSmoothingFilterActual&&(this.landmarksSmoothingFilterActual=new Vt(Zt)),r=this.landmarksSmoothingFilterActual.apply(r,this.timestamp,i,!0,s),null==this.landmarksSmoothingFilterAuxiliary&&(this.landmarksSmoothingFilterAuxiliary=new Vt($t)),o=this.landmarksSmoothingFilterAuxiliary.apply(o,this.timestamp,i,!0,s),null==this.worldLandmarksSmoothingFilterActual&&(this.worldLandmarksSmoothingFilterActual=new Vt(Jt)),a=this.worldLandmarksSmoothingFilterActual.apply(n,this.timestamp)}else r=t,o=e,a=n;return{actualLandmarksFiltered:r,auxiliaryLandmarksFiltered:o,actualWorldLandmarksFiltered:a}},t}();function oe(t){return N(this,void 0,void 0,(function(){var e,n,i,r,o,a;return D(this,(function(s){switch(s.label){case 0:return e=function(t){var e=B({},null==t?Dt:t);if(null==e.enableSmoothing&&(e.enableSmoothing=Dt.enableSmoothing),null==e.enableSegmentation&&(e.enableSegmentation=Dt.enableSegmentation),null==e.smoothSegmentation&&(e.smoothSegmentation=Dt.smoothSegmentation),null==e.modelType&&(e.modelType=Dt.modelType),null==e.detectorModelUrl&&(e.detectorModelUrl=Dt.detectorModelUrl),null==e.landmarkModelUrl)switch(e.modelType){case\"lite\":e.landmarkModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/lite/2\";break;case\"heavy\":e.landmarkModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/heavy/2\";break;case\"full\":default:e.landmarkModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/full/2\"}return e}(t),n=\"string\"==typeof e.detectorModelUrl&&e.detectorModelUrl.indexOf(\"https://tfhub.dev\")>-1,i=\"string\"==typeof e.landmarkModelUrl&&e.landmarkModelUrl.indexOf(\"https://tfhub.dev\")>-1,[4,Promise.all([C(e.detectorModelUrl,{fromTFHub:n}),C(e.landmarkModelUrl,{fromTFHub:i})])];case 1:return r=s.sent(),o=r[0],a=r[1],[2,new re(o,a,e.enableSmoothing,e.enableSegmentation,e.smoothSegmentation,e.modelType)]}}))}))}var ae,se,ue=function(){function t(t){!function(t){if(t.maxTracks<1)throw new Error(\"Must specify 'maxTracks' to be at least 1, but \"+\"encountered \".concat(t.maxTracks));if(t.maxAge<=0)throw new Error(\"Must specify 'maxAge' to be positive, but \"+\"encountered \".concat(t.maxAge));if(void 0!==t.keypointTrackerParams){if(t.keypointTrackerParams.keypointConfidenceThreshold<0||t.keypointTrackerParams.keypointConfidenceThreshold>1)throw new Error(\"Must specify 'keypointConfidenceThreshold' to be in the range [0, 1], but encountered \"+\"\".concat(t.keypointTrackerParams.keypointConfidenceThreshold));if(t.keypointTrackerParams.minNumberOfKeypoints<1)throw new Error(\"Must specify 'minNumberOfKeypoints' to be at least 1, but \"+\"encountered \".concat(t.keypointTrackerParams.minNumberOfKeypoints));for(var e=0,n=t.keypointTrackerParams.keypointFalloff;e<n.length;e++){var i=n[e];if(i<=0)throw new Error(\"Must specify each keypoint falloff parameterto be positive \"+\"but encountered \".concat(i))}}}(t),this.tracks=[],this.maxTracks=t.maxTracks,this.maxAge=1e3*t.maxAge,this.minSimilarity=t.minSimilarity,this.nextID=1}return t.prototype.apply=function(t,e){this.filterOldTracks(e);var n=this.computeSimilarity(t);return this.assignTracks(t,n,e),this.updateTracks(e),t},t.prototype.getTracks=function(){return this.tracks.slice()},t.prototype.getTrackIDs=function(){return new Set(this.tracks.map((function(t){return t.id})))},t.prototype.filterOldTracks=function(t){var e=this;this.tracks=this.tracks.filter((function(n){return t-n.lastTimestamp<=e.maxAge}))},t.prototype.assignTracks=function(t,e,n){for(var i=Array.from(Array(e[0].length).keys()),r=[],o=0,a=Array.from(Array(t.length).keys());o<a.length;o++){var s=a[o];if(0!==i.length){for(var u=-1,h=-1,l=0,c=i;l<c.length;l++){var p=c[l],f=e[s][p];f>=this.minSimilarity&&f>h&&(u=p,h=f)}if(u>=0){var d=this.tracks[u];d=Object.assign(d,this.createTrack(t[s],n,d.id)),t[s].id=d.id;var m=i.indexOf(u);i.splice(m,1)}else r.push(s)}else r.push(s)}for(var g=0,y=r;g<y.length;g++){s=y[g];var v=this.createTrack(t[s],n);this.tracks.push(v),t[s].id=v.id}},t.prototype.updateTracks=function(t){this.tracks.sort((function(t,e){return e.lastTimestamp-t.lastTimestamp})),this.tracks=this.tracks.slice(0,this.maxTracks)},t.prototype.createTrack=function(t,e,n){var i={id:n||this.nextTrackID(),lastTimestamp:e,keypoints:K([],t.keypoints,!0).map((function(t){return B({},t)}))};return void 0!==t.box&&(i.box=B({},t.box)),i},t.prototype.nextTrackID=function(){var t=this.nextID;return this.nextID+=1,t},t.prototype.remove=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.tracks=this.tracks.filter((function(e){return!t.includes(e.id)}))},t.prototype.reset=function(){this.tracks=[]},t}(),he=function(t){function e(e){return t.call(this,e)||this}return V(e,t),e.prototype.computeSimilarity=function(t){var e=this;return 0===t.length||0===this.tracks.length?[[]]:t.map((function(t){return e.tracks.map((function(n){return e.iou(t,n)}))}))},e.prototype.iou=function(t,e){var n=Math.max(t.box.xMin,e.box.xMin),i=Math.max(t.box.yMin,e.box.yMin),r=Math.min(t.box.xMax,e.box.xMax),o=Math.min(t.box.yMax,e.box.yMax);if(n>=r||i>=o)return 0;var a=(r-n)*(o-i);return a/(t.box.width*t.box.height+e.box.width*e.box.height-a)},e}(ue),le=function(t){function e(e){var n=t.call(this,e)||this;return n.keypointThreshold=e.keypointTrackerParams.keypointConfidenceThreshold,n.keypointFalloff=e.keypointTrackerParams.keypointFalloff,n.minNumKeyoints=e.keypointTrackerParams.minNumberOfKeypoints,n}return V(e,t),e.prototype.computeSimilarity=function(t){if(0===t.length||0===this.tracks.length)return[[]];for(var e=[],n=0,i=t;n<i.length;n++){for(var r=i[n],o=[],a=0,s=this.tracks;a<s.length;a++){var u=s[a];o.push(this.oks(r,u))}e.push(o)}return e},e.prototype.oks=function(t,e){for(var n=this.area(e.keypoints)+1e-6,i=0,r=0,o=0;o<t.keypoints.length;++o){var a=t.keypoints[o],s=e.keypoints[o];if(!(a.score<this.keypointThreshold||s.score<this.keypointThreshold)){r+=1;var u=Math.pow(a.x-s.x,2)+Math.pow(a.y-s.y,2),h=2*this.keypointFalloff[o];i+=Math.exp(-1*u/(2*n*Math.pow(h,2)))}}return r<this.minNumKeyoints?0:i/r},e.prototype.area=function(t){var e=this,n=t.filter((function(t){return t.score>e.keypointThreshold})),i=Math.min.apply(Math,K([1],n.map((function(t){return t.x})),!1)),r=Math.max.apply(Math,K([0],n.map((function(t){return t.x})),!1)),o=Math.min.apply(Math,K([1],n.map((function(t){return t.y})),!1));return(r-i)*(Math.max.apply(Math,K([0],n.map((function(t){return t.y})),!1))-o)},e}(ue);function ce(t){switch(t){case se.BlazePose:return j.reduce((function(t,e,n){return t[e]=n,t}),{});case se.PoseNet:case se.MoveNet:return U.reduce((function(t,e,n){return t[e]=n,t}),{});default:throw new Error(\"Model \".concat(t,\" is not supported.\"))}}!function(t){t.Keypoint=\"keypoint\",t.BoundingBox=\"boundingBox\"}(ae||(ae={})),function(t){t.MoveNet=\"MoveNet\",t.BlazePose=\"BlazePose\",t.PoseNet=\"PoseNet\"}(se||(se={}));var pe=Object.freeze({__proto__:null,getKeypointIndexBySide:function(t){switch(t){case se.BlazePose:return H;case se.PoseNet:case se.MoveNet:return q;default:throw new Error(\"Model \".concat(t,\" is not supported.\"))}},getAdjacentPairs:function(t){switch(t){case se.BlazePose:return Y;case se.PoseNet:case se.MoveNet:return X;default:throw new Error(\"Model \".concat(t,\" is not supported.\"))}},getKeypointIndexByName:ce}),fe=[\"SinglePose.Lightning\",\"SinglePose.Thunder\",\"MultiPose.Lightning\"],de={modelType:\"SinglePose.Lightning\",enableSmoothing:!0},me={},ge={frequency:30,minCutOff:2.5,beta:300,derivateCutOff:2.5,thresholdCutOff:.5,thresholdBeta:5,disableValueScaling:!0},ye={maxTracks:18,maxAge:1e3,minSimilarity:.2,keypointTrackerParams:{keypointConfidenceThreshold:.3,keypointFalloff:[.026,.025,.025,.035,.035,.079,.079,.072,.072,.062,.062,.107,.107,.087,.087,.089,.089],minNumberOfKeypoints:4}},ve={maxTracks:18,maxAge:1e3,minSimilarity:.15,trackerParams:{}};function xe(t,e,n,i){for(var r={},o=0,a=U;o<a.length;o++){var s=a[o];r[s]=[e[n[s]].y*i.height,e[n[s]].x*i.width]}if(function(t,e){return(t[e.left_hip].score>.2||t[e.right_hip].score>.2)&&(t[e.left_shoulder].score>.2||t[e.right_shoulder].score>.2)}(e,n)){var u=(r.left_hip[0]+r.right_hip[0])/2,h=(r.left_hip[1]+r.right_hip[1])/2,l=function(t,e,n,i,r){for(var o=[\"left_shoulder\",\"right_shoulder\",\"left_hip\",\"right_hip\"],a=0,s=0,u=0;u<o.length;u++){(f=Math.abs(i-n[o[u]][0]))>a&&(a=f),(d=Math.abs(r-n[o[u]][1]))>s&&(s=d)}for(var h=0,l=0,c=0,p=Object.keys(n);c<p.length;c++){var f,d,m=p[c];if(!(t[e[m]].score<.2))(f=Math.abs(i-n[m][0]))>h&&(h=f),(d=Math.abs(r-n[m][1]))>l&&(l=d)}return[a,s,h,l]}(e,n,r,u,h),c=l[0],p=l[1],f=l[2],d=l[3],m=Math.max(1.9*p,1.9*c,1.2*f,1.2*d),g=[u-(m=Math.min(m,Math.max(h,i.width-h,u,i.height-u))),h-m];if(m>Math.max(i.width,i.height)/2)return we(null==t,i);var y=2*m;return{yMin:g[0]/i.height,xMin:g[1]/i.width,yMax:(g[0]+y)/i.height,xMax:(g[1]+y)/i.width,height:(g[0]+y)/i.height-g[0]/i.height,width:(g[1]+y)/i.width-g[1]/i.width}}return we(null==t,i)}function we(t,e){var n,i,r,o;return t?e.width>e.height?(n=1,i=e.height/e.width,r=0,o=(e.width/2-e.height/2)/e.width):(n=e.width/e.height,i=1,r=(e.height/2-e.width/2)/e.height,o=0):e.width>e.height?(n=e.width/e.height,i=1,r=(e.height/2-e.width/2)/e.height,o=0):(n=1,i=e.height/e.width,r=0,o=(e.width/2-e.height/2)/e.width),{yMin:r,xMin:o,yMax:r+n,xMax:o+i,height:n,width:i}}function ke(t){var e,n=null==t?de:B({},t);if(null==n.modelType)n.modelType=\"SinglePose.Lightning\";else if(fe.indexOf(n.modelType)<0)throw new Error(\"Invalid architecture \".concat(n.modelType,\". \")+\"Should be one of \".concat(fe));if(null==n.enableSmoothing&&(n.enableSmoothing=!0),null!=n.minPoseScore&&(n.minPoseScore<0||n.minPoseScore>1))throw new Error(\"minPoseScore should be between 0.0 and 1.0\");if(null!=n.multiPoseMaxDimension&&(n.multiPoseMaxDimension%32!=0||n.multiPoseMaxDimension<32))throw new Error(\"multiPoseMaxDimension must be a multiple of 32 and higher than 0\");if(\"MultiPose.Lightning\"===n.modelType&&null==n.enableTracking&&(n.enableTracking=!0),\"MultiPose.Lightning\"===n.modelType&&!0===n.enableTracking)if(null==n.trackerType&&(n.trackerType=ae.BoundingBox),n.trackerType===ae.Keypoint)null!=n.trackerConfig?n.trackerConfig=function(t){var e=be(ye,t);e.keypointTrackerParams=B({},ye.keypointTrackerParams),null!=t.keypointTrackerParams&&(null!=t.keypointTrackerParams.keypointConfidenceThreshold&&(e.keypointTrackerParams.keypointConfidenceThreshold=t.keypointTrackerParams.keypointConfidenceThreshold),null!=t.keypointTrackerParams.keypointFalloff&&(e.keypointTrackerParams.keypointFalloff=t.keypointTrackerParams.keypointFalloff),null!=t.keypointTrackerParams.minNumberOfKeypoints&&(e.keypointTrackerParams.minNumberOfKeypoints=t.keypointTrackerParams.minNumberOfKeypoints));return e}(n.trackerConfig):n.trackerConfig=ye;else{if(n.trackerType!==ae.BoundingBox)throw new Error(\"Tracker type not supported by MoveNet\");null!=n.trackerConfig?n.trackerConfig=(e=n.trackerConfig,be(ve,e)):n.trackerConfig=ve}return n}function be(t,e){var n={maxTracks:t.maxTracks,maxAge:t.maxAge,minSimilarity:t.minSimilarity};return null!=e.maxTracks&&(n.maxTracks=e.maxTracks),null!=e.maxAge&&(n.maxAge=e.maxAge),null!=e.minSimilarity&&(n.minSimilarity=e.minSimilarity),n}var Me=function(){function t(t,e){this.moveNetModel=t,this.modelInputResolution={height:0,width:0},this.keypointIndexByName=ce(se.MoveNet),\"SinglePose.Lightning\"===e.modelType?(this.modelInputResolution.width=192,this.modelInputResolution.height=192):\"SinglePose.Thunder\"===e.modelType&&(this.modelInputResolution.width=256,this.modelInputResolution.height=256),this.multiPoseModel=\"MultiPose.Lightning\"===e.modelType,this.multiPoseModel||(this.keypointFilter=new Et(ge),this.cropRegionFilterYMin=new zt(.9),this.cropRegionFilterXMin=new zt(.9),this.cropRegionFilterYMax=new zt(.9),this.cropRegionFilterXMax=new zt(.9)),this.enableSmoothing=e.enableSmoothing,e.minPoseScore?this.minPoseScore=e.minPoseScore:this.minPoseScore=.25,e.multiPoseMaxDimension?this.multiPoseMaxDimension=e.multiPoseMaxDimension:this.multiPoseMaxDimension=256,this.enableTracking=e.enableTracking,this.multiPoseModel&&this.enableTracking&&(e.trackerType===ae.Keypoint?this.tracker=new le(e.trackerConfig):e.trackerType===ae.BoundingBox&&(this.tracker=new he(e.trackerConfig)),this.enableSmoothing&&(this.keypointFilterMap=new Map))}return t.prototype.runSinglePersonPoseModel=function(t){return N(this,void 0,void 0,(function(){var e,n,i,r,o;return D(this,(function(a){switch(a.label){case 0:if(4!==(e=this.moveNetModel.execute(t)).shape.length||1!==e.shape[0]||1!==e.shape[1]||17!==e.shape[2]||3!==e.shape[3])throw e.dispose(),new Error(\"Unexpected output shape from model: [\".concat(e.shape,\"]\"));return\"webgpu\"===m()?[3,1]:(n=e.dataSync(),[3,3]);case 1:return[4,e.data()];case 2:n=a.sent(),a.label=3;case 3:for(e.dispose(),i={keypoints:[],score:0},r=0,o=0;o<17;++o)i.keypoints[o]={y:n[3*o],x:n[3*o+1],score:n[3*o+2]},i.keypoints[o].score>.2&&(++r,i.score+=i.keypoints[o].score);return r>0&&(i.score/=r),[2,i]}}))}))},t.prototype.runMultiPersonPoseModel=function(t){return N(this,void 0,void 0,(function(){var e,n,i,r,o,a,s,u;return D(this,(function(h){switch(h.label){case 0:if(3!==(e=this.moveNetModel.execute(t)).shape.length||1!==e.shape[0]||56!==e.shape[2])throw e.dispose(),new Error(\"Unexpected output shape from model: [\".concat(e.shape,\"]\"));return\"webgpu\"===m()?[3,1]:(n=e.dataSync(),[3,3]);case 1:return[4,e.data()];case 2:n=h.sent(),h.label=3;case 3:for(e.dispose(),i=[],r=n.length/56,o=0;o<r;++o)for(i[o]={keypoints:[]},a=56*o+51,i[o].box={yMin:n[a],xMin:n[a+1],yMax:n[a+2],xMax:n[a+3],width:n[a+3]-n[a+1],height:n[a+2]-n[a]},s=56*o+55,i[o].score=n[s],i[o].keypoints=[],u=0;u<17;++u)i[o].keypoints[u]={y:n[56*o+3*u],x:n[56*o+3*u+1],score:n[56*o+3*u+2]};return[2,i]}}))}))},t.prototype.estimatePoses=function(t,n,i){return void 0===n&&(n=me),N(this,void 0,void 0,(function(){var r,o,a,s,u,l;return D(this,(function(c){switch(c.label){case 0:return n=function(t){return null==t?me:B({},t)}(n),null==t?(this.reset(),[2,[]]):(null==i?vt(t)&&(i=1e6*t.currentTime):i*=1e3,r=at(t),o=rt(r),a=h(r,0),t instanceof e||r.dispose(),s=[],this.multiPoseModel?[3,2]:[4,this.estimateSinglePose(a,o,i)]);case 1:return s=c.sent(),[3,4];case 2:return[4,this.estimateMultiplePoses(a,o,i)];case 3:s=c.sent(),c.label=4;case 4:for(u=0;u<s.length;++u)for(l=0;l<s[u].keypoints.length;++l)s[u].keypoints[l].name=U[l],s[u].keypoints[l].y*=o.height,s[u].keypoints[l].x*=o.width;return[2,s]}}))}))},t.prototype.estimateSinglePose=function(t,e,n){return N(this,void 0,void 0,(function(){var i,o,a,h,c=this;return D(this,(function(p){switch(p.label){case 0:return this.cropRegion||(this.cropRegion=we(null==this.cropRegion,e)),i=r((function(){var e=s([[c.cropRegion.yMin,c.cropRegion.xMin,c.cropRegion.yMax,c.cropRegion.xMax]]),n=I([1],\"int32\"),i=[c.modelInputResolution.height,c.modelInputResolution.width];return l(u.cropAndResize(t,e,n,i,\"bilinear\",0),\"int32\")})),t.dispose(),[4,this.runSinglePersonPoseModel(i)];case 1:if(o=p.sent(),i.dispose(),o.score<this.minPoseScore)return this.reset(),[2,[]];for(a=0;a<o.keypoints.length;++a)o.keypoints[a].y=this.cropRegion.yMin+o.keypoints[a].y*this.cropRegion.height,o.keypoints[a].x=this.cropRegion.xMin+o.keypoints[a].x*this.cropRegion.width;return null!=n&&this.enableSmoothing&&(o.keypoints=this.keypointFilter.apply(o.keypoints,n,1)),h=xe(this.cropRegion,o.keypoints,this.keypointIndexByName,e),this.cropRegion=this.filterCropRegion(h),[2,[o]]}}))}))},t.prototype.estimateMultiplePoses=function(t,e,n){return N(this,void 0,void 0,(function(){var i,r,o,a,s,h,c,p,f,d,m,g=this;return D(this,(function(y){switch(y.label){case 0:return 32,e.width>e.height?(r=this.multiPoseMaxDimension,o=Math.round(this.multiPoseMaxDimension*e.height/e.width),i=u.resizeBilinear(t,[o,r]),s=r,h=32*Math.ceil(o/32),a=F(i,[[0,0],[0,h-o],[0,0],[0,0]])):(r=Math.round(this.multiPoseMaxDimension*e.width/e.height),o=this.multiPoseMaxDimension,i=u.resizeBilinear(t,[o,r]),s=32*Math.ceil(r/32),h=o,a=F(i,[[0,0],[0,0],[0,s-r],[0,0]])),i.dispose(),t.dispose(),c=l(a,\"int32\"),a.dispose(),[4,this.runMultiPersonPoseModel(c)];case 1:for(p=y.sent(),c.dispose(),p=p.filter((function(t){return t.score>=g.minPoseScore})),d=0;d<p.length;++d)for(f=0;f<p[d].keypoints.length;++f)p[d].keypoints[f].y*=h/o,p[d].keypoints[f].x*=s/r;if(this.enableTracking&&(this.tracker.apply(p,n),this.enableSmoothing)){for(d=0;d<p.length;++d)this.keypointFilterMap.has(p[d].id)||this.keypointFilterMap.set(p[d].id,new Et(ge)),p[d].keypoints=this.keypointFilterMap.get(p[d].id).apply(p[d].keypoints,n,1);m=this.tracker.getTrackIDs(),this.keypointFilterMap.forEach((function(t,e){m.has(e)||g.keypointFilterMap.delete(e)}))}return[2,p]}}))}))},t.prototype.filterCropRegion=function(t){if(t){var e=this.cropRegionFilterYMin.apply(t.yMin),n=this.cropRegionFilterXMin.apply(t.xMin),i=this.cropRegionFilterYMax.apply(t.yMax),r=this.cropRegionFilterXMax.apply(t.xMax);return{yMin:e,xMin:n,yMax:i,xMax:r,height:i-e,width:r-n}}return this.cropRegionFilterYMin.reset(),this.cropRegionFilterXMin.reset(),this.cropRegionFilterYMax.reset(),this.cropRegionFilterXMax.reset(),null},t.prototype.dispose=function(){this.moveNetModel.dispose()},t.prototype.reset=function(){this.cropRegion=null,this.resetFilters()},t.prototype.resetFilters=function(){this.keypointFilter.reset(),this.cropRegionFilterYMin.reset(),this.cropRegionFilterXMin.reset(),this.cropRegionFilterYMax.reset(),this.cropRegionFilterXMax.reset()},t}();function Se(t){return void 0===t&&(t=de),N(this,void 0,void 0,(function(){var e,n,i,r;return D(this,(function(o){switch(o.label){case 0:return e=ke(t),i=!0,e.modelUrl?(i=\"string\"==typeof e.modelUrl&&e.modelUrl.indexOf(\"https://tfhub.dev\")>-1,[4,C(e.modelUrl,{fromTFHub:i})]):[3,2];case 1:return n=o.sent(),[3,4];case 2:return r=void 0,\"SinglePose.Lightning\"===e.modelType?r=\"https://tfhub.dev/google/tfjs-model/movenet/singlepose/lightning/4\":\"SinglePose.Thunder\"===e.modelType?r=\"https://tfhub.dev/google/tfjs-model/movenet/singlepose/thunder/4\":\"MultiPose.Lightning\"===e.modelType&&(r=\"https://tfhub.dev/google/tfjs-model/movenet/multipose/lightning/1\"),[4,C(r,{fromTFHub:i})];case 3:n=o.sent(),o.label=4;case 4:return\"webgl\"===m()&&O().set(\"TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD\",0),[2,new Me(n,e)]}}))}))}var Te={architecture:\"MobileNetV1\",outputStride:16,multiplier:.75,inputResolution:{height:257,width:257}},Pe=[\"MobileNetV1\",\"ResNet50\"],Fe={MobileNetV1:[8,16],ResNet50:[16]},_e=[8,16,32],Oe={MobileNetV1:[.5,.75,1],ResNet50:[1]},Ie=[1,2,4],Ae={maxPoses:1,flipHorizontal:!1},ze={maxPoses:5,flipHorizontal:!1,scoreThreshold:.5,nmsRadius:20},Ce=[-123.15,-115.9,-103.06];function Ee(t){return Math.floor(t/2)}var Re=function(){function t(t,e){this.priorityQueue=new Array(t),this.numberOfElements=-1,this.getElementValue=e}return t.prototype.enqueue=function(t){this.priorityQueue[++this.numberOfElements]=t,this.swim(this.numberOfElements)},t.prototype.dequeue=function(){var t=this.priorityQueue[0];return this.exchange(0,this.numberOfElements--),this.sink(0),this.priorityQueue[this.numberOfElements+1]=null,t},t.prototype.empty=function(){return-1===this.numberOfElements},t.prototype.size=function(){return this.numberOfElements+1},t.prototype.all=function(){return this.priorityQueue.slice(0,this.numberOfElements+1)},t.prototype.max=function(){return this.priorityQueue[0]},t.prototype.swim=function(t){for(;t>0&&this.less(Ee(t),t);)this.exchange(t,Ee(t)),t=Ee(t)},t.prototype.sink=function(t){for(;2*t<=this.numberOfElements;){var e=2*t;if(e<this.numberOfElements&&this.less(e,e+1)&&e++,!this.less(t,e))break;this.exchange(t,e),t=e}},t.prototype.getValueAt=function(t){return this.getElementValue(this.priorityQueue[t])},t.prototype.less=function(t,e){return this.getValueAt(t)<this.getValueAt(e)},t.prototype.exchange=function(t,e){var n=this.priorityQueue[t];this.priorityQueue[t]=this.priorityQueue[e],this.priorityQueue[e]=n},t}();function Le(t,e,n,i,r,o){for(var a=o.shape,s=a[0],u=a[1],h=!0,l=Math.max(n-r,0),c=Math.min(n+r+1,s),p=l;p<c;++p){for(var f=Math.max(i-r,0),d=Math.min(i+r+1,u),m=f;m<d;++m)if(o.get(p,m,t)>e){h=!1;break}if(!h)break}return h}function Ve(t){return N(this,void 0,void 0,(function(){return D(this,(function(e){return[2,Promise.all(t.map((function(t){return t.buffer()})))]}))}))}function Be(t,e,n,i){return{y:i.get(t,e,n),x:i.get(t,e,n+17)}}function Ne(t,e,n){var i=Be(t.heatmapY,t.heatmapX,t.id,n),r=i.y,o=i.x;return{x:t.heatmapX*e+o,y:t.heatmapY*e+r}}function De(t,e,n,i){var r=n.x,o=n.y;return t.some((function(t){var n,a,s,u,h,l,c=t.keypoints;return n=o,a=r,s=c[i].y,u=c[i].x,(h=s-n)*h+(l=u-a)*l<=e}))}var Ke=U.reduce((function(t,e,n){return t[e]=n,t}),{}),Ue=[[\"nose\",\"left_eye\"],[\"left_eye\",\"left_ear\"],[\"nose\",\"right_eye\"],[\"right_eye\",\"right_ear\"],[\"nose\",\"left_shoulder\"],[\"left_shoulder\",\"left_elbow\"],[\"left_elbow\",\"left_wrist\"],[\"left_shoulder\",\"left_hip\"],[\"left_hip\",\"left_knee\"],[\"left_knee\",\"left_ankle\"],[\"nose\",\"right_shoulder\"],[\"right_shoulder\",\"right_elbow\"],[\"right_elbow\",\"right_wrist\"],[\"right_shoulder\",\"right_hip\"],[\"right_hip\",\"right_knee\"],[\"right_knee\",\"right_ankle\"]].map((function(t){var e=t[0],n=t[1];return[Ke[e],Ke[n]]})),je=Ue.map((function(t){return t[1]})),He=Ue.map((function(t){return t[0]}));function qe(t,e,n){return t<e?e:t>n?n:t}function Xe(t,e,n,i){return{y:qe(Math.round(t.y/e),0,n-1),x:qe(Math.round(t.x/e),0,i-1)}}function Ye(t,e){return{x:t.x+e.x,y:t.y+e.y}}function We(t,e,n,i,r,o,a,s){void 0===s&&(s=2);for(var u=i.shape,h=u[0],l=u[1],c={y:e.y,x:e.x},p=Ye(c,function(t,e,n){var i=n.shape[2]/2;return{y:n.get(e.y,e.x,t),x:n.get(e.y,e.x,i+t)}}(t,Xe(c,o,h,l),a)),f=0;f<s;f++){var d=Xe(p,o,h,l),m=Be(d.y,d.x,n,r);p=Ye({x:d.x*o,y:d.y*o},{x:m.x,y:m.y})}var g=Xe(p,o,h,l),y=i.get(g.y,g.x,n);return{y:p.y,x:p.x,name:U[n],score:y}}function Ge(t,e,n,i,r,o){var a=e.shape[2],s=je.length,u=new Array(a),h=t.part,l=t.score,c=Ne(h,i,n);u[h.id]={score:l,name:U[h.id],y:c.y,x:c.x};for(var p=s-1;p>=0;--p){var f=je[p],d=He[p];u[f]&&!u[d]&&(u[d]=We(p,u[f],d,e,n,i,o))}for(p=0;p<s;++p){f=He[p],d=je[p];u[f]&&!u[d]&&(u[d]=We(p,u[f],d,e,n,i,r))}return u}function Qe(t,e,n){return n.reduce((function(n,i,r){var o=i.y,a=i.x,s=i.score;return De(t,e,{y:o,x:a},r)||(n+=s),n}),0)/n.length}function Ze(t,e,n,i,r,o,a,s){return void 0===a&&(a=.5),void 0===s&&(s=20),N(this,void 0,void 0,(function(){var u,h,l,c,p,f,d,m,g,y,v,x;return D(this,(function(w){switch(w.label){case 0:return[4,Ve([t,e,n,i])];case 1:for(u=w.sent(),h=u[0],l=u[1],c=u[2],p=u[3],f=[],d=function(t,e,n){for(var i=n.shape,r=i[0],o=i[1],a=i[2],s=new Re(r*o*a,(function(t){return t.score})),u=0;u<r;++u)for(var h=0;h<o;++h)for(var l=0;l<a;++l){var c=n.get(u,h,l);c<t||Le(l,c,u,h,e,n)&&s.enqueue({score:c,part:{heatmapY:u,heatmapX:h,id:l}})}return s}(a,1,h),m=s*s;f.length<o&&!d.empty();)g=d.dequeue(),y=Ne(g.part,r,l),De(f,m,y,g.part.id)||(v=Ge(g,h,l,r,c,p),x=Qe(f,m,v),f.push({keypoints:v,score:x}));return[2,f]}}))}))}function $e(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];switch(e.length){case 0:t=\"fn main() \";break;case 1:t=\"fn main(\".concat(e[0],\" : i32)\");break;default:throw Error(\"Unreachable\")}return t}var Je=function(){function t(t){this.variableNames=[\"A\",\"B\"],this.size=!0;this.workgroupSize=[32,1,1],this.outputShape=[t[0],1],this.dispatchLayout=E.flatDispatchLayout(this.outputShape),this.dispatch=E.computeDispatch(this.dispatchLayout,this.outputShape,this.workgroupSize),this.shaderKey=\"getpointsConfidenceOp\"}return t.prototype.getUserCode=function(){return\"\\n        \".concat($e(\"index\"),\" {\\n          if (index < uniforms.size) {\\n            let y = B[index * 2];\\n            let x = B[index * 2 + 1];\\n            let outIndex = y * uniforms.aShape.x * uniforms.aShape.z + x * uniforms.aShape.z + index;\\n            result[index] = A[outIndex];\\n          }\\n        }\\n        \")},t}();function tn(t,e){if(w()instanceof R)return function(t,e){var n=w(),i=new Je(e.shape),r=n.runWebGPUProgram(i,[t,e],\"float32\");return g().makeTensorFromTensorInfo(r)}(t,e);throw new Error(\"getPointsConfidenceWebGPU is not supported in this backend!\")}var en=function(){function t(t){if(this.variableNames=[\"A\",\"B\"],this.size=!0,this.supportedLastDimension=2,2!==t.length||t[1]!==this.supportedLastDimension)throw new Error(\"GetOffsetVectorsProgram only supports shape of [x, \".concat(this.supportedLastDimension,\"], but current shape is \").concat(t));this.workgroupSize=[32,1,1],this.outputShape=t;var e=[t[0],1];this.dispatchLayout=E.flatDispatchLayout(e),this.dispatch=E.computeDispatch(this.dispatchLayout,e,this.workgroupSize),this.shaderKey=\"GetOffsetVectors\"}return t.prototype.getUserCode=function(){return\"\\n    fn getOffsetPoint(y: i32, x: i32, index: i32) -> vec2<i32> {\\n      let outIndexY = y * uniforms.bShape.x * uniforms.bShape.y + x * uniforms.bShape.y + index;\\n      let outIndexX = outIndexY + uniforms.bShape.z;\\n      let outY = i32(B[outIndexY]);\\n      let outX = i32(B[outIndexX]);\\n      return vec2<i32>(outY, outX);\\n    }\\n\\n    \".concat($e(\"index\"),\" {\\n      if (index < uniforms.size) {\\n        let indexY = index * \").concat(this.supportedLastDimension,\";\\n        let indexX = indexY + 1;\\n        let heatmapY = A[indexY];\\n        let heatmapX = A[indexX];\\n        let out = getOffsetPoint(i32(heatmapY), i32(heatmapX), index);\\n        result[indexY] = f32(out[0]);\\n        result[indexX] = f32(out[1]);\\n      }\\n    }\\n    \")},t}();function nn(t,e){if(w()instanceof R)return function(t,e){var n=w(),i=new en(t.shape),r=n.runWebGPUProgram(i,[t,e],\"float32\");return g().makeTensorFromTensorInfo(r)}(t,e);throw new Error(\"getOffsetVectorsGPU is not supported in this backend!\")}function rn(t){var e=t.shape,n=e[0],i=e[1],o=e[2];return r((function(){var e,s,u=S(t,[n*i,o]),l=z(u,0),c=h(k(l,A(i,\"int32\")),1),p=h((e=l,s=i,r((function(){var t=k(e,A(s,\"int32\"));return y(e,a(t,A(s,\"int32\")))}))),1);return M([c,p],1)}))}function on(t,e,n){return r((function(){var i=function(t,e){for(var n=[],i=0;i<U.length;i++){var r=t.get(i,0).valueOf(),o=t.get(i,1).valueOf(),a=an(r,o,i,e),u=a.x,h=a.y;n.push(h),n.push(u)}return s(n,[U.length,2])}(t,n);return o(l(a(t.toTensor(),A(e,\"int32\")),\"float32\"),i)}))}function an(t,e,n,i){return{y:i.get(t,e,n),x:i.get(t,e,n+U.length)}}function sn(t,e,n){return N(this,void 0,void 0,(function(){var i,r,o,a,s,u,h,l,c,p;return D(this,(function(f){switch(f.label){case 0:return i=0,r=rn(t),[4,Promise.all([t.buffer(),e.buffer(),r.buffer()])];case 1:return o=f.sent(),a=o[0],s=o[1],u=o[2],[4,(h=on(u,n,s)).buffer()];case 2:return l=f.sent(),c=Array.from(function(t,e){for(var n=e.shape[0],i=new Float32Array(n),r=0;r<n;r++){var o=e.get(r,0),a=e.get(r,1);i[r]=t.get(o,a,r)}return i}(a,u)),p=c.map((function(t,e){return i+=t,{y:l.get(e,0),x:l.get(e,1),score:t,name:U[e]}})),r.dispose(),h.dispose(),[2,{keypoints:p,score:i/p.length}]}}))}))}function un(t,e,n){return N(this,void 0,void 0,(function(){var i,s,u;return D(this,(function(h){return i=rn(t),s=function(t,e,n){return r((function(){var i=nn(t,n);return o(l(a(t,A(e,\"int32\")),\"float32\"),i)}))}(i,n,e),u=tn(t,i),[2,[s,u]]}))}))}function hn(t,e){return(t-1)%e==0}var ln=\"https://storage.googleapis.com/tfjs-models/savedmodel/posenet/mobilenet/\",cn=\"https://storage.googleapis.com/tfjs-models/savedmodel/posenet/resnet50/\";function pn(t,e){return function(t,e){return(t-1)%e==0}(t,e)?t:Math.floor(t/e)*e+1}var fn=function(){function t(t,e){this.posenetModel=t;var n=this.posenetModel.inputs[0].shape;i.assert(-1===n[1]&&-1===n[2],(function(){return\"Input shape [\".concat(n[1],\", \").concat(n[2],\"] \")+\"must both be equal to or -1\"}));var r,o,a=(r=e.inputResolution,o=e.outputStride,{height:pn(r.height,o),width:pn(r.width,o)});!function(t){i.assert(_e.indexOf(t)>=0,(function(){return\"outputStride of \".concat(t,\" is invalid. \")+\"It must be either 8 or 16.\"}))}(e.outputStride),function(t,e){i.assert(hn(t.height,e),(function(){return\"height of \".concat(t.height,\" is invalid for output stride \")+\"\".concat(e,\".\")})),i.assert(hn(t.width,e),(function(){return\"width of \".concat(t.width,\" is invalid for output stride \")+\"\".concat(e,\".\")}))}(a,e.outputStride),this.inputResolution=a,this.outputStride=e.outputStride,this.architecture=e.architecture}return t.prototype.estimatePoses=function(t,e){return void 0===e&&(e=Ae),N(this,void 0,void 0,(function(){return D(this,(function(n){return[2,this.estimatePosesGPU(t,e,!1)]}))}))},t.prototype.estimatePosesGPU=function(t,e,n){return void 0===e&&(e=Ae),void 0===n&&(n=!1),N(this,void 0,void 0,(function(){var i,r,a,s,u,h,l,c,d,m,g,y,v,x,w,k,b,M;return D(this,(function(S){switch(S.label){case 0:return i=function(t){var e=t;if(null==e.maxPoses&&(e.maxPoses=1),e.maxPoses<=0)throw new Error(\"Invalid maxPoses \".concat(e.maxPoses,\". Should be > 0.\"));if(e.maxPoses>1){if((e=B(B({},ze),e)).scoreThreshold<0||e.scoreThreshold>1)throw new Error(\"Invalid scoreThreshold \".concat(e.scoreThreshold,\". \")+\"Should be in range [0.0, 1.0]\");if(e.nmsRadius<=0)throw new Error(\"Invalid nmsRadius \".concat(e.nmsRadius,\".\"))}return e}(e),null==t?[2,n?[[],[]]:[]]:(this.maxPoses=i.maxPoses,r=mt(t,{outputTensorSize:this.inputResolution,keepAspectRatio:!0,borderMode:\"replicate\"}),a=r.imageTensor,s=r.padding,u=\"ResNet50\"===this.architecture?o(a,Ce):dt(a,[-1,1]),h=this.posenetModel.predict(u),\"ResNet50\"===this.architecture?(l=p(h[2],[0]),c=p(h[3],[0]),d=p(h[0],[0]),m=p(h[1],[0])):(l=p(h[0],[0]),c=p(h[1],[0]),d=p(h[2],[0]),m=p(h[3],[0])),g=P(c),1!==this.maxPoses?[3,5]:n?[4,un(g,l,this.outputStride)]:[3,2]);case 1:return v=S.sent(),w=v[0],x=v[1],y=[w,x],[3,4];case 2:return[4,sn(g,l,this.outputStride)];case 3:w=S.sent(),y=[w],S.label=4;case 4:return[3,7];case 5:if(n)throw new Error(\"GPU renderer only supports single pose!\");return[4,Ze(g,l,d,m,this.outputStride,this.maxPoses,i.scoreThreshold,i.nmsRadius)];case 6:y=S.sent(),S.label=7;case 7:if(n){if(!0===i.flipHorizontal)throw new Error(\"flipHorizontal is not supported!\");k=this.getCanvasInfo(rt(t),this.inputResolution,s)}else M=rt(t),b=function(t,e,n,i){var r=e.height,o=e.width,a=r/(n.height*(1-i.top-i.bottom)),s=o/(n.width*(1-i.left-i.right)),u=-i.top*n.height,h=-i.left*n.width;if(1===s&&1===a&&0===u&&0===h)return t;for(var l=0,c=t;l<c.length;l++)for(var p=0,f=c[l].keypoints;p<f.length;p++){var d=f[p];d.x=(d.x+h)*s,d.y=(d.y+u)*a}return t}(y,M,this.inputResolution,s),i.flipHorizontal&&(b=function(t,e){for(var n=0,i=t;n<i.length;n++)for(var r=0,o=i[n].keypoints;r<o.length;r++){var a=o[r];a.x=e.width-1-a.x}return t}(b,M));return a.dispose(),u.dispose(),f(h),l.dispose(),c.dispose(),d.dispose(),m.dispose(),g.dispose(),[2,n?[y,k]:b]}}))}))},t.prototype.getCanvasInfo=function(t,e,n){var i=t.height,r=t.width,o=i/(e.height*(1-n.top-n.bottom)),a=r/(e.width*(1-n.left-n.right)),s=-n.top*e.height;return[-n.left*e.width,s,a,o,t.width,t.height]},t.prototype.dispose=function(){this.posenetModel.dispose()},t.prototype.reset=function(){},t}();function dn(t){return void 0===t&&(t=Te),N(this,void 0,void 0,(function(){var e,n,i,r,o;return D(this,(function(a){switch(a.label){case 0:return\"ResNet50\"!==(e=function(t){var e=t||Te;if(null==e.architecture&&(e.architecture=\"MobileNetV1\"),Pe.indexOf(e.architecture)<0)throw new Error(\"Invalid architecture \".concat(e.architecture,\". \")+\"Should be one of \".concat(Pe));if(null==e.inputResolution&&(e.inputResolution={height:257,width:257}),null==e.outputStride&&(e.outputStride=16),Fe[e.architecture].indexOf(e.outputStride)<0)throw new Error(\"Invalid outputStride \".concat(e.outputStride,\". \")+\"Should be one of \".concat(Fe[e.architecture],\" \")+\"for architecture \".concat(e.architecture,\".\"));if(null==e.multiplier&&(e.multiplier=1),Oe[e.architecture].indexOf(e.multiplier)<0)throw new Error(\"Invalid multiplier \".concat(e.multiplier,\". \")+\"Should be one of \".concat(Oe[e.architecture],\" \")+\"for architecture \".concat(e.architecture,\".\"));if(null==e.quantBytes&&(e.quantBytes=4),Ie.indexOf(e.quantBytes)<0)throw new Error(\"Invalid quantBytes \".concat(e.quantBytes,\". \")+\"Should be one of \".concat(Ie,\" \")+\"for architecture \".concat(e.architecture,\".\"));if(\"MobileNetV1\"===e.architecture&&32===e.outputStride&&1!==e.multiplier)throw new Error(\"When using an output stride of 32, you must select 1 as the multiplier.\");return e}(t)).architecture?[3,2]:(s=e.outputStride,u=e.quantBytes,h=\"model-stride\".concat(s,\".json\"),n=4===u?cn+\"float/\"+h:cn+\"quant\".concat(u,\"/\")+h,[4,C(e.modelUrl||n)]);case 1:return i=a.sent(),[2,new fn(i,e)];case 2:return r=function(t,e,n){var i={1:\"100\",.75:\"075\",.5:\"050\"},r=\"model-stride\".concat(t,\".json\");return 4===n?ln+\"float/\".concat(i[e],\"/\")+r:ln+\"quant\".concat(n,\"/\").concat(i[e],\"/\")+r}(e.outputStride,e.multiplier,e.quantBytes),[4,C(e.modelUrl||r)];case 3:return o=a.sent(),[2,new fn(o,e)]}var s,u,h}))}))}function mn(t,e){return N(this,void 0,void 0,(function(){var n,i;return D(this,(function(r){switch(t){case se.PoseNet:return[2,dn(e)];case se.BlazePose:if(i=void 0,null!=(n=e)){if(\"tfjs\"===n.runtime)return[2,oe(e)];if(\"mediapipe\"===n.runtime)return[2,it(e)];i=n.runtime}throw new Error(\"Expect modelConfig.runtime to be either 'tfjs' \"+\"or 'mediapipe', but got \".concat(i));case se.MoveNet:return[2,Se(e)];default:throw new Error(\"\".concat(t,\" is not a supported model name.\"))}}))}))}var gn={keypointsToNormalizedKeypoints:At},yn={modelType:{SINGLEPOSE_LIGHTNING:\"SinglePose.Lightning\",SINGLEPOSE_THUNDER:\"SinglePose.Thunder\",MULTIPOSE_LIGHTNING:\"MultiPose.Lightning\"}};export{se as SupportedModels,ae as TrackerType,gn as calculators,mn as createDetector,yn as movenet,pe as util};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,WAAU;AAKX;AAAa,UAAI;AAAE,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE;AAAE,eAAO,WAAU;AAAC,iBAAO,IAAE,EAAE,SAAO,EAAC,MAAK,OAAG,OAAM,EAAE,GAAG,EAAC,IAAE,EAAC,MAAK,KAAE;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,KAAG,cAAY,OAAO,OAAO,mBAAiB,OAAO,iBAAe,SAAS,GAAE,GAAE,GAAE;AAAC,YAAG,KAAG,MAAM,aAAW,KAAG,OAAO,UAAU,QAAO;AAAE,UAAE,CAAC,IAAE,EAAE;AAAM,eAAO;AAAA,MAAC;AACjR,eAAS,GAAG,GAAE;AAAC,YAAE,CAAC,YAAU,OAAO,cAAY,YAAW,GAAE,YAAU,OAAO,UAAQ,QAAO,YAAU,OAAO,QAAM,MAAK,YAAU,OAAO,UAAQ,MAAM;AAAE,iBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,cAAI,IAAE,EAAE,CAAC;AAAE,cAAG,KAAG,EAAE,QAAM,KAAK,QAAO;AAAA,QAAC;AAAC,cAAM,MAAM,2BAA2B;AAAA,MAAE;AAAC,UAAI,IAAE,GAAG,IAAI;AAAE,eAAS,EAAE,GAAE,GAAE;AAAC,YAAG,EAAE,IAAE;AAAC,cAAI,IAAE;AAAE,cAAE,EAAE,MAAM,GAAG;AAAE,mBAAQ,IAAE,GAAE,IAAE,EAAE,SAAO,GAAE,KAAI;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,gBAAG,EAAE,KAAK,GAAG,OAAM;AAAE,gBAAE,EAAE,CAAC;AAAA,UAAC;AAAC,cAAE,EAAE,EAAE,SAAO,CAAC;AAAE,cAAE,EAAE,CAAC;AAAE,cAAE,EAAE,CAAC;AAAE,eAAG,KAAG,QAAM,KAAG,GAAG,GAAE,GAAE,EAAC,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AACpe,QAAE,UAAS,SAAS,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,cAAG,gBAAgB,EAAE,OAAM,IAAI,UAAU,6BAA6B;AAAE,iBAAO,IAAI,EAAE,KAAG,KAAG,MAAI,MAAI,KAAI,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAE,GAAE,GAAE;AAAC,eAAK,IAAE;AAAE,aAAG,MAAK,eAAc,EAAC,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC;AAAA,QAAC;AAAC,YAAG,EAAE,QAAO;AAAE,UAAE,UAAU,WAAS,WAAU;AAAC,iBAAO,KAAK;AAAA,QAAC;AAAE,YAAI,IAAE,oBAAkB,MAAI,KAAK,OAAO,MAAI,KAAG,KAAI,IAAE;AAAE,eAAO;AAAA,MAAC,CAAC;AAClW,QAAE,mBAAkB,SAAS,GAAE;AAAC,YAAG,EAAE,QAAO;AAAE,YAAE,OAAO,iBAAiB;AAAE,iBAAQ,IAAE,uHAAuH,MAAM,GAAG,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,cAAI,IAAE,EAAE,EAAE,CAAC,CAAC;AAAE,yBAAa,OAAO,KAAG,cAAY,OAAO,EAAE,UAAU,CAAC,KAAG,GAAG,EAAE,WAAU,GAAE,EAAC,cAAa,MAAG,UAAS,MAAG,OAAM,WAAU;AAAC,mBAAO,GAAG,GAAG,IAAI,CAAC;AAAA,UAAC,EAAC,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,YAAE,EAAC,MAAK,EAAC;AAAE,UAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,iBAAO;AAAA,QAAI;AAAE,eAAO;AAAA,MAAC;AAChe,eAAS,EAAE,GAAE;AAAC,YAAI,IAAE,eAAa,OAAO,UAAQ,OAAO,YAAU,EAAE,OAAO,QAAQ;AAAE,eAAO,IAAE,EAAE,KAAK,CAAC,IAAE,EAAC,MAAK,GAAG,CAAC,EAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAG,EAAE,aAAa,QAAO;AAAC,cAAE,EAAE,CAAC;AAAE,mBAAQ,GAAE,IAAE,CAAC,GAAE,EAAE,IAAE,EAAE,KAAK,GAAG,OAAM,GAAE,KAAK,EAAE,KAAK;AAAE,cAAE;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AAAC,UAAI,KAAG,cAAY,OAAO,OAAO,SAAO,OAAO,SAAO,SAAS,GAAE,GAAE;AAAC,iBAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,cAAI,IAAE,UAAU,CAAC;AAAE,cAAG,EAAE,UAAQ,KAAK,EAAE,QAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAA,QAAE;AAAC,eAAO;AAAA,MAAC;AAAE,QAAE,iBAAgB,SAAS,GAAE;AAAC,eAAO,KAAG;AAAA,MAAE,CAAC;AAC/d,UAAI,KAAG,cAAY,OAAO,OAAO,SAAO,OAAO,SAAO,SAAS,GAAE;AAAC,iBAAS,IAAG;AAAA,QAAC;AAAC,UAAE,YAAU;AAAE,eAAO,IAAI;AAAA,MAAC,GAAE;AAAG,UAAG,cAAY,OAAO,OAAO,eAAe,MAAG,OAAO;AAAA,WAAmB;AAAC,YAAI;AAAG,WAAE;AAAC,cAAI,KAAG,EAAC,GAAE,KAAE,GAAE,KAAG,CAAC;AAAE,cAAG;AAAC,eAAG,YAAU;AAAG,iBAAG,GAAG;AAAE,kBAAM;AAAA,UAAC,SAAO,GAAE;AAAA,UAAC;AAAC,eAAG;AAAA,QAAE;AAAC,aAAG,KAAG,SAAS,GAAE,GAAE;AAAC,YAAE,YAAU;AAAE,cAAG,EAAE,cAAY,EAAE,OAAM,IAAI,UAAU,IAAE,oBAAoB;AAAE,iBAAO;AAAA,QAAC,IAAE;AAAA,MAAI;AAAC,UAAI,KAAG;AAClY,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,YAAU,GAAG,EAAE,SAAS;AAAE,UAAE,UAAU,cAAY;AAAE,YAAG,GAAG,IAAG,GAAE,CAAC;AAAA,YAAO,UAAQ,KAAK,EAAE,KAAG,eAAa,EAAE,KAAG,OAAO,kBAAiB;AAAC,cAAI,IAAE,OAAO,yBAAyB,GAAE,CAAC;AAAE,eAAG,OAAO,eAAe,GAAE,GAAE,CAAC;AAAA,QAAC,MAAM,GAAE,CAAC,IAAE,EAAE,CAAC;AAAE,UAAE,KAAG,EAAE;AAAA,MAAS;AAAC,eAAS,KAAI;AAAC,aAAK,IAAE;AAAG,aAAK,IAAE;AAAK,aAAK,IAAE;AAAO,aAAK,IAAE;AAAE,aAAK,IAAE,KAAK,IAAE;AAAE,aAAK,IAAE;AAAA,MAAI;AAAC,eAAS,GAAG,GAAE;AAAC,YAAG,EAAE,EAAE,OAAM,IAAI,UAAU,8BAA8B;AAAE,UAAE,IAAE;AAAA,MAAE;AAAC,SAAG,UAAU,IAAE,SAAS,GAAE;AAAC,aAAK,IAAE;AAAA,MAAC;AAC7c,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,IAAE,EAAC,IAAG,GAAE,IAAG,KAAE;AAAE,UAAE,IAAE,EAAE,KAAG,EAAE;AAAA,MAAC;AAAC,SAAG,UAAU,SAAO,SAAS,GAAE;AAAC,aAAK,IAAE,EAAC,QAAO,EAAC;AAAE,aAAK,IAAE,KAAK;AAAA,MAAC;AAAE,eAASA,GAAE,GAAE,GAAE,GAAE;AAAC,UAAE,IAAE;AAAE,eAAM,EAAC,OAAM,EAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,aAAK,IAAE,IAAI;AAAG,aAAK,IAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,WAAG,EAAE,CAAC;AAAE,YAAI,IAAE,EAAE,EAAE;AAAE,YAAG,EAAE,QAAO,GAAG,GAAE,YAAW,IAAE,EAAE,QAAQ,IAAE,SAAS,GAAE;AAAC,iBAAM,EAAC,OAAM,GAAE,MAAK,KAAE;AAAA,QAAC,GAAE,GAAE,EAAE,EAAE,MAAM;AAAE,UAAE,EAAE,OAAO,CAAC;AAAE,eAAO,GAAG,CAAC;AAAA,MAAC;AAC1V,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAG;AAAC,cAAI,IAAE,EAAE,KAAK,EAAE,EAAE,GAAE,CAAC;AAAE,cAAG,EAAE,aAAa,QAAQ,OAAM,IAAI,UAAU,qBAAmB,IAAE,mBAAmB;AAAE,cAAG,CAAC,EAAE,KAAK,QAAO,EAAE,EAAE,IAAE,OAAG;AAAE,cAAI,IAAE,EAAE;AAAA,QAAK,SAAO,GAAE;AAAC,iBAAO,EAAE,EAAE,IAAE,MAAK,GAAG,EAAE,GAAE,CAAC,GAAE,GAAG,CAAC;AAAA,QAAC;AAAC,UAAE,EAAE,IAAE;AAAK,UAAE,KAAK,EAAE,GAAE,CAAC;AAAE,eAAO,GAAG,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAK,EAAE,EAAE,IAAG,KAAG;AAAC,cAAI,IAAE,EAAE,EAAE,EAAE,CAAC;AAAE,cAAG,EAAE,QAAO,EAAE,EAAE,IAAE,OAAG,EAAC,OAAM,EAAE,OAAM,MAAK,MAAE;AAAA,QAAC,SAAO,GAAE;AAAC,YAAE,EAAE,IAAE,QAAO,GAAG,EAAE,GAAE,CAAC;AAAA,QAAC;AAAC,UAAE,EAAE,IAAE;AAAG,YAAG,EAAE,EAAE,GAAE;AAAC,cAAE,EAAE,EAAE;AAAE,YAAE,EAAE,IAAE;AAAK,cAAG,EAAE,GAAG,OAAM,EAAE;AAAG,iBAAM,EAAC,OAAM,EAAE,QAAO,MAAK,KAAE;AAAA,QAAC;AAAC,eAAM,EAAC,OAAM,QAAO,MAAK,KAAE;AAAA,MAAC;AAClf,eAAS,GAAG,GAAE;AAAC,aAAK,OAAK,SAAS,GAAE;AAAC,aAAG,EAAE,CAAC;AAAE,YAAE,EAAE,IAAE,IAAE,GAAG,GAAE,EAAE,EAAE,EAAE,MAAK,GAAE,EAAE,EAAE,CAAC,KAAG,EAAE,EAAE,EAAE,CAAC,GAAE,IAAE,GAAG,CAAC;AAAG,iBAAO;AAAA,QAAC;AAAE,aAAK,QAAM,SAAS,GAAE;AAAC,aAAG,EAAE,CAAC;AAAE,YAAE,EAAE,IAAE,IAAE,GAAG,GAAE,EAAE,EAAE,EAAE,OAAO,GAAE,GAAE,EAAE,EAAE,CAAC,KAAG,GAAG,EAAE,GAAE,CAAC,GAAE,IAAE,GAAG,CAAC;AAAG,iBAAO;AAAA,QAAC;AAAE,aAAK,SAAO,SAAS,GAAE;AAAC,iBAAO,GAAG,GAAE,CAAC;AAAA,QAAC;AAAE,aAAK,OAAO,QAAQ,IAAE,WAAU;AAAC,iBAAO;AAAA,QAAI;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,iBAAO,EAAE,KAAK,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAE,GAAE;AAAC,iBAAO,EAAE,MAAM,CAAC;AAAA,QAAC;AAAC,eAAO,IAAI,QAAQ,SAAS,GAAE,GAAE;AAAC,mBAAS,EAAE,GAAE;AAAC,cAAE,OAAK,EAAE,EAAE,KAAK,IAAE,QAAQ,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAE,CAAC,EAAE,KAAK,GAAE,CAAC;AAAA,UAAC;AAAC,YAAE,EAAE,KAAK,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC5e,eAAS,EAAE,GAAE;AAAC,eAAO,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;AAAA,MAAC;AAC1C,QAAE,WAAU,SAAS,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,eAAK,IAAE;AAAE,eAAK,IAAE;AAAO,eAAK,IAAE,CAAC;AAAE,eAAK,IAAE;AAAG,cAAI,IAAE,KAAK,EAAE;AAAE,cAAG;AAAC,cAAE,EAAE,SAAQ,EAAE,MAAM;AAAA,UAAC,SAAO,GAAE;AAAC,cAAE,OAAO,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAC,eAAK,IAAE;AAAA,QAAI;AAAC,iBAAS,EAAE,GAAE;AAAC,iBAAO,aAAa,IAAE,IAAE,IAAI,EAAE,SAAS,GAAE;AAAC,cAAE,CAAC;AAAA,UAAC,CAAC;AAAA,QAAC;AAAC,YAAG,EAAE,QAAO;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,cAAG,QAAM,KAAK,GAAE;AAAC,iBAAK,IAAE,CAAC;AAAE,gBAAI,IAAE;AAAK,iBAAK,EAAE,WAAU;AAAC,gBAAE,EAAE;AAAA,YAAC,CAAC;AAAA,UAAC;AAAC,eAAK,EAAE,KAAK,CAAC;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAW,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,YAAE,GAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,WAAU;AAAC,iBAAK,KAAK,KAAG,KAAK,EAAE,UAAQ;AAAC,gBAAI,IAAE,KAAK;AAAE,iBAAK,IAAE,CAAC;AAAE,qBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,kBAAI,IAClgB,EAAE,CAAC;AAAE,gBAAE,CAAC,IAAE;AAAK,kBAAG;AAAC,kBAAE;AAAA,cAAC,SAAO,GAAE;AAAC,qBAAK,EAAE,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAC,eAAK,IAAE;AAAA,QAAI;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,eAAK,EAAE,WAAU;AAAC,kBAAM;AAAA,UAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,WAAU;AAAC,mBAAS,EAAE,GAAE;AAAC,mBAAO,SAAS,GAAE;AAAC,oBAAI,IAAE,MAAG,EAAE,KAAK,GAAE,CAAC;AAAA,YAAE;AAAA,UAAC;AAAC,cAAI,IAAE,MAAK,IAAE;AAAG,iBAAM,EAAC,SAAQ,EAAE,KAAK,CAAC,GAAE,QAAO,EAAE,KAAK,CAAC,EAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,cAAG,MAAI,KAAK,MAAK,EAAE,IAAI,UAAU,oCAAoC,CAAC;AAAA,mBAAU,aAAa,EAAE,MAAK,EAAE,CAAC;AAAA,eAAM;AAAC,cAAE,SAAO,OAAO,GAAE;AAAA,cAAC,KAAK;AAAS,oBAAI,IAAE,QAAM;AAAE,sBAAM;AAAA,cAAE,KAAK;AAAW,oBAAE;AAAG,sBAAM;AAAA,cAAE;AAAQ,oBAAE;AAAA,YAAE;AAAC,gBAAE,KAAK,EAAE,CAAC,IAAE,KAAK,EAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAC7f,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,cAAI,IAAE;AAAO,cAAG;AAAC,gBAAE,EAAE;AAAA,UAAI,SAAO,GAAE;AAAC,iBAAK,EAAE,CAAC;AAAE;AAAA,UAAM;AAAC,wBAAY,OAAO,IAAE,KAAK,EAAE,GAAE,CAAC,IAAE,KAAK,EAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,eAAK,EAAE,GAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE;AAAC,eAAK,EAAE,GAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE,GAAE;AAAC,cAAG,KAAG,KAAK,EAAE,OAAM,MAAM,mBAAiB,IAAE,OAAK,IAAE,wCAAsC,KAAK,CAAC;AAAE,eAAK,IAAE;AAAE,eAAK,IAAE;AAAE,gBAAI,KAAK,KAAG,KAAK,EAAE;AAAE,eAAK,EAAE;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,WAAU;AAAC,cAAI,IAAE;AAAK,YAAE,WAAU;AAAC,gBAAG,EAAE,EAAE,GAAE;AAAC,kBAAI,IAAE,EAAE;AAAQ,8BAAc,OAAO,KAAG,EAAE,MAAM,EAAE,CAAC;AAAA,YAAC;AAAA,UAAC,GAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IACxf,WAAU;AAAC,cAAG,KAAK,EAAE,QAAM;AAAG,cAAI,IAAE,EAAE,aAAY,IAAE,EAAE,OAAM,IAAE,EAAE;AAAc,cAAG,gBAAc,OAAO,EAAE,QAAM;AAAG,yBAAa,OAAO,IAAE,IAAE,IAAI,EAAE,sBAAqB,EAAC,YAAW,KAAE,CAAC,IAAE,eAAa,OAAO,IAAE,IAAE,IAAI,EAAE,sBAAqB,EAAC,YAAW,KAAE,CAAC,KAAG,IAAE,EAAE,SAAS,YAAY,aAAa,GAAE,EAAE,gBAAgB,sBAAqB,OAAG,MAAG,CAAC;AAAG,YAAE,UAAQ;AAAK,YAAE,SAAO,KAAK;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,WAAU;AAAC,cAAG,QAAM,KAAK,GAAE;AAAC,qBAAQ,IAAE,GAAE,IAAE,KAAK,EAAE,QAAO,EAAE,EAAE,GAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AAAE,iBAAK,IAAE;AAAA,UAAI;AAAA,QAAC;AAAE,YAAI,IAAE,IAAI;AAAE,UAAE,UAAU,IAC9f,SAAS,GAAE;AAAC,cAAI,IAAE,KAAK,EAAE;AAAE,YAAE,EAAE,EAAE,SAAQ,EAAE,MAAM;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE,GAAE;AAAC,cAAI,IAAE,KAAK,EAAE;AAAE,cAAG;AAAC,cAAE,KAAK,GAAE,EAAE,SAAQ,EAAE,MAAM;AAAA,UAAC,SAAO,GAAE;AAAC,cAAE,OAAO,CAAC;AAAA,UAAC;AAAA,QAAC;AAAE,UAAE,UAAU,OAAK,SAAS,GAAE,GAAE;AAAC,mBAAS,EAAE,GAAE,GAAE;AAAC,mBAAM,cAAY,OAAO,IAAE,SAASC,IAAE;AAAC,kBAAG;AAAC,kBAAE,EAAEA,EAAC,CAAC;AAAA,cAAC,SAAOC,IAAE;AAAC,kBAAEA,EAAC;AAAA,cAAC;AAAA,YAAC,IAAE;AAAA,UAAC;AAAC,cAAI,GAAE,GAAE,IAAE,IAAI,EAAE,SAAS,GAAE,GAAE;AAAC,gBAAE;AAAE,gBAAE;AAAA,UAAC,CAAC;AAAE,eAAK,EAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,CAAC;AAAE,iBAAO;AAAA,QAAC;AAAE,UAAE,UAAU,QAAM,SAAS,GAAE;AAAC,iBAAO,KAAK,KAAK,QAAO,CAAC;AAAA,QAAC;AAAE,UAAE,UAAU,IAAE,SAAS,GAAE,GAAE;AAAC,mBAAS,IAAG;AAAC,oBAAO,EAAE,GAAE;AAAA,cAAC,KAAK;AAAE,kBAAE,EAAE,CAAC;AAAE;AAAA,cAAM,KAAK;AAAE,kBAAE,EAAE,CAAC;AAAE;AAAA,cAAM;AAAQ,sBAAM,MAAM,uBAC9f,EAAE,CAAC;AAAA,YAAE;AAAA,UAAC;AAAC,cAAI,IAAE;AAAK,kBAAM,KAAK,IAAE,EAAE,EAAE,CAAC,IAAE,KAAK,EAAE,KAAK,CAAC;AAAE,eAAK,IAAE;AAAA,QAAE;AAAE,UAAE,UAAQ;AAAE,UAAE,SAAO,SAAS,GAAE;AAAC,iBAAO,IAAI,EAAE,SAAS,GAAE,GAAE;AAAC,cAAE,CAAC;AAAA,UAAC,CAAC;AAAA,QAAC;AAAE,UAAE,OAAK,SAAS,GAAE;AAAC,iBAAO,IAAI,EAAE,SAAS,GAAE,GAAE;AAAC,qBAAQ,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,GAAE,EAAE,KAAK,EAAE,EAAE,GAAE,CAAC;AAAA,UAAC,CAAC;AAAA,QAAC;AAAE,UAAE,MAAI,SAAS,GAAE;AAAC,cAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,KAAK;AAAE,iBAAO,EAAE,OAAK,EAAE,CAAC,CAAC,IAAE,IAAI,EAAE,SAAS,GAAE,GAAE;AAAC,qBAAS,EAAED,IAAE;AAAC,qBAAO,SAASC,IAAE;AAAC,kBAAED,EAAC,IAAEC;AAAE;AAAI,qBAAG,KAAG,EAAE,CAAC;AAAA,cAAC;AAAA,YAAC;AAAC,gBAAI,IAAE,CAAC,GAAE,IAAE;AAAE;AAAG,gBAAE,KAAK,MAAM,GAAE,KAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAO,CAAC,GAAE,CAAC,GAAE,IAAE,EAAE,KAAK;AAAA,mBAAQ,CAAC,EAAE;AAAA,UAAK,CAAC;AAAA,QAAC;AAAE,eAAO;AAAA,MAAC,CAAC;AACne,eAAS,GAAG,GAAE,GAAE;AAAC,qBAAa,WAAS,KAAG;AAAI,YAAI,IAAE,GAAE,IAAE,OAAG,IAAE,EAAC,MAAK,WAAU;AAAC,cAAG,CAAC,KAAG,IAAE,EAAE,QAAO;AAAC,gBAAI,IAAE;AAAI,mBAAM,EAAC,OAAM,EAAE,GAAE,EAAE,CAAC,CAAC,GAAE,MAAK,MAAE;AAAA,UAAC;AAAC,cAAE;AAAG,iBAAM,EAAC,MAAK,MAAG,OAAM,OAAM;AAAA,QAAC,EAAC;AAAE,UAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,iBAAO;AAAA,QAAC;AAAE,eAAO;AAAA,MAAC;AAAC,QAAE,wBAAuB,SAAS,GAAE;AAAC,eAAO,IAAE,IAAE,WAAU;AAAC,iBAAO,GAAG,MAAK,SAAS,GAAE;AAAC,mBAAO;AAAA,UAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAC;AAClU,QAAE,wBAAuB,SAAS,GAAE;AAAC,eAAO,IAAE,IAAE,SAAS,GAAE,GAAE,GAAE;AAAC,cAAI,IAAE,KAAK,UAAQ;AAAE,cAAE,MAAI,IAAE,KAAK,IAAI,GAAE,IAAE,CAAC;AAAG,cAAG,QAAM,KAAG,IAAE,EAAE,KAAE;AAAE,cAAE,OAAO,CAAC;AAAE,cAAE,MAAI,IAAE,KAAK,IAAI,GAAE,IAAE,CAAC;AAAG,eAAI,IAAE,OAAO,KAAG,CAAC,GAAE,IAAE,GAAE,IAAI,MAAK,CAAC,IAAE;AAAE,iBAAO;AAAA,QAAI;AAAA,MAAC,CAAC;AAAE,eAAS,EAAE,GAAE;AAAC,eAAO,IAAE,IAAE,MAAM,UAAU;AAAA,MAAI;AAAC,QAAE,4BAA2B,CAAC;AAAE,QAAE,6BAA4B,CAAC;AAAE,QAAE,oCAAmC,CAAC;AAAE,QAAE,6BAA4B,CAAC;AAAE,QAAE,8BAA6B,CAAC;AAAE,QAAE,6BAA4B,CAAC;AACpd,QAAE,8BAA6B,CAAC;AAAE,QAAE,+BAA8B,CAAC;AAAE,QAAE,+BAA8B,CAAC;AAAE,QAAE,aAAY,SAAS,GAAE;AAAC,eAAO,IAAE,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAO,MAAI,IAAE,MAAI,KAAG,IAAE,MAAI,IAAE,IAAE,MAAI,KAAG,MAAI;AAAA,QAAC;AAAA,MAAC,CAAC;AAAE,QAAE,4BAA2B,SAAS,GAAE;AAAC,eAAO,IAAE,IAAE,SAAS,GAAE,GAAE;AAAC,cAAI,IAAE;AAAK,uBAAa,WAAS,IAAE,OAAO,CAAC;AAAG,cAAI,IAAE,EAAE;AAAO,cAAE,KAAG;AAAE,eAAI,IAAE,MAAI,IAAE,KAAK,IAAI,IAAE,GAAE,CAAC,IAAG,IAAE,GAAE,KAAI;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,gBAAG,MAAI,KAAG,OAAO,GAAG,GAAE,CAAC,EAAE,QAAM;AAAA,UAAE;AAAC,iBAAM;AAAA,QAAE;AAAA,MAAC,CAAC;AAC5a,QAAE,6BAA4B,SAAS,GAAE;AAAC,eAAO,IAAE,IAAE,SAAS,GAAE,GAAE;AAAC,cAAG,QAAM,KAAK,OAAM,IAAI,UAAU,8EAA8E;AAAE,cAAG,aAAa,OAAO,OAAM,IAAI,UAAU,8EAA8E;AAAE,iBAAM,OAAK,KAAK,QAAQ,GAAE,KAAG,CAAC;AAAA,QAAC;AAAA,MAAC,CAAC;AAAE,UAAI,KAAG,QAAM;AAChW,eAASC,GAAE,GAAE,GAAE;AAAC,YAAE,EAAE,MAAM,GAAG;AAAE,YAAI,IAAE;AAAG,UAAE,CAAC,KAAI,KAAG,eAAa,OAAO,EAAE,cAAY,EAAE,WAAW,SAAO,EAAE,CAAC,CAAC;AAAE,iBAAQ,GAAE,EAAE,WAAS,IAAE,EAAE,MAAM,KAAI,GAAE,UAAQ,WAAS,IAAE,EAAE,CAAC,KAAG,EAAE,CAAC,MAAI,OAAO,UAAU,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI;AAAE,WAAE;AAAC,cAAG,IAAE,GAAG;AAAU,gBAAG,IAAE,EAAE,UAAU,OAAM;AAAA;AAAE,cAAE;AAAA,QAAE;AAAC,eAAM,MAAI,EAAE,QAAQ,CAAC;AAAA,MAAC;AAAC;AAAC,UAAI,KAAG,MAAM,UAAU,MAAI,SAAS,GAAE,GAAE;AAAC,eAAO,MAAM,UAAU,IAAI,KAAK,GAAE,GAAE,MAAM;AAAA,MAAC,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAQ,IAAE,EAAE,QAAO,IAAE,MAAM,CAAC,GAAE,IAAE,aAAW,OAAO,IAAE,EAAE,MAAM,EAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI,MAAK,MAAI,EAAE,CAAC,IAAE,EAAE,KAAK,QAAO,EAAE,CAAC,GAAE,GAAE,CAAC;AAAG,eAAO;AAAA,MAAC;AAAE,UAAI,KAAG,CAAC,GAAE,KAAG;AAAK,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,QAAO,IAAE,IAAE,IAAE;AAAE,YAAE,IAAE,IAAE,KAAK,MAAM,CAAC,IAAE,MAAI,KAAK,QAAQ,EAAE,IAAE,CAAC,CAAC,MAAI,IAAE,MAAI,KAAK,QAAQ,EAAE,IAAE,CAAC,CAAC,IAAE,IAAE,IAAE,IAAE;AAAG,YAAI,IAAE,IAAI,WAAW,CAAC,GAAE,IAAE;AAAE,WAAG,GAAE,SAAS,GAAE;AAAC,YAAE,GAAG,IAAE;AAAA,QAAC,CAAC;AAAE,eAAO,MAAI,IAAE,EAAE,SAAS,GAAE,CAAC,IAAE;AAAA,MAAC;AACpwB,eAAS,GAAG,GAAE,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,iBAAK,IAAE,EAAE,UAAQ;AAAC,gBAAI,IAAE,EAAE,OAAO,GAAG,GAAE,IAAE,GAAG,CAAC;AAAE,gBAAG,QAAM,EAAE,QAAO;AAAE,gBAAG,CAAC,cAAc,KAAK,CAAC,EAAE,OAAM,MAAM,sCAAoC,CAAC;AAAA,UAAE;AAAC,iBAAO;AAAA,QAAC;AAAC,WAAG;AAAE,iBAAQ,IAAE,OAAI;AAAC,cAAI,IAAE,EAAE,EAAE,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,EAAE,GAAE,IAAE,EAAE,EAAE;AAAE,cAAG,OAAK,KAAG,OAAK,EAAE;AAAM,YAAE,KAAG,IAAE,KAAG,CAAC;AAAE,gBAAI,MAAI,EAAE,KAAG,IAAE,MAAI,KAAG,CAAC,GAAE,MAAI,KAAG,EAAE,KAAG,IAAE,MAAI,CAAC;AAAA,QAAE;AAAA,MAAC;AACnU,eAAS,KAAI;AAAC,YAAG,CAAC,IAAG;AAAC,eAAG,CAAC;AAAE,mBAAQ,IAAE,iEAAiE,MAAM,EAAE,GAAE,IAAE,CAAC,OAAM,MAAK,OAAM,OAAM,IAAI,GAAE,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,gBAAI,IAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;AAAE,eAAG,CAAC,IAAE;AAAE,qBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,kBAAI,IAAE,EAAE,CAAC;AAAE,yBAAS,GAAG,CAAC,MAAI,GAAG,CAAC,IAAE;AAAA,YAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC;AAAC,UAAI,KAAG,gBAAc,OAAO,YAAW,KAAG,EAAE,GAAG,SAAS,KAAG,GAAG,MAAM,MAAI,eAAa,OAAO,GAAG;AACzW,eAAS,GAAG,GAAE;AAAC,YAAG,CAAC,IAAG;AAAC,cAAI;AAAE,qBAAS,MAAI,IAAE;AAAG,aAAG;AAAE,cAAE,GAAG,CAAC;AAAE,mBAAQ,IAAE,MAAM,KAAK,MAAM,EAAE,SAAO,CAAC,CAAC,GAAE,IAAE,EAAE,EAAE,KAAG,IAAG,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,SAAO,GAAE,KAAG,GAAE;AAAC,gBAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,IAAE,CAAC,GAAE,IAAE,EAAE,IAAE,CAAC,GAAE,IAAE,EAAE,KAAG,CAAC;AAAE,gBAAE,GAAG,IAAE,MAAI,IAAE,KAAG,CAAC;AAAE,gBAAE,GAAG,IAAE,OAAK,IAAE,KAAG,CAAC;AAAE,gBAAE,EAAE,IAAE,EAAE;AAAE,cAAE,GAAG,IAAE,IAAE,IAAE,IAAE;AAAA,UAAC;AAAC,cAAE;AAAE,cAAE;AAAE,kBAAO,EAAE,SAAO,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAE,EAAE,IAAE,CAAC,GAAE,IAAE,GAAG,IAAE,OAAK,CAAC,KAAG;AAAA,YAAE,KAAK;AAAE,kBAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,KAAG,CAAC,IAAE,GAAG,IAAE,MAAI,IAAE,KAAG,CAAC,IAAE,IAAE;AAAA,UAAC;AAAC,iBAAO,EAAE,KAAK,EAAE;AAAA,QAAC;AAAC,aAAI,IAAE,IAAG,QAAM,EAAE,SAAQ,MAAG,OAAO,aAAa,MAAM,MAAK,EAAE,SAAS,GAAE,KAAK,CAAC,GAAE,IAAE,EAAE,SAAS,KAAK;AAAE,aAAG,OAAO,aAAa;AAAA,UAAM;AAAA,UAClf;AAAA,QAAC;AAAE,eAAO,KAAK,CAAC;AAAA,MAAC;AAAC,UAAI,KAAG,OAAO,SAAQ,GAAG;AAAE,eAAS,GAAG,GAAE;AAAC,gBAAO,GAAE;AAAA,UAAC,KAAK;AAAI,mBAAM;AAAA,UAAI,KAAK;AAAI,mBAAM;AAAA,UAAI,KAAK;AAAI,mBAAM;AAAA,UAAI;AAAQ,mBAAM;AAAA,QAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAG,CAAC,GAAG,QAAO,GAAG,CAAC;AAAE,WAAG,KAAK,CAAC,MAAI,IAAE,EAAE,QAAQ,IAAG,EAAE;AAAG,YAAE,KAAK,CAAC;AAAE,iBAAQ,IAAE,IAAI,WAAW,EAAE,MAAM,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,IAAE,EAAE,WAAW,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,UAAI;AAAG,eAAS,KAAI;AAAC,eAAO,OAAK,KAAG,IAAI,WAAW,CAAC;AAAA,MAAE;AAAC,UAAI,KAAG,CAAC;AAAE,UAAI,KAAG,eAAa,OAAO,WAAW,UAAU,OAAMC,KAAE,GAAEC,KAAE;AAAE,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,IAAE;AAAE,YAAE,KAAK,IAAI,CAAC;AAAE,YAAI,IAAE,MAAI;AAAE,YAAE,KAAK,OAAO,IAAE,KAAG,UAAU;AAAE,cAAI,IAAE,EAAE,GAAG,GAAE,CAAC,CAAC,GAAE,IAAE,EAAE,KAAK,EAAE,OAAM,IAAE,EAAE,KAAK,EAAE,OAAM,IAAE;AAAG,QAAAD,KAAE,MAAI;AAAE,QAAAC,KAAE,MAAI;AAAA,MAAC;AAAC,UAAI,KAAG,eAAa,OAAO;AAAO,eAAS,GAAG,GAAE,GAAE;AAAC,YAAE,CAAC;AAAE,YAAE,IAAE,CAAC,IAAE,IAAE,KAAG;AAAE,eAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,aAAK,IAAE,MAAI;AAAE,aAAK,IAAE,MAAI;AAAA,MAAC;AACzsB,eAAS,GAAG,GAAE;AAAC,YAAG,CAAC,EAAE,QAAO,OAAK,KAAG,IAAI,GAAG,GAAE,CAAC;AAAG,YAAG,CAAC,UAAU,KAAK,CAAC,EAAE,QAAO;AAAK,YAAG,KAAG,EAAE,OAAO,IAAG,OAAO,CAAC,CAAC;AAAA,iBAAU,GAAG,KAAE,OAAO,CAAC,GAAED,KAAE,OAAO,IAAE,OAAO,UAAU,CAAC,MAAI,GAAEC,KAAE,OAAO,KAAG,OAAO,EAAE,IAAE,OAAO,UAAU,CAAC;AAAA,aAAM;AAAC,cAAI,IAAE,EAAE,QAAM,EAAE,CAAC;AAAG,UAAAA,KAAED,KAAE;AAAE,mBAAQ,IAAE,EAAE,QAAO,IAAE,GAAE,KAAG,IAAE,KAAG,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,EAAE,KAAE,OAAO,EAAE,MAAM,GAAE,CAAC,CAAC,GAAEC,MAAG,KAAID,KAAE,MAAIA,KAAE,GAAE,cAAYA,OAAIC,MAAGD,KAAE,aAAW,GAAEA,MAAG;AAAY,gBAAI,IAAE,EAAE,GAAGA,IAAEC,EAAC,CAAC,GAAE,IAAE,EAAE,KAAK,EAAE,OAAM,IAAE,EAAE,KAAK,EAAE,OAAMD,KAAE,GAAEC,KAAE;AAAA,QAAE;AAAC,eAAO,IAAI,GAAGD,IAAEC,EAAC;AAAA,MAAC;AAAC,UAAI;AAAG,eAAS,GAAG,GAAE,GAAE;AAAC,eAAO,MAAM,wBAAsB,IAAE,mBAAiB,IAAE,GAAG;AAAA,MAAC;AAAC,eAAS,KAAI;AAAC,eAAO,MAAM,6CAA6C;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,eAAO,MAAM,4CAA0C,IAAE,QAAM,CAAC;AAAA,MAAC;AAAC;AAAC,eAASC,KAAG;AAAC,cAAM,MAAM,cAAc;AAAA,MAAE;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAE,OAAO,aAAa,MAAM,MAAK,CAAC;AAAE,eAAO,QAAM,IAAE,IAAE,IAAE;AAAA,MAAC;AAAC,UAAI,KAAG,QAAO,IAAG,KAAG,gBAAc,OAAO,aAAY,IAAG,KAAG,gBAAc,OAAO;AAAY,UAAI;AAAG,eAAS,GAAG,GAAE;AAAC,YAAG,MAAI,GAAG,OAAM,MAAM,yBAAyB;AAAA,MAAE;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,WAAG,CAAC;AAAE,aAAK,IAAE;AAAE,YAAG,QAAM,KAAG,MAAI,EAAE,OAAO,OAAM,MAAM,wDAAwD;AAAA,MAAE;AAAC,eAAS,KAAI;AAAC,eAAO,OAAK,KAAG,IAAI,GAAG,MAAK,EAAE;AAAA,MAAE;AAAC,eAAS,GAAG,GAAE;AAAC,WAAG,EAAE;AAAE,YAAI,IAAE,EAAE;AAAE,YAAE,QAAM,KAAG,MAAI,QAAM,KAAG,aAAa,aAAW,IAAE,aAAW,OAAO,IAAE,GAAG,CAAC,IAAE;AAAK,eAAO,QAAM,IAAE,IAAE,EAAE,IAAE;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAG,aAAW,OAAO,EAAE,QAAM,EAAC,QAAO,GAAG,CAAC,GAAE,GAAE,MAAE;AAAE,YAAG,MAAM,QAAQ,CAAC,EAAE,QAAM,EAAC,QAAO,IAAI,WAAW,CAAC,GAAE,GAAE,MAAE;AAAE,YAAG,EAAE,gBAAc,WAAW,QAAM,EAAC,QAAO,GAAE,GAAE,MAAE;AAAE,YAAG,EAAE,gBAAc,YAAY,QAAM,EAAC,QAAO,IAAI,WAAW,CAAC,GAAE,GAAE,MAAE;AAAE,YAAG,EAAE,gBAAc,GAAG,QAAM,EAAC,QAAO,GAAG,CAAC,KAAG,GAAG,GAAE,GAAE,KAAE;AAAE,YAAG,aAAa,WAAW,QAAM,EAAC,QAAO,IAAI,WAAW,EAAE,QAAO,EAAE,YAAW,EAAE,UAAU,GAAE,GAAE,MAAE;AAAE,cAAM,MAAM,2IAA2I;AAAA,MACjzD;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,aAAK,IAAE;AAAK,aAAK,IAAE;AAAG,aAAK,IAAE,KAAK,IAAE,KAAK,IAAE;AAAE,WAAG,MAAK,GAAE,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAE,WAAS,IAAE,CAAC,IAAE;AAAE,UAAE,IAAE,WAAS,EAAE,IAAE,QAAG,EAAE;AAAE,cAAI,IAAE,GAAG,CAAC,GAAE,EAAE,IAAE,EAAE,QAAO,EAAE,IAAE,EAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,EAAE,EAAE,QAAO,EAAE,IAAE,EAAE;AAAA,MAAE;AAAC,SAAG,UAAU,QAAM,WAAU;AAAC,aAAK,IAAE,KAAK;AAAA,MAAC;AAAE,eAAS,EAAE,GAAE,GAAE;AAAC,UAAE,IAAE;AAAE,YAAG,IAAE,EAAE,EAAE,OAAM,GAAG,EAAE,GAAE,CAAC;AAAA,MAAE;AACrS,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,IAAE;AAAI,YAAG,IAAE,QAAM,IAAE,EAAE,GAAG,GAAE,MAAI,IAAE,QAAM,GAAE,IAAE,QAAM,IAAE,EAAE,GAAG,GAAE,MAAI,IAAE,QAAM,IAAG,IAAE,QAAM,IAAE,EAAE,GAAG,GAAE,MAAI,IAAE,QAAM,IAAG,IAAE,QAAM,IAAE,EAAE,GAAG,GAAE,KAAG,KAAG,IAAG,IAAE,OAAK,EAAE,GAAG,IAAE,OAAK,EAAE,GAAG,IAAE,OAAK,EAAE,GAAG,IAAE,OAAK,EAAE,GAAG,IAAE,OAAK,EAAE,GAAG,IAAE,QAAQ,OAAM,GAAG;AAAE,UAAE,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAG,IAAE,EAAE,OAAM,MAAM,2CAAyC,CAAC;AAAE,YAAI,IAAE,EAAE,GAAE,IAAE,IAAE;AAAE,YAAG,IAAE,EAAE,EAAE,OAAM,GAAG,GAAE,EAAE,IAAE,CAAC;AAAE,UAAE,IAAE;AAAE,eAAO;AAAA,MAAC;AAAC,UAAI,KAAG,CAAC;AAAE,eAAS,KAAI;AAAC,aAAK,IAAE,CAAC;AAAA,MAAC;AAAC,SAAG,UAAU,SAAO,WAAU;AAAC,eAAO,KAAK,EAAE;AAAA,MAAM;AAAE,SAAG,UAAU,MAAI,WAAU;AAAC,YAAI,IAAE,KAAK;AAAE,aAAK,IAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAE,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,eAAK,IAAE,KAAG,MAAI,IAAG,GAAE,EAAE,KAAK,IAAE,MAAI,GAAG,GAAE,KAAG,MAAI,IAAE,KAAG,QAAM,GAAE,OAAK;AAAE,UAAE,EAAE,KAAK,CAAC;AAAA,MAAC;AAAC,eAASC,GAAE,GAAE,GAAE;AAAC,eAAK,MAAI,IAAG,GAAE,EAAE,KAAK,IAAE,MAAI,GAAG,GAAE,OAAK;AAAE,UAAE,EAAE,KAAK,CAAC;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAG,GAAG,QAAO;AAAC,cAAI,IAAE,GAAG,IAAI;AAAE,aAAG,GAAE,GAAE,CAAC;AAAE,cAAE;AAAA,QAAC,MAAM,KAAE,IAAI,GAAG,GAAE,CAAC;AAAE,aAAK,IAAE;AAAE,aAAK,IAAE,KAAK,EAAE;AAAE,aAAK,IAAE,KAAK,IAAE;AAAG,aAAK,WAAW,CAAC;AAAA,MAAC;AAAC,SAAG,UAAU,aAAW,SAAS,GAAE;AAAC,YAAE,WAAS,IAAE,CAAC,IAAE;AAAE,aAAK,KAAG,WAAS,EAAE,KAAG,QAAG,EAAE;AAAA,MAAE;AAAE,SAAG,UAAU,QAAM,WAAU;AAAC,aAAK,EAAE,MAAM;AAAE,aAAK,IAAE,KAAK,EAAE;AAAE,aAAK,IAAE,KAAK,IAAE;AAAA,MAAE;AAC5/B,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE;AAAE,YAAG,EAAE,KAAG,EAAE,EAAE,QAAM;AAAG,UAAE,IAAE,EAAE,EAAE;AAAE,YAAI,IAAE,GAAG,EAAE,CAAC,MAAI;AAAE,YAAE,MAAI;AAAE,aAAG;AAAE,YAAG,EAAE,KAAG,KAAG,KAAG,GAAG,OAAM,GAAG,GAAE,EAAE,CAAC;AAAE,YAAG,IAAE,EAAE,OAAM,MAAM,2BAAyB,IAAE,mBAAiB,EAAE,IAAE,GAAG;AAAE,UAAE,IAAE;AAAE,UAAE,IAAE;AAAE,eAAM;AAAA,MAAE;AACpN,eAAS,GAAG,GAAE;AAAC,gBAAO,EAAE,GAAE;AAAA,UAAC,KAAK;AAAE,gBAAG,KAAG,EAAE,EAAE,IAAG,CAAC;AAAA,gBAAO,IAAE;AAAC,kBAAE,EAAE;AAAE,uBAAQ,IAAE,EAAE,GAAE,IAAE,IAAE,IAAG,IAAE,EAAE,GAAE,IAAE,IAAG,KAAG,OAAK,EAAE,GAAG,IAAE,MAAK;AAAC,kBAAE,GAAE,CAAC;AAAE,sBAAM;AAAA,cAAC;AAAC,oBAAM,GAAG;AAAA,YAAE;AAAC;AAAA,UAAM,KAAK;AAAE,gBAAE,EAAE;AAAE,cAAE,GAAE,EAAE,IAAE,CAAC;AAAE;AAAA,UAAM,KAAK;AAAE,iBAAG,EAAE,IAAE,GAAG,CAAC,KAAG,IAAE,GAAG,EAAE,CAAC,MAAI,GAAE,IAAE,EAAE,GAAE,EAAE,GAAE,EAAE,IAAE,CAAC;AAAG;AAAA,UAAM,KAAK;AAAE,gBAAE,EAAE;AAAE,cAAE,GAAE,EAAE,IAAE,CAAC;AAAE;AAAA,UAAM,KAAK;AAAE,gBAAE,EAAE;AAAE,eAAE;AAAC,kBAAG,CAAC,GAAG,CAAC,EAAE,OAAM,MAAM,uCAAuC;AAAE,kBAAG,KAAG,EAAE,GAAE;AAAC,oBAAG,EAAE,KAAG,EAAE,OAAM,MAAM,yBAAyB;AAAE;AAAA,cAAK;AAAC,iBAAG,CAAC;AAAA,YAAC,SAAO;AAAG;AAAA,UAAM;AAAQ,kBAAM,GAAG,EAAE,GAAE,EAAE,CAAC;AAAA,QAAE;AAAA,MAAC;AAAC,UAAI,KAAG,CAAC;AAAE,eAAS,KAAI;AAAC,aAAK,IAAE,CAAC;AAAE,aAAK,IAAE;AAAE,aAAK,IAAE,IAAI;AAAA,MAAE;AAAC,eAAS,EAAE,GAAE,GAAE;AAAC,cAAI,EAAE,WAAS,EAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAG,EAAE;AAAA,MAAO;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAG,IAAE,EAAE,GAAE;AAAC,YAAE,GAAE,EAAE,EAAE,IAAI,CAAC;AAAE,mBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,GAAE,GAAG,EAAE,CAAC,CAAC,KAAG,GAAG,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC;AAAC,UAAI,IAAE,eAAa,OAAO,UAAQ,aAAW,OAAO,OAAO,IAAE,OAAO,IAAE;AAAO,eAASC,GAAE,GAAE,GAAE;AAAC,YAAG,EAAE,QAAO,EAAE,CAAC,KAAG;AAAE,YAAG,WAAS,EAAE,EAAE,QAAO,EAAE,KAAG;AAAE,eAAO,iBAAiB,GAAE,EAAC,GAAE,EAAC,OAAM,GAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAE,EAAE,CAAC,MAAI,EAAE,CAAC,KAAG,CAAC,KAAG,WAAS,EAAE,MAAI,EAAE,KAAG,CAAC;AAAA,MAAE;AAAC,eAAS,EAAE,GAAE;AAAC,YAAI;AAAE,YAAE,IAAE,EAAE,CAAC,IAAE,IAAE,EAAE;AAAE,eAAO,QAAM,IAAE,IAAE;AAAA,MAAC;AAAC,eAAS,EAAE,GAAE,GAAE;AAAC,YAAE,EAAE,CAAC,IAAE,IAAE,WAAS,EAAE,IAAE,EAAE,IAAE,IAAE,OAAO,iBAAiB,GAAE,EAAC,GAAE,EAAC,OAAM,GAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC,CAAC;AAAA,MAAC;AACxnC,eAAS,GAAG,GAAE;AAAC,QAAAA,GAAE,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,IAAG,IAAE,KAAG,GAAG;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,IAAG,IAAE,MAAI,GAAG;AAAA,MAAC;AAAC;AAAC,UAAI,KAAG,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,eAAO,SAAO,KAAG,aAAW,OAAO,KAAG,CAAC,MAAM,QAAQ,CAAC,KAAG,EAAE,gBAAc;AAAA,MAAM;AAAC,UAAI,IAAG,KAAG,CAAC;AAAE,QAAE,IAAG,EAAE;AAAE,WAAG,OAAO,OAAO,EAAE;AAAE,eAAS,GAAG,GAAE;AAAC,YAAG,EAAE,EAAE,CAAC,IAAE,EAAE,OAAM,MAAM,oCAAoC;AAAA,MAAE;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE;AAAO,SAAC,IAAE,IAAE,EAAE,IAAE,CAAC,IAAE,WAAS,GAAG,CAAC,IAAE,EAAE,IAAE,KAAG,IAAE,CAAC,GAAE,EAAE,MAAM,EAAE,IAAE,GAAE,EAAE;AAAA,MAAE;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,IAAE,EAAE;AAAE,eAAO,EAAE,MAAI,EAAE,IAAE,EAAE,EAAE,CAAC,IAAE,CAAC;AAAA,MAAE;AAAC,eAAS,EAAE,GAAE,GAAE;AAAC,eAAM,OAAK,IAAE,OAAK,KAAG,EAAE,IAAE,EAAE,IAAE,EAAE,EAAE,CAAC,IAAE,SAAO,EAAE,EAAE,IAAE,EAAE,CAAC;AAAA,MAAC;AAAC,eAASC,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,WAAG,CAAC;AAAE,WAAG,GAAE,GAAE,GAAE,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,UAAE,MAAI,EAAE,IAAE;AAAQ,aAAG,EAAE,KAAG,IAAE,GAAG,CAAC,EAAE,CAAC,IAAE,KAAG,EAAE,EAAE,IAAE,EAAE,CAAC,IAAE,IAAG,IAAE,EAAE,MAAI,KAAK,KAAG,OAAO,EAAE,CAAC;AAAA,MAAE;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,CAAC;AAAE,cAAM,QAAQ,CAAC,MAAI,IAAE;AAAI,YAAI,IAAE,EAAE,CAAC;AAAE,YAAE,KAAG,GAAG,CAAC;AAAE,YAAG,EAAE,KAAE,KAAGD,GAAE,GAAE,CAAC,GAAE,IAAE,KAAG,OAAO,OAAO,CAAC;AAAA,aAAM;AAAC,cAAE,EAAE,IAAE;AAAG,cAAI,IAAE,IAAE;AAAE,cAAE,KAAG,CAAC,IAAE,KAAG,IAAE,MAAI,CAAC,KAAG,GAAG,GAAE,EAAE,KAAG,IAAE,GAAG,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC,GAAE,GAAG,GAAE,GAAE,CAAC;AAAA,QAAE;AAAC,eAAO;AAAA,MAAC;AAC/4B,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,CAAC;AAAE,YAAI,IAAE,QAAM,IAAE,IAAE,aAAW,OAAO,KAAG,UAAQ,KAAG,eAAa,KAAG,gBAAc,IAAE,OAAO,CAAC,IAAE;AAAO,gBAAM,KAAG,MAAI,KAAG,GAAG,GAAE,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AACjK,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,UAAE,MAAI,EAAE,IAAE,CAAC;AAAG,YAAI,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,GAAG,GAAE,GAAE,GAAE,CAAC;AAAE,YAAG,CAAC,GAAE;AAAC,cAAI,IAAE;AAAE,cAAE,CAAC;AAAE,cAAI,IAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAE;AAAI,cAAE,CAAC,EAAE,EAAE,CAAC,IAAE;AAAG,cAAI,IAAE;AAAE,WAAC,KAAG,MAAI,IAAE,MAAM,UAAU,MAAM,KAAK,CAAC;AAAG,mBAAQ,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,gBAAI,IAAE,GAAEP,KAAE;AAAG,YAAAA,KAAE,WAASA,KAAE,QAAGA;AAAE,gBAAE,MAAM,QAAQ,CAAC,IAAE,IAAI,EAAE,CAAC,IAAEA,KAAE,IAAI,MAAE;AAAO,gBAAG,WAAS,GAAE;AAAC,kBAAE,EAAE;AAAE,kBAAIC,KAAED,KAAE,EAAE,CAAC;AAAE,oBAAIC,MAAG;AAAG,oBAAIA,MAAG;AAAI,cAAAA,MAAGD,MAAG,EAAE,GAAEC,EAAC;AAAE,kBAAEA;AAAE,kBAAE,KAAG,CAAC,EAAE,IAAE;AAAG,gBAAE,KAAK,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,EAAE,CAAC,IAAE;AAAE,cAAE,EAAE,CAAC;AAAE,cAAE,IAAE;AAAG,cAAE,IAAE,IAAE,KAAG,IAAE;AAAE,eAAG,MAAI,IAAE,GAAE,OAAO,SAAS,CAAC,MAAI,IAAE,MAAM,UAAU,MAAM,KAAK,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,IAAE;AAAG,gBAAI,KAAG;AAAA,YAAG;AAAA,YACtf;AAAA,YAAE;AAAA,UAAC;AAAE,WAAC,KAAG,KAAG,MAAIM,GAAE,GAAE,CAAC;AAAE,eAAG,OAAO,OAAO,CAAC;AAAE,iBAAO;AAAA,QAAC;AAAC,cAAI,IAAE,OAAO,SAAS,CAAC,GAAE,KAAG,CAAC,IAAE,OAAO,OAAO,CAAC,IAAE,CAAC,KAAG,MAAI,IAAE,MAAM,UAAU,MAAM,KAAK,CAAC,GAAE,EAAE,EAAE,CAAC,IAAE;AAAI,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAE;AAAG,YAAE,GAAG,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,YAAE,GAAG,GAAE,GAAE,GAAE,CAAC;AAAE,YAAG,EAAE,KAAG,EAAE,CAAC,IAAE,IAAG;AAAC,eAAI,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,gBAAE,EAAE,CAAC;AAAE,gBAAG,EAAE,EAAE,CAAC,IAAE,GAAE;AAAC,kBAAI,IAAE,GAAG,GAAE,KAAE;AAAE,gBAAE,IAAE;AAAA,YAAC,MAAM,KAAE;AAAE,kBAAI,MAAI,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,EAAE;AAAA,UAAE;AAAC,UAAAA,GAAE,GAAE,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AACvW,eAASE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAG,QAAM,KAAG,aAAW,OAAO,EAAE,OAAM,MAAM,wEAAsE,OAAO,IAAE,OAAK,CAAC;AAAE,QAAAD,GAAE,GAAE,GAAE,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,WAAG,CAAC;AAAE,YAAI,IAAE,GAAG,GAAE,GAAE,GAAE,OAAG,KAAE;AAAE,YAAE,QAAM,IAAE,IAAE,IAAI;AAAE,YAAE,GAAG,GAAE,GAAE,GAAE,KAAE;AAAE,kBAAQ,KAAG,EAAE,OAAO,GAAE,GAAE,CAAC,GAAE,EAAE,OAAO,GAAE,GAAE,EAAE,CAAC,MAAI,EAAE,KAAK,CAAC,GAAE,EAAE,KAAK,EAAE,CAAC;AAAG,UAAE,EAAE,KAAG,GAAG,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,eAAO,QAAM,IAAE,IAAE;AAAA,MAAC;AAAC,eAASE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAE,WAAS,IAAE,IAAE;AAAE,eAAO,GAAG,GAAG,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC;AAAC,UAAI;AAAG,eAAS,GAAG,GAAE;AAAC,gBAAO,OAAO,GAAE;AAAA,UAAC,KAAK;AAAS,mBAAO,SAAS,CAAC,IAAE,IAAE,OAAO,CAAC;AAAA,UAAE,KAAK;AAAS,gBAAG,EAAE,KAAG,MAAM,QAAQ,CAAC,GAAE;AAAC,kBAAG,OAAK,EAAE,CAAC,IAAE,KAAK,QAAO,IAAE,MAAM,UAAU,MAAM,KAAK,CAAC,GAAE,GAAG,CAAC,GAAE;AAAA,YAAC,OAAK;AAAC,kBAAG,MAAI,QAAM,KAAG,aAAa,WAAW,QAAO,GAAG,CAAC;AAAE,kBAAG,aAAa,IAAG;AAAC,oBAAI,IAAE,EAAE;AAAE,uBAAO,QAAM,IAAE,KAAG,aAAW,OAAO,IAAE,IAAE,EAAE,IAAE,GAAG,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAG,QAAM,GAAE;AAAC,cAAG,MAAM,QAAQ,CAAC,EAAE,KAAE,GAAG,GAAE,GAAE,GAAE,WAAS,CAAC;AAAA,mBAAU,GAAG,CAAC,GAAE;AAAC,gBAAI,IAAE,CAAC,GAAE;AAAE,iBAAI,KAAK,EAAE,GAAE,CAAC,IAAE,GAAG,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,gBAAE;AAAA,UAAC,MAAM,KAAE,EAAE,GAAE,CAAC;AAAE,iBAAO;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,CAAC;AAAE,YAAE,IAAE,CAAC,EAAE,IAAE,MAAI;AAAO,YAAE,MAAM,UAAU,MAAM,KAAK,CAAC;AAAE,iBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,IAAE,GAAG,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,UAAE,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAO,EAAE,OAAK,KAAG,EAAE,OAAO,IAAE,GAAG,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAE,OAAK,GAAG,CAAC;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAE,WAAS,IAAE,KAAG;AAAE,YAAG,QAAM,GAAE;AAAC,cAAG,MAAI,aAAa,WAAW,QAAO,EAAE,SAAO,IAAI,GAAG,IAAI,WAAW,CAAC,GAAE,EAAE,IAAE,GAAG;AAAE,cAAG,MAAM,QAAQ,CAAC,GAAE;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,gBAAG,IAAE,EAAE,QAAO;AAAE,gBAAG,KAAG,EAAE,IAAE,QAAM,IAAE,MAAI,MAAI,GAAG,QAAO,EAAE,GAAE,IAAE,CAAC,GAAE;AAAE,gBAAE,GAAG,GAAE,IAAG,IAAE,IAAE,KAAG,GAAE,IAAE;AAAE,gBAAE,EAAE,CAAC;AAAE,gBAAE,KAAG,IAAE,KAAG,OAAO,OAAO,CAAC;AAAE,mBAAO;AAAA,UAAC;AAAC,iBAAO,EAAE,OAAK,KAAG,GAAG,CAAC,IAAE;AAAA,QAAC;AAAA,MAAC;AACh8C,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAG,IAAE,EAAE,KAAG,EAAE,EAAE,CAAC,GAAE;AAAC,cAAE,EAAE,CAAC;AAAE,cAAE,IAAE,IAAE,KAAG,IAAE,GAAG,GAAE,EAAE,GAAE,GAAG,GAAE,CAAC,GAAE,OAAO,OAAO,CAAC,GAAE,IAAE;AAAG,aAAG,CAAC;AAAE,cAAE,QAAM,IAAE,KAAG,GAAG,CAAC,CAAC;AAAE,cAAG,QAAM,GAAE;AAAC,gBAAE,CAAC,CAAC,EAAE;AAAO,iBAAI,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,kBAAI,IAAE,EAAE,CAAC;AAAE,kBAAE,KAAG,EAAE,EAAE,EAAE,CAAC,IAAE;AAAG,gBAAE,CAAC,IAAE,EAAE;AAAA,YAAC;AAAC,iBAAG,IAAE,IAAE,KAAG;AAAE,gBAAE,EAAE,CAAC;AAAE,aAAC,IAAE,OAAK,MAAI,OAAO,SAAS,CAAC,MAAI,IAAE,MAAM,UAAU,MAAM,KAAK,CAAC,IAAG,EAAE,GAAE,IAAE,CAAC;AAAG,cAAE,MAAI,EAAE,IAAE,CAAC;AAAG,cAAE,EAAE,CAAC,IAAE;AAAA,UAAC,MAAM,GAAE,MAAI,EAAE,EAAE,CAAC,IAAE;AAAQ,aAAG,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC,MAAM,CAAAF,GAAE,GAAE,GAAE,GAAG,GAAE,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAG,EAAE,EAAE,CAAC,IAAE,EAAE,QAAO;AAAE,YAAE,GAAG,GAAE,IAAE;AAAE,QAAAD,GAAE,EAAE,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAC1c,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,IAAE,CAAC;AAAE,QAAAA,GAAE,GAAE,EAAE;AAAE,YAAI,IAAE,EAAE,YAAY;AAAE,aAAG,EAAE,KAAK,CAAC;AAAE,YAAE,EAAE;AAAE,YAAG,GAAE;AAAC,YAAE,SAAO,EAAE;AAAO,YAAE,KAAK,QAAO,EAAE,QAAO,EAAE,MAAM;AAAE,cAAI,IAAE,CAAC;AAAE,YAAE,EAAE,SAAO,CAAC,IAAE;AAAA,QAAC;AAAC,eAAK,EAAE,CAAC,IAAE,QAAM,GAAG,CAAC;AAAE,YAAE,KAAG,EAAE,EAAE,IAAE,KAAG;AAAG,YAAE,EAAE;AAAY,aAAG;AAAE,YAAE,IAAI,EAAE,CAAC;AAAE,aAAG;AAAO,UAAE,MAAI,EAAE,IAAE,EAAE,EAAE,MAAM;AAAG,YAAE,CAAC,EAAE,EAAE,CAAC,IAAE;AAAI,iBAAQ,IAAE,IAAE,EAAE,SAAO,IAAE,EAAE,QAAO,IAAE,GAAE,IAAE,GAAE,IAAI,IAAG,GAAE,GAAE,IAAE,EAAE,GAAE,EAAE,CAAC,GAAE,OAAG,GAAE,CAAC;AAAE,YAAG,EAAE,UAAQ,KAAK,EAAE,IAAG,GAAE,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,MAAG,GAAE,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC;AAAC,eAASI,GAAE,GAAE,GAAE,GAAE;AAAC,gBAAM,MAAI,IAAE;AAAI,aAAG;AAAO,YAAI,IAAE,KAAK,YAAY,KAAG,GAAE,IAAE,IAAE,GAAE,IAAE,KAAK,YAAY,GAAE,IAAE;AAAG,YAAG,QAAM,GAAE;AAAC,cAAE,IAAE,CAAC,CAAC,IAAE,CAAC;AAAE,cAAI,IAAE;AAAG,cAAI,IAAE;AAAG,gBAAI,IAAE,GAAE,KAAG;AAAK,YAAE,GAAE,CAAC;AAAA,QAAC,OAAK;AAAC,cAAG,CAAC,MAAM,QAAQ,CAAC,EAAE,OAAM,MAAM;AAAE,cAAG,KAAG,MAAI,EAAE,CAAC,EAAE,OAAM,MAAM;AAAE,cAAI,IAAE,IAAEJ,GAAE,GAAE,CAAC;AAAE,cAAG,IAAE,OAAK,KAAG,GAAG,EAAC,IAAE,OAAK,KAAG,QAAM,KAAG;AAAI,cAAG,EAAE,KAAG,MAAI,EAAE,KAAE;AAAA,eAAM;AAAC,gBAAG,IAAE,EAAE,QAAO;AAAC,kBAAI,IAAE,EAAE,EAAE,SAAO,CAAC;AAAE,kBAAG,GAAG,CAAC,KAAG,OAAM,GAAE;AAAC,oBAAE;AAAE,qBAAG;AAAI,uBAAO,EAAE;AAAE,oBAAI,IAAE,MAAG;AAAE,qBAAI,KAAK,GAAE;AAAC,sBAAE;AAAG;AAAA,gBAAK;AAAC,qBAAG,EAAE,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,mBAAS,MAAI,EAAE,OAAM,MAAM;AAAE,gBAAI,KAAG,EAAE,GAAE,CAAC;AAAA,QAAC;AAAC,aAAK,KAAG,IAAE,IAAE,MAAI;AAAE,aAAK,IAC34B;AAAO,aAAK,IAAE;AAAE,WAAE;AAAC,cAAE,KAAK,EAAE;AAAO,cAAE,IAAE;AAAE,cAAG,MAAI,IAAE,KAAK,EAAE,CAAC,GAAE,GAAG,CAAC,IAAG;AAAC,iBAAK,IAAE;AAAE,iBAAK,IAAE,IAAE,KAAK;AAAE,kBAAM;AAAA,UAAC;AAAC,qBAAS,KAAG,KAAG,KAAG,KAAK,IAAE,KAAK,IAAI,GAAE,IAAE,IAAE,KAAK,CAAC,GAAE,KAAK,IAAE,UAAQ,KAAK,IAAE,OAAO;AAAA,QAAS;AAAC,YAAG,CAAC,KAAG,KAAK,KAAG,OAAM,KAAK,EAAE,OAAM,MAAM,2EAA2E;AAAE,YAAG,GAAE;AAAC,cAAE,KAAG,CAAC,KAAG;AAAG,cAAE,KAAK;AAAE,cAAI;AAAE,eAAI,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAE,EAAE,CAAC,GAAE,IAAE,KAAG,KAAG,KAAK,IAAG,IAAE,EAAE,CAAC,KAAG,GAAG,GAAE,CAAC,IAAE,EAAE,CAAC,IAAE,OAAK,MAAI,IAAE,GAAG,IAAI,KAAI,IAAE,EAAE,CAAC,KAAG,GAAG,GAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAA,QAAG;AAAA,MAAC;AACpc,MAAAI,GAAE,UAAU,SAAO,WAAU;AAAC,eAAO,GAAG,KAAK,GAAE,IAAG,EAAE;AAAA,MAAC;AAAE,MAAAA,GAAE,UAAU,IAAE,WAAU;AAAC,eAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAE;AAAA,MAAE;AAAE,eAAS,GAAG,GAAE,GAAE;AAAC,YAAG,MAAM,QAAQ,CAAC,GAAE;AAAC,cAAI,IAAE,EAAE,CAAC,GAAE,IAAE;AAAE,WAAC,KAAG,IAAE,MAAI,KAAG;AAAI,WAAC,IAAE,OAAK,KAAG,EAAE,GAAE,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAA,GAAE,UAAU,KAAG;AAAG,MAAAA,GAAE,UAAU,WAAS,WAAU;AAAC,eAAO,KAAK,EAAE,SAAS;AAAA,MAAC;AAAE,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAG,GAAE;AAAC,cAAI,IAAE,CAAC,GAAE;AAAE,eAAI,KAAK,GAAE;AAAC,gBAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE;AAAG,kBAAI,EAAE,IAAE,EAAE,MAAI,EAAE,GAAG,GAAE,EAAE,MAAI,EAAE,KAAG,GAAG,EAAE,EAAE,GAAE,IAAE,yBAAS,GAAE;AAAC,qBAAO,SAAS,GAAE,GAAE,GAAE;AAAC,uBAAO,EAAE,EAAE,GAAE,GAAE,GAAE,EAAE,EAAE;AAAA,cAAC;AAAA,YAAC,EAAE,CAAC,KAAG,EAAE,MAAI,EAAE,IAAE,GAAG,EAAE,GAAG,GAAE,EAAE,EAAE,GAAE,IAAE,yBAAS,GAAE;AAAC,qBAAO,SAAS,GAAE,GAAE,GAAE;AAAC,uBAAO,EAAE,EAAE,GAAE,GAAE,GAAE,EAAE,CAAC;AAAA,cAAC;AAAA,YAAC,EAAE,CAAC,KAAG,IAAE,EAAE,GAAE,EAAE,KAAG;AAAG,cAAE,GAAE,GAAE,EAAE,EAAE;AAAE,gBAAE,EAAC,GAAE,EAAE,GAAE,IAAG,EAAE,IAAG,GAAE,EAAE,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,WAAG,GAAE,CAAC;AAAA,MAAC;AAAC,UAAI,KAAG,OAAO;AAAE,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,eAAO,EAAE,EAAE,MAAI,EAAE,EAAE,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,CAAC;AAAA,QAAC;AAAA,MAAE;AACxqB,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,EAAE;AAAE,YAAG,CAAC,GAAE;AAAC,cAAI,IAAE,GAAG,CAAC;AAAE,cAAE,SAAS,GAAE,GAAE;AAAC,mBAAO,GAAG,GAAE,GAAE,CAAC;AAAA,UAAC;AAAE,YAAE,EAAE,IAAE;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE;AAAG,YAAG,EAAE,QAAO,GAAG,CAAC;AAAE,YAAG,IAAE,EAAE,GAAG,QAAO,GAAG,EAAE,GAAG,GAAE,GAAE,EAAE,EAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,GAAG,CAAC,GAAE,IAAE,EAAE,IAAG,IAAE,EAAE,GAAG;AAAE,eAAO,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,CAAC;AAAE,sBAAY,OAAO,KAAG,MAAI,EAAE,WAAS,IAAE,EAAE,GAAE,EAAE,CAAC,IAAE;AAAG,eAAO,MAAM,QAAQ,CAAC,MAAI,MAAM,KAAG,MAAM,KAAG,IAAE,EAAE,UAAQ,cAAY,OAAO,EAAE,CAAC,KAAG,IAAE;AAAA,MAAM;AAC5c,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,UAAE,IAAE,EAAE,CAAC;AAAE,YAAI,IAAE;AAAE,YAAG,EAAE,SAAO,KAAG,aAAW,OAAO,EAAE,CAAC,GAAE;AAAC,cAAI,IAAE,EAAE,GAAG;AAAE,YAAE,GAAE,CAAC;AAAA,QAAC;AAAC,eAAK,IAAE,EAAE,UAAQ;AAAC,cAAE,EAAE,GAAG;AAAE,mBAAQ,IAAE,IAAE,GAAE,IAAE,EAAE,UAAQ,aAAW,OAAO,EAAE,CAAC,IAAG;AAAI,cAAE,EAAE,GAAG;AAAE,eAAG;AAAE,kBAAO,GAAE;AAAA,YAAC,KAAK;AAAE,gBAAE,GAAE,GAAE,CAAC;AAAE;AAAA,YAAM,KAAK;AAAE,eAAC,IAAE,GAAG,GAAE,CAAC,MAAI,KAAI,EAAE,GAAE,GAAE,GAAE,CAAC,KAAG,EAAE,GAAE,GAAE,GAAE,EAAE,GAAG,CAAC;AAAE;AAAA,YAAM,KAAK;AAAE,kBAAE;AAAI,kBAAE,GAAG,GAAE,CAAC;AAAE,gBAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAG,CAAC;AAAE;AAAA,YAAM,KAAK;AAAE,gBAAE,GAAE,GAAE,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,CAAC;AAAE;AAAA,YAAM,KAAK;AAAE,gBAAE,GAAE,GAAE,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,CAAC;AAAE;AAAA,YAAM;AAAQ,oBAAM,MAAM,kDAAgD,CAAC;AAAA,UAAE;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AAC3f,UAAI,KAAG,OAAO;AAAE,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,EAAE;AAAE,YAAG,CAAC,GAAE;AAAC,cAAI,IAAE,GAAG,CAAC;AAAE,cAAE,SAAS,GAAE,GAAE;AAAC,mBAAO,GAAG,GAAE,GAAE,CAAC;AAAA,UAAC;AAAE,YAAE,EAAE,IAAE;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,EAAE;AAAE,cAAI,IAAE,SAAS,GAAE,GAAE;AAAC,iBAAO,GAAG,GAAE,GAAE,CAAC;AAAA,QAAC,GAAE,EAAE,EAAE,IAAE;AAAG,eAAO;AAAA,MAAC;AAAC,UAAI,KAAG,OAAO;AAAE,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,KAAK,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,UAAE,KAAK,GAAE,EAAE,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,GAAG,CAAC,GAAE,IAAE,GAAG,CAAC,EAAE,GAAE,IAAE,EAAE;AAAE,UAAE,KAAK,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,EAAE;AAAE,UAAE,KAAK,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC7c,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,EAAE;AAAE,YAAG,EAAE,QAAO;AAAE,YAAE,GAAG,GAAE,EAAE,EAAE,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAAE,cAAM,KAAG,MAAM,MAAI,EAAE,SAAO;AAAG,eAAO;AAAA,MAAC;AAAC,UAAI,KAAG,OAAO;AAAE,eAAS,GAAG,GAAE,GAAE;AAAC,UAAE,CAAC,IAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE;AAAE,UAAE,CAAC,IAAE,IAAE,SAAS,GAAE,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC,IAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,IAAE,GAAG,CAAC,GAAE,IAAE,GAAG,CAAC,EAAE;AAAE,UAAE,CAAC,IAAE,SAAS,GAAE,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,GAAE,IAAE,GAAG,GAAE,GAAE,CAAC;AAAE,UAAE,CAAC,IAAE,SAAS,GAAE,GAAE,GAAE;AAAC,iBAAO,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAC5Z,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,EAAE,EAAE;AAAE,YAAG,EAAE,QAAO;AAAE,YAAE,GAAG,GAAE,EAAE,EAAE,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAAE,cAAM,KAAG,MAAM,MAAI,EAAE,SAAO;AAAG,eAAO;AAAA,MAAC;AAC7G,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,eAAK,GAAG,CAAC,KAAG,KAAG,EAAE,KAAG;AAAC,cAAI,IAAE,EAAE,GAAE,IAAE,EAAE,CAAC;AAAE,cAAG,CAAC,GAAE;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,kBAAI,IAAE,EAAE,CAAC,OAAK,IAAE,EAAE,CAAC,IAAE,GAAG,CAAC;AAAA,UAAE;AAAC,cAAG,CAAC,KAAG,CAAC,EAAE,GAAE,GAAE,CAAC,GAAE;AAAC,gBAAE;AAAE,gBAAE;AAAE,gBAAE,EAAE;AAAE,eAAG,CAAC;AAAE,gBAAI,IAAE;AAAE,gBAAG,CAAC,EAAE,IAAG;AAAC,kBAAE,EAAE,EAAE,IAAE;AAAE,gBAAE,EAAE,IAAE;AAAE,kBAAE,EAAE;AAAE,kBAAG,KAAG,EAAE,KAAE,GAAG;AAAA,mBAAM;AAAC,oBAAE,GAAG,GAAE,CAAC;AAAE,oBAAG,EAAE,KAAG,EAAE,EAAE,KAAE,EAAE,EAAE,SAAS,GAAE,IAAE,CAAC;AAAA,qBAAM;AAAC,sBAAE,EAAE;AAAE,sBAAI,IAAE;AAAE,sBAAE,IAAE;AAAE,sBAAE,MAAI,IAAE,GAAG,IAAE,KAAG,EAAE,MAAM,GAAE,CAAC,IAAE,IAAI,WAAW,EAAE,SAAS,GAAE,CAAC,CAAC;AAAA,gBAAC;AAAC,oBAAE,KAAG,EAAE,SAAO,GAAG,IAAE,IAAI,GAAG,GAAE,EAAE;AAAA,cAAC;AAAC,eAAC,IAAE,EAAE,KAAG,EAAE,KAAK,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC;AACjZ,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,iBAAQ,IAAE,EAAE,QAAO,IAAE,KAAG,IAAE,GAAE,IAAE,IAAE,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,EAAC,GAAE,EAAE,IAAE,CAAC,GAAG,GAAE,GAAE,EAAE,CAAC,CAAC;AAAE,WAAG,GAAE,GAAE,IAAE,EAAE,CAAC,IAAE,MAAM;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,eAAM,EAAC,GAAE,GAAE,GAAE,EAAC;AAAA,MAAC;AAC3I,UAAIC,KAAE,GAAG,SAAS,GAAE,GAAE,GAAE;AAAC,YAAG,MAAI,EAAE,EAAE,QAAM;AAAG,YAAE,EAAE;AAAE,YAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,CAAC;AAAE,YAAI,IAAE,EAAE,IAAE,CAAC;AAAE,YAAI,IAAE,EAAE,IAAE,CAAC;AAAE,YAAE,EAAE,IAAE,CAAC;AAAE,UAAE,GAAE,EAAE,IAAE,CAAC;AAAE,aAAG,KAAG,IAAE,KAAG,IAAE,KAAG,KAAG,KAAG,QAAM;AAAE,YAAE,KAAG,KAAG,MAAI;AAAE,YAAE,MAAI,KAAG;AAAI,aAAG;AAAQ,QAAAJ,GAAE,GAAE,GAAE,OAAK,IAAE,IAAE,MAAI,WAAS,IAAE,KAAG,IAAE,IAAE,KAAK,IAAI,GAAE,IAAI,IAAE,IAAE,IAAE,KAAK,IAAI,GAAE,IAAE,GAAG,KAAG,IAAE,KAAK,IAAI,GAAE,EAAE,EAAE;AAAE,eAAM;AAAA,MAAE,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,YAAE,GAAG,GAAE,CAAC;AAAE,YAAG,QAAM,GAAE;AAAC,UAAAF,GAAE,EAAE,GAAE,IAAE,IAAE,CAAC;AAAE,cAAE,EAAE;AAAE,cAAI,IAAE,CAAC;AAAE,gBAAI,IAAE,IAAE,IAAE,IAAEH,KAAEC,KAAE,KAAGA,KAAE,GAAED,KAAE,cAAY,MAAM,CAAC,KAAGC,KAAE,GAAED,KAAE,eAAa,KAAG,IAAE,IAAE,IAAE,cAAY,KAAG,CAAC,IAAE,GAAE,uBAAsB,KAAGC,KAAE,GAAED,MAAG,IAAE,gBACze,KAAG,wBAAuB,KAAG,IAAE,KAAK,MAAM,IAAE,KAAK,IAAI,GAAE,IAAI,CAAC,GAAEC,KAAE,GAAED,MAAG,IAAE,OAAK,MAAI,IAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAE,KAAK,GAAG,GAAE,KAAG,KAAK,IAAI,GAAE,CAAC,CAAC,GAAE,IAAE,KAAK,MAAM,UAAQ,CAAC,GAAE,YAAU,KAAG,EAAE,GAAEC,KAAE,GAAED,MAAG,IAAE,IAAE,OAAK,KAAG,IAAE,aAAW;AAAI,cAAEA;AAAE,YAAE,EAAE,KAAK,MAAI,IAAE,GAAG;AAAE,YAAE,EAAE,KAAK,MAAI,IAAE,GAAG;AAAE,YAAE,EAAE,KAAK,MAAI,KAAG,GAAG;AAAE,YAAE,EAAE,KAAK,MAAI,KAAG,GAAG;AAAA,QAAC;AAAA,MAAC,CAAC,GAAE,KAAG,GAAG,SAAS,GAAE,GAAE,GAAE;AAAC,YAAG,MAAI,EAAE,EAAE,QAAM;AAAG,YAAI,IAAE,EAAE,GAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,WAAE;AAAC,cAAI,IAAE,EAAE,GAAG;AAAE,gBAAI,IAAE,QAAM;AAAE,eAAG;AAAA,QAAC,SAAO,KAAG,KAAG,IAAE;AAAK,aAAG,MAAI,MAAI,IAAE,QAAM;AAAG,aAAI,IAAE,GAAE,KAAG,KAAG,IAAE,KAAI,KAAG,EAAE,KAAE,EAAE,GAAG,GAAE,MAAI,IAAE,QAAM;AAAE;AAAA,UAAE;AAAA,UACnf;AAAA,QAAC;AAAE,YAAG,MAAI,GAAE;AAAC,cAAE,MAAI;AAAE,cAAE,MAAI;AAAE,cAAG,IAAE,IAAE,WAAW,KAAE,CAAC,IAAE,MAAI,GAAE,IAAE,CAAC,MAAI,GAAE,KAAG,MAAI,IAAE,IAAE,MAAI;AAAG,cAAE,aAAW,KAAG,MAAI;AAAA,QAAE,MAAM,OAAM,GAAG;AAAE,QAAAK,GAAE,GAAE,GAAE,IAAE,CAAC,IAAE,CAAC;AAAE,eAAM;AAAA,MAAE,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,YAAE,EAAE,GAAE,CAAC;AAAE,gBAAM,MAAI,aAAW,OAAO,KAAG,GAAG,CAAC,GAAE,QAAM,MAAIF,GAAE,EAAE,GAAE,IAAE,CAAC,GAAE,aAAW,OAAO,KAAG,IAAE,EAAE,GAAE,GAAG,CAAC,GAAE,GAAG,GAAEH,IAAEC,EAAC,MAAI,IAAE,GAAG,CAAC,GAAE,GAAG,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAA,MAAI,CAAC,GAAE,KAAG,GAAG,SAAS,GAAE,GAAE,GAAE;AAAC,YAAG,MAAI,EAAE,EAAE,QAAM;AAAG,QAAAI,GAAE,GAAE,GAAE,GAAG,EAAE,CAAC,CAAC;AAAE,eAAM;AAAA,MAAE,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,YAAE,EAAE,GAAE,CAAC;AAAE,YAAG,QAAM,KAAG,QAAM,EAAE,KAAGF,GAAE,EAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,GAAE,KAAG,EAAE,CAAAA,GAAE,GAAE,CAAC;AAAA,aAAM;AAAC,eAAI,IAAE,GAAE,IAAE,GAAE,IAAI,GAAE,EAAE,KAAK,IAAE,MAAI,GAAG,GAAE,MAAI;AAAE,YAAE,EAAE,KAAK,CAAC;AAAA,QAAC;AAAA,MAAC,CAAC,GACjgB,KAAG,GAAG,SAAS,GAAE,GAAE,GAAE;AAAC,YAAG,MAAI,EAAE,EAAE,QAAM;AAAG,YAAI,IAAE,GAAG,EAAE,CAAC,MAAI;AAAE,YAAE,EAAE;AAAE,YAAI,IAAE,GAAG,GAAE,CAAC;AAAE,YAAE,EAAE;AAAE,YAAG,IAAG;AAAC,cAAI,IAAE,GAAE;AAAE,WAAC,IAAE,QAAM,IAAE,KAAG,IAAI,YAAY,SAAQ,EAAC,OAAM,KAAE,CAAC;AAAG,cAAE,IAAE;AAAE,cAAE,MAAI,KAAG,MAAI,EAAE,SAAO,IAAE,EAAE,SAAS,GAAE,CAAC;AAAE,cAAG;AAAC,gBAAI,IAAE,EAAE,OAAO,CAAC;AAAA,UAAC,SAAO,GAAE;AAAC,gBAAG,WAAS,IAAG;AAAC,kBAAG;AAAC,kBAAE,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAAA,cAAC,SAAO,GAAE;AAAA,cAAC;AAAC,kBAAG;AAAC,kBAAE,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC,GAAE,KAAG;AAAA,cAAE,SAAO,GAAE;AAAC,qBAAG;AAAA,cAAE;AAAA,YAAC;AAAC,aAAC,OAAK,KAAG;AAAQ,kBAAM;AAAA,UAAE;AAAA,QAAC,OAAK;AAAC,cAAE;AAAE,cAAE,IAAE;AAAE,cAAE,CAAC;AAAE,mBAAQ,IAAE,MAAK,GAAE,GAAE,IAAE,IAAG,KAAE,EAAE,GAAG,GAAE,MAAI,IAAE,EAAE,KAAK,CAAC,IAAE,MAAI,IAAE,KAAG,IAAED,GAAE,KAAG,IAAE,EAAE,GAAG,GAAE,MAAI,KAAG,SAAO,IAAE,QAAM,KAAIA,GAAE,KACnf,EAAE,MAAM,IAAE,OAAK,IAAE,IAAE,EAAE,KAAG,MAAI,IAAE,KAAG,IAAE,IAAEA,GAAE,KAAG,IAAE,EAAE,GAAG,GAAE,SAAO,IAAE,QAAM,QAAM,KAAG,MAAI,KAAG,QAAM,KAAG,OAAK,KAAG,UAAQ,IAAE,EAAE,GAAG,KAAG,QAAM,KAAIA,GAAE,KAAG,EAAE,MAAM,IAAE,OAAK,MAAI,IAAE,OAAK,IAAE,IAAE,EAAE,KAAG,OAAK,IAAE,KAAG,IAAE,IAAEA,GAAE,KAAG,IAAE,EAAE,GAAG,GAAE,SAAO,IAAE,QAAM,OAAK,KAAG,OAAK,IAAE,QAAM,MAAI,UAAQ,IAAE,EAAE,GAAG,KAAG,QAAM,UAAQ,IAAE,EAAE,GAAG,KAAG,QAAM,KAAIA,GAAE,MAAI,KAAG,IAAE,MAAI,MAAI,IAAE,OAAK,MAAI,IAAE,OAAK,IAAE,IAAE,IAAG,KAAG,OAAM,EAAE,MAAM,KAAG,KAAG,QAAM,QAAO,IAAE,QAAM,KAAK,MAAIA,GAAE,GAAE,QAAM,EAAE,WAAS,IAAE,GAAG,GAAE,CAAC,GAAE,EAAE,SAAO;AAAG,cAAE,GAAG,GAAE,CAAC;AAAA,QAAC;AAAC,QAAAG,GAAE,GAAE,GAAE,CAAC;AAAE,eAAM;AAAA,MAAE,GAAE,SAAS,GAAE,GAAE,GAAE;AAAC,YAAE,EAAE,GAAE,CAAC;AAAE,YAAG,QAAM,GAAE;AAAC,cAAI,IAAE;AACnf,cAAE,WAAS,IAAE,QAAG;AAAE,cAAG,IAAG;AAAC,gBAAG,KAAG,2EAA2E,KAAK,CAAC,EAAE,OAAM,MAAM,6BAA6B;AAAE,iBAAG,OAAK,KAAG,IAAI,gBAAc,OAAO,CAAC;AAAA,UAAC,OAAK;AAAC,qBAAQ,IAAE,GAAE,IAAE,IAAI,WAAW,IAAE,EAAE,MAAM,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,kBAAI,IAAE,EAAE,WAAW,CAAC;AAAE,kBAAG,MAAI,EAAE,GAAE,GAAG,IAAE;AAAA,mBAAM;AAAC,oBAAG,OAAK,EAAE,GAAE,GAAG,IAAE,KAAG,IAAE;AAAA,qBAAQ;AAAC,sBAAG,SAAO,KAAG,SAAO,GAAE;AAAC,wBAAG,SAAO,KAAG,IAAE,EAAE,QAAO;AAAC,0BAAI,IAAE,EAAE,WAAW,EAAE,CAAC;AAAE,0BAAG,SAAO,KAAG,SAAO,GAAE;AAAC,4BAAE,QAAM,IAAE,SAAO,IAAE,QAAM;AAAM,0BAAE,GAAG,IAAE,KAAG,KAAG;AAAI,0BAAE,GAAG,IAAE,KAAG,KAAG,KAAG;AACjf,0BAAE,GAAG,IAAE,KAAG,IAAE,KAAG;AAAI,0BAAE,GAAG,IAAE,IAAE,KAAG;AAAI;AAAA,sBAAQ,MAAM;AAAA,oBAAG;AAAC,wBAAG,EAAE,OAAM,MAAM,6BAA6B;AAAE,wBAAE;AAAA,kBAAK;AAAC,oBAAE,GAAG,IAAE,KAAG,KAAG;AAAI,oBAAE,GAAG,IAAE,KAAG,IAAE,KAAG;AAAA,gBAAG;AAAC,kBAAE,GAAG,IAAE,IAAE,KAAG;AAAA,cAAG;AAAA,YAAC;AAAC,gBAAE,MAAI,EAAE,SAAO,IAAE,EAAE,SAAS,GAAE,CAAC;AAAA,UAAC;AAAC,UAAAF,GAAE,EAAE,GAAE,IAAE,IAAE,CAAC;AAAE,UAAAA,GAAE,EAAE,GAAE,EAAE,MAAM;AAAE,YAAE,GAAE,EAAE,EAAE,IAAI,CAAC;AAAE,YAAE,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAC,GAAE,KAAG,GAAG,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAG,MAAI,EAAE,EAAE,QAAM;AAAG,YAAE,GAAG,GAAE,GAAE,CAAC;AAAE,YAAE,EAAE,EAAE;AAAE,YAAE,GAAG,EAAE,CAAC,MAAI;AAAE,YAAI,IAAE,EAAE,EAAE,IAAE,GAAE,IAAE,IAAE;AAAE,aAAG,MAAI,EAAE,EAAE,IAAE,GAAE,EAAE,GAAE,GAAE,QAAO,QAAO,MAAM,GAAE,IAAE,IAAE,EAAE,EAAE;AAAG,YAAG,EAAE,OAAM,MAAM,2DAAyD,IAAE,2BAC9d,IAAE,KAAG,uFAAuF;AAAE,UAAE,EAAE,IAAE;AAAE,UAAE,EAAE,IAAE;AAAE,eAAM;AAAA,MAAE,GAAE,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,YAAE,GAAG,GAAE,GAAE,CAAC;AAAE,YAAG,QAAM,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,cAAI,IAAE;AAAE,UAAAA,GAAE,EAAE,GAAE,IAAE,IAAE,CAAC;AAAE,cAAI,IAAE,EAAE,EAAE,IAAI;AAAE,YAAE,GAAE,CAAC;AAAE,YAAE,KAAK,EAAE,CAAC;AAAE,cAAE;AAAE,YAAE,EAAE,CAAC,GAAE,CAAC;AAAE,cAAE;AAAE,cAAI,IAAE,EAAE,IAAI;AAAE,eAAI,IAAE,EAAE,IAAE,EAAE,EAAE,OAAO,IAAE,GAAE,MAAI,IAAG,GAAE,KAAK,IAAE,MAAI,GAAG,GAAE,OAAK,GAAE,EAAE;AAAI,YAAE,KAAK,CAAC;AAAE,YAAE;AAAA,QAAG;AAAA,MAAC,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,eAAO,SAAS,GAAE,GAAE;AAAC,aAAE;AAAC,gBAAG,GAAG,QAAO;AAAC,kBAAI,IAAE,GAAG,IAAI;AAAE,gBAAE,WAAW,CAAC;AAAE,iBAAG,EAAE,GAAE,GAAE,CAAC;AAAE,kBAAE;AAAA,YAAC,MAAM,KAAE,IAAI,GAAG,GAAE,CAAC;AAAE,gBAAG;AAAC,kBAAI,IAAE,GAAG,CAAC;AAAE,kBAAI,IAAE,GAAG,IAAI,EAAE,KAAE,GAAE,CAAC;AAAE,oBAAM;AAAA,YAAC,UAAC;AAAQ,kBAAE,EAAE,GAAE,EAAE,IAAE,MAAK,EAAE,IAAE,OAAG,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,OAAG,EAAE,IAAE,IAAG,EAAE,IAAE,IAAG,MAAI,GAAG,UAAQ,GAAG,KAAK,CAAC;AAAA,YAAC;AAAC,gBAAE;AAAA,UAAM;AAAC,iBAAO;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAO,WAAU;AAAC,cAAI,IAAE,IAAI;AAAG,aAAG,MAAK,GAAE,GAAG,CAAC,CAAC;AAAE,YAAE,GAAE,EAAE,EAAE,IAAI,CAAC;AAAE,mBAAQ,IAAE,IAAI,WAAW,EAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,QAAO,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,cAAE,IAAI,GAAE,CAAC;AAAE,iBAAG,EAAE;AAAA,UAAM;AAAC,YAAE,IAAE,CAAC,CAAC;AAAE,iBAAO;AAAA,QAAC;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,QAAAK,GAAE,KAAK,MAAK,CAAC;AAAA,MAAC;AAAC,SAAG,IAAGA,EAAC;AAAE,UAAI,KAAG,CAAC,IAAG,GAAE,IAAG,GAAEC,IAAE,GAAE,IAAG,GAAE,EAAE;AAAE,SAAG,UAAU,IAAE,GAAG,EAAE;AAAE,eAAS,GAAG,GAAE;AAAC,QAAAD,GAAE,KAAK,MAAK,GAAE,IAAG,EAAE;AAAA,MAAC;AAAC,SAAG,IAAGA,EAAC;AAAE,SAAG,UAAU,oBAAkB,SAAS,GAAE,GAAE;AAAC,WAAG,MAAK,GAAE,IAAG,GAAE,CAAC;AAAE,eAAO;AAAA,MAAI;AAAE,UAAI,KAAG,CAAC,CAAC,GAAE,KAAG,GAAG,CAAC,IAAG,GAAE,IAAG,EAAE,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,QAAAA,GAAE,KAAK,MAAK,CAAC;AAAA,MAAC;AAAC,SAAG,IAAGA,EAAC;AAAE,UAAI,KAAG,CAAC,IAAG,GAAEC,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAEA,EAAC;AAAE,SAAG,UAAU,IAAE,GAAG,EAAE;AAAE,eAAS,GAAG,GAAE;AAAC,QAAAD,GAAE,KAAK,MAAK,GAAE,IAAG,EAAE;AAAA,MAAC;AAAC,SAAG,IAAGA,EAAC;AAAE,UAAI,KAAG,CAAC,CAAC,GAAE,KAAG,GAAG,CAAC,IAAG,GAAE,IAAG,EAAE,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,QAAAA,GAAE,KAAK,MAAK,CAAC;AAAA,MAAC;AAAC,SAAG,IAAGA,EAAC;AAAE,UAAI,KAAG,CAAC,IAAG,GAAEC,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAE,EAAE,GAAE,KAAG,GAAG,EAAE;AAAE,SAAG,UAAU,IAAE,GAAG,EAAE;AAAE,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAE,EAAE,aAAa,MAAI,IAAE,EAAE,gBAAc,EAAE,eAAe;AAAE,UAAE,aAAa,GAAE,CAAC;AAAE,UAAE,cAAc,CAAC;AAAE,YAAG,CAAC,EAAE,mBAAmB,GAAE,EAAE,cAAc,EAAE,OAAM,MAAM,wCAAsC,EAAE,iBAAiB,CAAC,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAO,GAAG,GAAE,IAAG,CAAC,EAAE,IAAI,SAAS,GAAE;AAAC,cAAI,IAAE,EAAE,GAAE,CAAC;AAAE,iBAAM,EAAC,OAAM,QAAM,IAAE,IAAE,GAAE,IAAGF,GAAE,GAAE,CAAC,GAAE,OAAM,QAAM,EAAE,GAAE,CAAC,IAAE,GAAG,EAAE,GAAE,CAAC,GAAE,EAAE,IAAE,QAAO,aAAY,QAAM,EAAE,GAAE,CAAC,IAAE,GAAG,EAAE,GAAE,CAAC,GAAE,EAAE,IAAE,OAAM;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAM,EAAC,GAAEA,GAAE,GAAE,CAAC,GAAE,GAAEA,GAAE,GAAE,CAAC,GAAE,GAAEA,GAAE,GAAE,CAAC,GAAE,YAAW,QAAM,GAAG,GAAE,CAAC,IAAEA,GAAE,GAAE,CAAC,IAAE,OAAM;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,eAAO,GAAG,GAAG,CAAC,GAAE,IAAG,CAAC,EAAE,IAAI,EAAE;AAAA,MAAC;AAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,aAAK,IAAE;AAAE,aAAK,IAAE;AAAE,aAAK,IAAE;AAAA,MAAC;AAC16D,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,WAAG,GAAE,CAAC;AAAE,YAAG,eAAa,OAAO,EAAE,EAAE,OAAO,sBAAsB,QAAO,QAAQ,QAAQ,EAAE,EAAE,OAAO,sBAAsB,CAAC;AAAE,YAAG,EAAE,QAAO,QAAQ,QAAQ,EAAE,EAAE,MAAM;AAAE,YAAG,eAAa,OAAO,kBAAkB,QAAO,kBAAkB,EAAE,EAAE,MAAM;AAAE,mBAAS,EAAE,MAAI,EAAE,IAAE,SAAS,cAAc,QAAQ;AAAG,eAAO,IAAI,QAAQ,SAAS,GAAE;AAAC,YAAE,EAAE,SAAO,EAAE,EAAE,OAAO;AAAO,YAAE,EAAE,QAAM,EAAE,EAAE,OAAO;AAAM,YAAE,EAAE,WAAW,MAAK,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,QAAO,GAAE,GAAE,EAAE,EAAE,OAAO,OAAM,EAAE,EAAE,OAAO,MAAM;AAAE,YAAE,EAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC7e,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE;AAAE,YAAG,WAAS,EAAE,GAAE;AAAC,cAAI,IAAE,GAAG,GAAE,qKAAoK,CAAC,GAAE,IAAE,GAAG,GAAE,yJAAwJ,CAAC,GAAE,IAAE,EAAE,cAAc;AAAE,YAAE,aAAa,GAAE,CAAC;AAAE,YAAE,aAAa,GAAE,CAAC;AAAE,YAAE,YAAY,CAAC;AAAE,cAAG,CAAC,EAAE,oBAAoB,GAAE,EAAE,WAAW,EAAE,OAAM,MAAM,yCACpgB,EAAE,kBAAkB,CAAC,CAAC;AAAE,cAAE,EAAE,IAAE;AAAE,YAAE,WAAW,CAAC;AAAE,cAAE,EAAE,mBAAmB,GAAE,UAAU;AAAE,YAAE,IAAE,EAAC,GAAE,EAAE,kBAAkB,GAAE,SAAS,GAAE,GAAE,EAAE,kBAAkB,GAAE,MAAM,GAAE,IAAG,EAAC;AAAE,YAAE,IAAE,EAAE,aAAa;AAAE,YAAE,WAAW,EAAE,cAAa,EAAE,CAAC;AAAE,YAAE,wBAAwB,EAAE,EAAE,CAAC;AAAE,YAAE,oBAAoB,EAAE,EAAE,GAAE,GAAE,EAAE,OAAM,OAAG,GAAE,CAAC;AAAE,YAAE,WAAW,EAAE,cAAa,IAAI,aAAa,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC,GAAE,EAAE,WAAW;AAAE,YAAE,WAAW,EAAE,cAAa,IAAI;AAAE,YAAE,IAAE,EAAE,aAAa;AAAE,YAAE,WAAW,EAAE,cAAa,EAAE,CAAC;AAAE,YAAE,wBAAwB,EAAE,EAAE,CAAC;AAAE,YAAE;AAAA,YAAoB,EAAE,EAAE;AAAA,YAC9gB;AAAA,YAAE,EAAE;AAAA,YAAM;AAAA,YAAG;AAAA,YAAE;AAAA,UAAC;AAAE,YAAE,WAAW,EAAE,cAAa,IAAI,aAAa,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,EAAE,WAAW;AAAE,YAAE,WAAW,EAAE,cAAa,IAAI;AAAE,YAAE,UAAU,GAAE,CAAC;AAAA,QAAC;AAAC,YAAE,EAAE;AAAE,UAAE,WAAW,EAAE,CAAC;AAAE,UAAE,OAAO,QAAM,EAAE;AAAM,UAAE,OAAO,SAAO,EAAE;AAAO,UAAE,SAAS,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM;AAAE,UAAE,cAAc,EAAE,QAAQ;AAAE,UAAE,EAAE,cAAc,EAAE,MAAM;AAAE,UAAE,wBAAwB,EAAE,CAAC;AAAE,UAAE,WAAW,EAAE,cAAa,EAAE,CAAC;AAAE,UAAE,oBAAoB,EAAE,GAAE,GAAE,EAAE,OAAM,OAAG,GAAE,CAAC;AAAE,UAAE,wBAAwB,EAAE,CAAC;AAAE,UAAE,WAAW,EAAE,cAAa,EAAE,CAAC;AAAE,UAAE;AAAA,UAAoB,EAAE;AAAA,UACzf;AAAA,UAAE,EAAE;AAAA,UAAM;AAAA,UAAG;AAAA,UAAE;AAAA,QAAC;AAAE,UAAE,gBAAgB,EAAE,mBAAiB,EAAE,mBAAiB,EAAE,aAAY,IAAI;AAAE,UAAE,WAAW,GAAE,GAAE,GAAE,CAAC;AAAE,UAAE,MAAM,EAAE,gBAAgB;AAAE,UAAE,UAAU,MAAG,MAAG,MAAG,IAAE;AAAE,UAAE,WAAW,EAAE,cAAa,GAAE,CAAC;AAAE,UAAE,yBAAyB,EAAE,CAAC;AAAE,UAAE,yBAAyB,EAAE,CAAC;AAAE,UAAE,WAAW,EAAE,cAAa,IAAI;AAAE,UAAE,EAAE,cAAc,CAAC;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,aAAK,IAAE;AAAA,MAAC;AAAC;AAAC,UAAI,KAAG,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,IAAG,EAAE,CAAC;AAAE,eAAS,GAAG,GAAE,GAAE;AAAC,eAAO,IAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE,GAAE;AAAC,eAAO,CAAC,IAAE;AAAA,MAAC;AAAC,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE,SAAS,cAAc,QAAQ;AAAE,UAAE,aAAa,OAAM,CAAC;AAAE,UAAE,aAAa,eAAc,WAAW;AAAE,eAAO,IAAI,QAAQ,SAAS,GAAE;AAAC,YAAE,iBAAiB,QAAO,WAAU;AAAC,cAAE;AAAA,UAAC,GAAE,KAAE;AAAE,YAAE,iBAAiB,SAAQ,WAAU;AAAC,cAAE;AAAA,UAAC,GAAE,KAAE;AAAE,mBAAS,KAAK,YAAY,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AACrwB,eAAS,KAAI;AAAC,eAAO,EAAE,SAAS,GAAE;AAAC,kBAAO,EAAE,GAAE;AAAA,YAAC,KAAK;AAAE,qBAAO,EAAE,IAAE,GAAEX,GAAE,GAAE,YAAY,YAAY,EAAE,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,gBAAE,IAAE;AAAE,gBAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,IAAE,GAAE,EAAE,IAAE,MAAK,EAAE,OAAO,KAAE;AAAA,YAAE,KAAK;AAAE,qBAAO,EAAE,OAAO,IAAE;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AACtM,eAAS,GAAG,GAAE;AAAC,aAAK,IAAE;AAAE,aAAK,YAAU,CAAC;AAAE,aAAK,IAAE,CAAC;AAAE,aAAK,IAAE,CAAC;AAAE,aAAK,IAAE,CAAC;AAAE,aAAK,IAAE,CAAC;AAAE,aAAK,IAAE,KAAK,IAAE,KAAK,KAAG;AAAG,aAAK,IAAE,QAAQ,QAAQ;AAAE,aAAK,KAAG;AAAG,aAAK,IAAE,CAAC;AAAE,aAAK,aAAW,KAAG,EAAE,cAAY;AAAG,YAAG,aAAW,OAAO,OAAO,KAAI,IAAE,OAAO,SAAS,SAAS,SAAS,EAAE,UAAU,GAAE,OAAO,SAAS,SAAS,SAAS,EAAE,YAAY,GAAG,CAAC,IAAE;AAAA,iBAAY,gBAAc,OAAO,SAAS,KAAE,SAAS,SAAS,SAAS,EAAE,UAAU,GAAE,SAAS,SAAS,SAAS,EAAE,YAAY,GAAG,CAAC,IAAE;AAAA,YAAS,OAAM,MAAM,+DAA+D;AAC7hB,aAAK,KAAG;AAAE,YAAG,EAAE,SAAQ;AAAC,cAAE,EAAE,OAAO,KAAK,EAAE,OAAO,CAAC;AAAE,mBAAQ,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,GAAE;AAAC,gBAAE,EAAE;AAAM,gBAAI,IAAE,EAAE,QAAQ,CAAC,EAAE;AAAQ,uBAAS,MAAI,KAAK,EAAE,CAAC,IAAE,eAAa,OAAO,IAAE,EAAE,IAAE;AAAA,UAAE;AAAA,QAAC;AAAA,MAAC;AAAC,UAAE,GAAG;AAAU,QAAE,QAAM,WAAU;AAAC,aAAK,KAAG,KAAK,EAAE,OAAO;AAAE,eAAO,QAAQ,QAAQ;AAAA,MAAC;AACxQ,eAAS,GAAG,GAAE;AAAC,YAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,kBAAO,EAAE,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAG,CAAC,EAAE,GAAG,QAAO,EAAE,OAAO;AAAE,kBAAE,WAAS,EAAE,EAAE,QAAM,CAAC,IAAE,eAAa,OAAO,EAAE,EAAE,QAAM,EAAE,EAAE,MAAM,EAAE,CAAC,IAAE,EAAE,EAAE;AAAM,qBAAOA,GAAE,GAAE,GAAG,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,kBAAE,EAAE;AAAE,kBAAG,aAAW,OAAO,OAAO,QAAO,GAAG,gCAA+B,EAAC,YAAW,EAAE,WAAU,CAAC,GAAE,GAAG,wCAAuC,EAAC,YAAW,EAAE,WAAU,CAAC,GAAE,IAAE,EAAE,OAAO,SAAS,GAAE;AAAC,uBAAO,WAAS,EAAE;AAAA,cAAI,CAAC,GAAE,IAAE,EAAE,OAAO,SAAS,GAAE;AAAC,uBAAO,WAAS,EAAE;AAAA,cAAI,CAAC,GAAE,IAAE,QAAQ,IAAI,EAAE,IAAI,SAAS,GAAE;AAAC,oBAAIC,KACpgB,GAAG,GAAE,EAAE,GAAG;AAAE,oBAAG,WAAS,EAAE,MAAK;AAAC,sBAAIC,KAAE,EAAE;AAAK,kBAAAD,KAAEA,GAAE,KAAK,SAAS,GAAE;AAAC,sBAAE,aAAaC,IAAE,CAAC;AAAE,2BAAO,QAAQ,QAAQ,CAAC;AAAA,kBAAC,CAAC;AAAA,gBAAC;AAAC,uBAAOD;AAAA,cAAC,CAAC,CAAC,GAAE,IAAE,QAAQ,IAAI,EAAE,IAAI,SAAS,GAAE;AAAC,uBAAO,WAAS,EAAE,QAAM,EAAE,QAAM,KAAG,CAAC,EAAE,QAAM,CAAC,IAAE,GAAG,EAAE,WAAW,EAAE,KAAI,EAAE,EAAE,CAAC,IAAE,QAAQ,QAAQ;AAAA,cAAC,CAAC,CAAC,EAAE,KAAK,WAAU;AAAC,oBAAI,GAAEA,IAAEC;AAAE,uBAAO,EAAE,SAAS,GAAE;AAAC,sBAAG,KAAG,EAAE,EAAE,QAAO,IAAE,OAAO,8BAA6BD,KAAE,OAAO,sCAAqCC,KAAE,GAAEF,GAAE,GAAE,EAAEC,EAAC,GAAE,CAAC;AAAE,kBAAAC,GAAE,IAAE,EAAE;AAAE,oBAAE,IAAE;AAAA,gBAAC,CAAC;AAAA,cAAC,CAAC,GAAE,IAAE,WAAU;AAAC,uBAAO,EAAE,SAAS,GAAE;AAAC,oBAAE,EAAE,SAAO,EAAE,EAAE,MAAM,MAAI,IAAEF;AAAA,oBAAE;AAAA,oBACpf,GAAG,GAAE,EAAE,EAAE,MAAM,GAAG;AAAA,oBAAE;AAAA,kBAAC,KAAG,EAAE,IAAE,GAAE,IAAE;AAAQ,yBAAO;AAAA,gBAAC,CAAC;AAAA,cAAC,EAAE,GAAEA,GAAE,GAAE,QAAQ,IAAI,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC;AAAE,kBAAG,eAAa,OAAO,cAAc,OAAM,MAAM,+DAA+D;AAAE,kBAAE,EAAE,OAAO,SAAS,GAAE;AAAC,uBAAO,WAAS,EAAE,QAAM,EAAE,QAAM,KAAG,CAAC,EAAE,QAAM,CAAC;AAAA,cAAC,CAAC,EAAE,IAAI,SAAS,GAAE;AAAC,uBAAO,EAAE,WAAW,EAAE,KAAI,EAAE,EAAE;AAAA,cAAC,CAAC;AAAE,4BAAc,MAAM,MAAK,GAAG,CAAC,CAAC;AAAE,kBAAE;AAAE,qBAAOA,GAAE,GAAE,6BAA6B,MAAM,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,gBAAE,IAAE,EAAE;AAAE,gBAAE,IAAE,IAAI,gBAAgB,GAAE,CAAC;AAAE,gBAAE,EAAE,SAAO,EAAE;AAAE,kBAAE,EAAE,EAAE,GAAG,cAAc,EAAE,GAAE;AAAA,gBAAC,WAAU;AAAA,gBACtf,OAAM;AAAA,gBAAG,IAAG,gBAAc,OAAO,yBAAuB,IAAE;AAAA,cAAC,CAAC;AAAE,gBAAE,EAAE,GAAG,mBAAmB,CAAC;AAAE,gBAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAE,gBAAE,IAAE,SAAS,cAAc,QAAQ;AAAE,kBAAE,EAAE,EAAE,WAAW,UAAS,CAAC,CAAC;AAAE,kBAAG,CAAC,MAAI,IAAE,EAAE,EAAE,WAAW,SAAQ,CAAC,CAAC,GAAE,CAAC,GAAG,QAAO,MAAM,iEAAiE,GAAE,EAAE,OAAO;AAAE,gBAAE,IAAE;AAAE,gBAAE,EAAE,SAAO,EAAE;AAAE,gBAAE,EAAE,cAAc,EAAE,GAAE,MAAG,MAAG,CAAC,CAAC;AAAA,YAAE,KAAK;AAAE,gBAAE,IAAE,IAAI,EAAE,EAAE,gBAAa,EAAE,KAAG,OAAG,EAAE,IAAE;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC1Z,eAAS,GAAG,GAAE;AAAC,YAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,cAAG,KAAG,EAAE,GAAE;AAAC,gBAAG,EAAE,EAAE,SAAO,EAAE,EAAE,MAAM,OAAK,EAAE,OAAK,EAAE,EAAE,MAAM,IAAI,QAAO,EAAE,OAAO;AAAE,cAAE,IAAE;AAAG,gBAAG,CAAC,EAAE,EAAE,SAAO,CAAC,EAAE,EAAE,MAAM,KAAI;AAAC,gBAAE,IAAE;AAAE;AAAA,YAAM;AAAC,cAAE,KAAG,EAAE,EAAE,MAAM;AAAI,mBAAOA,GAAE,GAAE,GAAG,GAAE,EAAE,EAAE,MAAM,GAAG,GAAE,CAAC;AAAA,UAAC;AAAC,eAAG,EAAE,MAAI,IAAE,EAAE,GAAE,EAAE,EAAE,UAAU,CAAC;AAAG,cAAE,EAAE,OAAO,KAAK,EAAE,CAAC,CAAC;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,KAAE,EAAE,OAAM,EAAE,EAAE,aAAa,GAAE,EAAE,EAAE,CAAC,CAAC;AAAE,YAAE,IAAE,CAAC;AAAE,cAAG,EAAE,EAAE,UAAU,MAAI,IAAE,EAAE,EAAE,EAAE,SAAS,GAAE,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,KAAE,EAAE,OAAM,GAAG,GAAE,CAAC;AAAE,cAAE,EAAE;AAAE,YAAE,IAAE,CAAC;AAAE,YAAE,WAAW,CAAC;AAAE,YAAE,IAAE;AAAA,QAAC,CAAC;AAAA,MAAC;AAChf,QAAE,QAAM,WAAU;AAAC,YAAI,IAAE;AAAK,eAAO,EAAE,SAAS,GAAE;AAAC,YAAE,MAAI,EAAE,EAAE,MAAM,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC;AAAG,YAAE,IAAE;AAAA,QAAC,CAAC;AAAA,MAAC;AAC3F,QAAE,aAAW,SAAS,GAAE,GAAE;AAAC,YAAI,IAAE;AAAK,YAAG,IAAE,KAAG,KAAK,EAAE,SAAQ;AAAC,mBAAQ,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,OAAO,KAAK,CAAC,CAAC,GAAE,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC,GAAE,IAAE,EAAE,KAAK,EAAE,KAAG,IAAE,EAAE,OAAM,EAAE,KAAK,KAAK,KAAG,KAAK,EAAE,CAAC,MAAI,EAAE,CAAC,IAAG;AAAC,iBAAK,EAAE,CAAC,IAAE,EAAE,CAAC;AAAE,gBAAI,IAAE,EAAE,CAAC;AAAE,uBAAS,MAAI,EAAE,aAAW,EAAE,IAAE,EAAE,UAAS,EAAE,IAAE,EAAE,CAAC,GAAE,EAAE,KAAK,yBAAS,GAAE;AAAC,qBAAO,WAAU;AAAC,oBAAI;AAAE,uBAAO,EAAE,SAAS,GAAE;AAAC,sBAAG,KAAG,EAAE,EAAE,QAAOA,GAAE,GAAE,EAAE,EAAE,EAAE,CAAC,GAAE,CAAC;AAAE,sBAAE,EAAE;AAAE,2BAAK,MAAI,EAAE,IAAE;AAAI,oBAAE,IAAE;AAAA,gBAAC,CAAC;AAAA,cAAC;AAAA,YAAC,EAAE,CAAC,CAAC,IAAG,EAAE,oBAAkB,IAAE,OAAO;AAAA,cAAO,CAAC;AAAA,cAAE,EAAC,gBAAe,IAAG,iBAAgB,EAAC;AAAA,cAAE,EAAE;AAAA,cACxe,EAAC,aAAY,MAAI,EAAE,OAAK,EAAE,CAAC,IAAE,GAAE,cAAa,MAAI,EAAE,OAAK,EAAE,CAAC,IAAE,OAAG,aAAY,MAAI,EAAE,OAAK,EAAE,CAAC,IAAE,GAAE;AAAA,YAAC,GAAE,EAAE,KAAK,CAAC;AAAA,UAAG;AAAC,cAAG,MAAI,EAAE,UAAQ,MAAI,EAAE,OAAO,MAAK,IAAE,MAAG,KAAK,KAAG,WAAS,KAAK,IAAE,CAAC,IAAE,KAAK,GAAG,OAAO,CAAC,GAAE,KAAK,KAAG,WAAS,KAAK,IAAE,CAAC,IAAE,KAAK,GAAG,OAAO,CAAC;AAAA,QAAC;AAAA,MAAC;AAC9O,eAAS,GAAG,GAAE;AAAC,YAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,kBAAO,EAAE,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAG,CAAC,EAAE,EAAE,QAAO,EAAE,OAAO;AAAE,kBAAG,CAAC,EAAE,GAAE;AAAC,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAE,EAAE,EAAE,CAAC;AAAE,kBAAE,EAAE,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAG,EAAE,MAAK;AAAC,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAE,EAAE;AAAM,qBAAOA,GAAE,GAAE,EAAE,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,kBAAE,EAAE,KAAK;AAAE,gBAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAE,gBAAE,IAAE;AAAA,YAAO,KAAK;AAAE,kBAAG,EAAE,GAAE;AAAC,oBAAE,IAAI,EAAE,EAAE;AAA6B,oBAAE,EAAE,EAAE,CAAC;AAAE,qBAAI,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,KAAE,EAAE,OAAM,EAAE,UAAU,CAAC;AAAE,kBAAE,EAAE,cAAc,CAAC;AAAE,kBAAE,OAAO;AAAE,kBAAE,IAAE;AAAA,cAAM;AAAC,gBAAE,IAAE;AAAG,gBAAE,IAAE;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC1a,QAAE,aAAW,WAAU;AAAC,YAAI,IAAE;AAAK,eAAO,EAAE,SAAS,GAAE;AAAC,iBAAO,KAAG,EAAE,IAAEA,GAAE,GAAE,GAAG,CAAC,GAAE,CAAC,IAAE,KAAG,EAAE,IAAEA,GAAE,GAAE,GAAG,CAAC,GAAE,CAAC,IAAEA,GAAE,GAAE,GAAG,CAAC,GAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAE,eAAS,GAAG,GAAE,GAAE;AAAC,YAAI,GAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,cAAG,KAAK,EAAE,EAAE,QAAO,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;AAAE,cAAE,EAAE,WAAW,GAAE,EAAE;AAAE,cAAE,MAAM,CAAC,EAAE,KAAK,SAAS,GAAE;AAAC,mBAAO,EAAE,YAAY;AAAA,UAAC,CAAC;AAAE,YAAE,EAAE,CAAC,IAAE;AAAE,iBAAO,EAAE,OAAO,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,QAAE,eAAa,SAAS,GAAE,GAAE;AAAC,aAAK,IAAE,KAAK,EAAE,aAAa,GAAE,CAAC,IAAE,KAAK,EAAE,CAAC,IAAE;AAAA,MAAC;AAAE,QAAE,uBAAqB,WAAU;AAAC,aAAK,IAAE,CAAC;AAAE,aAAK,KAAG,KAAK,EAAE,qBAAqB;AAAA,MAAC;AAC5c,QAAE,OAAK,SAAS,GAAE,GAAE;AAAC,YAAI,IAAE,MAAK,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,kBAAO,EAAE,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAG,CAAC,EAAE,EAAE,OAAO,QAAO,EAAE,OAAO;AAAE,kBAAE,OAAK,WAAS,KAAG,SAAO,IAAE,YAAY,IAAI,IAAE;AAAG,qBAAOA,GAAE,GAAE,EAAE,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,qBAAOA,GAAE,GAAE,EAAE,WAAW,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,kBAAE,IAAI,EAAE,EAAE;AAAe,kBAAE,EAAE,OAAO,KAAK,CAAC,CAAC;AAAE,mBAAI,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,KAAG,IAAE,EAAE,OAAM,IAAE,EAAE,EAAE,OAAO,CAAC,GAAE;AAAC,mBAAE;AAAC,sBAAI,IAAE,EAAE,CAAC;AAAE,0BAAO,EAAE,MAAK;AAAA,oBAAC,KAAK;AAAQ,0BAAIC,KAAE,EAAE,EAAE,EAAE,MAAM;AAAE,sBAAAA,OAAIA,KAAE,IAAI,GAAG,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,EAAE,EAAE,MAAM,IAAEA;AAAG,4BAAIA,GAAE,MAAIA,GAAE,IAAEA,GAAE,EAAE,cAAc;AAAG,0BAAG,gBAAc,OAAO,oBACtf,aAAa,kBAAiB;AAAC,4BAAIC,KAAE,EAAE;AAAW,4BAAI,IAAE,EAAE;AAAA,sBAAW,MAAK,iBAAc,OAAO,oBAAkB,aAAa,oBAAkBA,KAAE,EAAE,cAAa,IAAE,EAAE,kBAAgBA,KAAE,EAAE,OAAM,IAAE,EAAE;AAAQ,0BAAE,EAAC,QAAOD,GAAE,GAAE,OAAMC,IAAE,QAAO,EAAC;AAAE,sBAAAA,KAAED,GAAE;AAAE,sBAAAC,GAAE,OAAO,QAAM,EAAE;AAAM,sBAAAA,GAAE,OAAO,SAAO,EAAE;AAAO,sBAAAA,GAAE,cAAcA,GAAE,QAAQ;AAAE,sBAAAD,GAAE,EAAE,cAAcA,GAAE,CAAC;AAAE,sBAAAC,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAc,CAAC;AAAE,sBAAAD,GAAE,EAAE,cAAc,CAAC;AAAE,sBAAAA,KAAE;AAAE,4BAAM;AAAA,oBAAE,KAAK;AAAa,sBAAAA,KAAE,EAAE,EAAE,EAAE,MAAM;AAAE,sBAAAA,OAAIA,KAAE,IAAI,GAAG,EAAE,CAAC,GAAE,EAAE,EAAE,EAAE,MAAM,IAAEA;AACpf,sBAAAA,GAAE,SAAOA,GAAE,OAAK,IAAIA,GAAE,EAAE;AAAmB,sBAAAA,GAAE,KAAK,MAAM,EAAE,MAAM;AAAE,2BAAI,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,wBAAAC,KAAE,EAAE,CAAC;AAAE,4BAAI,IAAED,GAAE,MAAKa,KAAE,EAAE,gBAAeC,KAAE;AAAE,4BAAI,IAAEb,GAAE;AAAG,4BAAI,IAAE,IAAI;AAAG,wBAAAQ,GAAE,GAAE,GAAE,EAAE,EAAE;AAAE,wBAAAA,GAAE,GAAE,GAAE,EAAE,EAAE;AAAE,wBAAAA,GAAE,GAAE,GAAE,EAAE,MAAM;AAAE,wBAAAA,GAAE,GAAE,GAAE,EAAE,KAAK;AAAE,wBAAAA,GAAE,GAAE,GAAE,EAAE,QAAQ;AAAE,wBAAAD,GAAE,GAAE,GAAE,EAAE,EAAE;AAAE,4BAAE,EAAE,EAAE;AAAE,wBAAAK,GAAE,KAAK,GAAEC,IAAE,CAAC;AAAE,4BAAGb,GAAE,GAAG,MAAI,IAAE,GAAE,IAAEA,GAAE,GAAG,QAAO,EAAE,GAAE;AAAC,8BAAEA,GAAE,GAAG,CAAC;AAAE,0BAAAY,KAAEb,GAAE;AAAK,0BAAAc,KAAED,GAAE;AAAsB,8BAAE;AAAE,8BAAE,OAAO,OAAO,CAAC,GAAE,GAAE,EAAC,YAAW,EAAE,aAAW,EAAE,aAAW,EAAC,CAAC;AAAE,8BAAI,IAAE,IAAI;AAAG,0BAAAJ,GAAE,GAAE,GAAE,EAAE,CAAC;AAAE,0BAAAA,GAAE,GAAE,GAAE,EAAE,CAAC;AAAE,0BAAAA,GAAE,GAAE,GAAE,EAAE,CAAC;AAAE,4BAAE,cAAYA,GAAE,GAAE,GAAE,EAAE,UAAU;AAAE,8BAAE,EAAE,EAAE;AAAE,0BAAAK,GAAE;AAAA,4BAAKD;AAAA,4BAC1f;AAAA,4BAAE;AAAA,0BAAC;AAAA,wBAAC;AAAC,4BAAGZ,GAAE,GAAG,MAAI,IAAE,GAAE,IAAEA,GAAE,GAAG,QAAO,EAAE,EAAE,CAAAY,KAAEb,GAAE,MAAKc,KAAED,GAAE,mBAAkB,IAAE,GAAE,IAAEZ,GAAE,GAAG,CAAC,GAAE,IAAE,IAAI,MAAGQ,GAAE,GAAE,GAAE,EAAE,EAAE,GAAE,EAAE,SAAOD,GAAE,GAAE,GAAE,EAAE,KAAK,GAAE,EAAE,SAAOA,GAAE,GAAE,GAAE,EAAE,KAAK,GAAE,EAAE,eAAaA,GAAE,GAAE,GAAE,EAAE,WAAW,GAAE,IAAE,EAAE,EAAE,GAAEM,GAAE,KAAKD,IAAE,GAAE,CAAC;AAAA,sBAAC;AAAC,sBAAAb,KAAEA,GAAE;AAAK,4BAAM;AAAA,oBAAE;AAAQ,sBAAAA,KAAE,CAAC;AAAA,kBAAC;AAAA,gBAAC;AAAC,oBAAEA;AAAE,oBAAE,EAAE;AAAO,wBAAO,EAAE,MAAK;AAAA,kBAAC,KAAK;AAAQ,sBAAE,cAAc,OAAO,OAAO,CAAC,GAAE,GAAE,EAAC,QAAO,GAAE,WAAU,EAAC,CAAC,CAAC;AAAE;AAAA,kBAAM,KAAK;AAAa,wBAAE;AAAE,sBAAE,SAAO;AAAE,sBAAE,YAAU;AAAE,sBAAE,kBAAkB,CAAC;AAAE;AAAA,kBAAM;AAAQ,0BAAM,MAAM,iCAA+B,EAAE,OAAK,GAAG;AAAA,gBAAE;AAAA,cAAC;AAAC,gBAAE,EAAE,KAAK,CAAC;AACtf,qBAAOD,GAAE,GAAE,EAAE,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,gBAAE,OAAO,GAAE,EAAE,IAAE;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC5C,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAEC,IAAEC,IAAE;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,kBAAO,EAAE,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAG,CAAC,EAAE,QAAO,EAAE,OAAO,CAAC;AAAE,kBAAE,CAAC;AAAE,kBAAE;AAAE,kBAAE,EAAE,OAAO,KAAK,CAAC,CAAC;AAAE,mBAAI,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,KAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAE,aAAW,OAAO,KAAG,cAAY,EAAE,QAAM,WAAS,EAAE,EAAE,MAAM,KAAG,EAAE;AAAE,kBAAE,MAAI,EAAE,IAAE;AAAI,kBAAE,EAAE,OAAO,KAAK,CAAC,CAAC;AAAE,kBAAE,EAAE,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAG,EAAE,MAAK;AAAC,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAE,EAAE;AAAM,kBAAE,EAAE,CAAC;AAAE,kBAAG,aAAW,OAAO,EAAE,QAAOA,KAAE,GAAE,IAAE,GAAEF,GAAE,GAAE,GAAG,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE;AAAE,kBAAE,EAAE,EAAE,MAAM;AAAE,kBAAG,qBAAmB,EAAE,MAAK;AAAC,oBAAG,GAAE;AAAC,sBAAIc,KAAE,EAAE,YAAY;AAAE,2BAAQC,KAAE,EAAE,iBAAiB,GACngB,IAAE,EAAE,uBAAuB,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAED,GAAE,KAAK,GAAE,EAAE,GAAE;AAAC,wBAAIE,KAAE,GAAGF,GAAE,IAAI,CAAC,CAAC,GAAE,KAAGH,GAAEK,IAAE,CAAC,GAAE,KAAGL,GAAEK,IAAE,CAAC,GAAE,KAAGL,GAAEK,IAAE,CAAC,GAAE,KAAGL,GAAEK,IAAE,CAAC,GAAE,KAAGL,GAAEK,IAAE,GAAE,CAAC,GAAE,KAAG;AAAO,yBAAG,WAAS,KAAG,IAAE;AAAG,oBAAAA,KAAE,EAAC,IAAG,EAAC,IAAG,IAAG,IAAG,IAAG,QAAO,IAAG,OAAM,IAAG,UAAS,IAAG,IAAG,GAAG,EAAEA,IAAE,CAAC,GAAE,EAAE,EAAC,GAAE,IAAG,GAAGD,GAAE,IAAI,CAAC,CAAC,GAAE,IAAG,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC;AAAE,sBAAE,KAAKC,EAAC;AAAA,kBAAC;AAAC,kBAAAF,KAAE;AAAA,gBAAC,MAAM,CAAAA,KAAE,CAAC;AAAE,kBAAE,CAAC,IAAEA;AAAE,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAG,iBAAe,EAAE,MAAK;AAAC,oBAAG,GAAE;AAAC,kBAAAA,KAAE,MAAM,EAAE,KAAK,CAAC;AAAE,uBAAIC,KAAE,GAAEA,KAAE,EAAE,KAAK,GAAEA,KAAI,CAAAD,GAAEC,EAAC,IAAE,EAAE,IAAIA,EAAC;AAAE,oBAAE,OAAO;AAAA,gBAAC,MAAM,CAAAD,KAAE,CAAC;AAAE,kBAAE,CAAC,IAAEA;AAAE,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAG,WAAS,GAAE;AAAC,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAG,iBAAe,EAAE,MAAK;AAAC,kBAAE,CAAC,IAAE;AAAE,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAG,YACzf,EAAE,MAAK;AAAC,kBAAE,CAAC,IAAE;AAAE,kBAAE,IAAE;AAAE;AAAA,cAAK;AAAC,kBAAG,cAAY,EAAE,KAAK,OAAM,MAAM,kCAAgC,EAAE,OAAK,GAAG;AAAE,kBAAE,EAAE,EAAE,CAAC;AAAE,oBAAI,IAAE,IAAI,GAAG,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,EAAE,CAAC,IAAE;AAAG,qBAAOd,GAAE,GAAE,GAAG,GAAE,GAAE,EAAE,CAAC,GAAE,EAAE;AAAA,YAAE,KAAK;AAAG,cAAAC,KAAE,EAAE,GAAE,EAAE,CAAC,IAAEA;AAAA,YAAE,KAAK;AAAE,gBAAE,aAAW,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,UAAU,EAAE,CAAC,CAAC;AAAG,gBAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAG,cAAAC,GAAE,CAAC,IAAE,EAAE;AAAA,YAAE,KAAK;AAAE,kBAAE,EAAE,KAAK;AAAE,gBAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,OAAO,CAAC;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAC5U,eAAS,GAAG,GAAE,GAAE,GAAE;AAAC,YAAI;AAAE,eAAO,EAAE,SAAS,GAAE;AAAC,iBAAM,aAAW,OAAO,KAAG,aAAa,cAAY,aAAa,EAAE,EAAE,gBAAc,EAAE,OAAO,CAAC,IAAE,aAAa,EAAE,EAAE,oBAAkB,IAAE,EAAE,EAAE,CAAC,GAAE,MAAI,IAAE,IAAI,GAAG,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,EAAE,CAAC,IAAE,IAAG,EAAE,OAAO,GAAG,GAAE,GAAE,EAAE,CAAC,CAAC,KAAG,EAAE,OAAO,MAAM;AAAA,QAAC,CAAC;AAAA,MAAC;AAClQ,eAAS,GAAG,GAAE,GAAE;AAAC,iBAAQ,IAAE,EAAE,QAAM,KAAI,IAAE,CAAC,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,cAAW,IAAE,EAAE,EAAE,KAAK,GAAE,IAAE,EAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAE,EAAE,KAAK,EAAE,GAAE,UAAU,EAAE,KAAK;AAAE,YAAE,EAAE,EAAE,eAAe,UAAU,EAAC,WAAU,SAAS,GAAE;AAAC,mBAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,QAAO,EAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,IAAI,CAAC;AAAE,cAAI,IAAE,EAAE,UAAU,CAAC;AAAE,gBAAI,EAAE,IAAE,GAAG,GAAE,GAAE,EAAE,IAAI,EAAE,KAAK,SAAS,GAAE;AAAC,gBAAE,EAAE,CAAC;AAAE,qBAAQ,IAAE,GAAE,IAAE,EAAE,MAAM,QAAO,EAAE,GAAE;AAAC,kBAAI,IAAE,EAAE,EAAE,CAAC,CAAC;AAAE,2BAAW,OAAO,KAAG,EAAE,kBAAgB,EAAE,eAAe,QAAQ,KAAG,EAAE,OAAO;AAAA,YAAC;AAAC,kBAAI,EAAE,IAAE;AAAA,UAAE,CAAC;AAAA,QAAE,EAAC,CAAC;AAAE,UAAE,EAAE,oBAAoB,GAAE,CAAC;AAAE,UAAE,OAAO;AAAA,MAAC;AAC5f,QAAE,YAAU,SAAS,GAAE,GAAE;AAAC,aAAK,UAAU,KAAG,GAAG,IAAE;AAAA,MAAC;AAAE,MAAAC,GAAE,YAAW,EAAE;AAAE,MAAAA,GAAE,cAAa,EAAC,MAAK,GAAE,QAAO,GAAE,IAAG,GAAE,GAAE,QAAO,GAAE,UAAS,GAAE,SAAQ,CAAC;AAAE,eAAS,GAAG,GAAE;AAAC,mBAAS,MAAI,IAAE;AAAG,gBAAO,GAAE;AAAA,UAAC,KAAK;AAAE,mBAAM;AAAA,UAA4B,KAAK;AAAE,mBAAM;AAAA,UAA6B;AAAQ,mBAAM;AAAA,QAA2B;AAAA,MAAC;AACnT,eAAS,GAAG,GAAE;AAAC,YAAI,IAAE;AAAK,YAAE,KAAG,CAAC;AAAE,aAAK,IAAE,IAAI,GAAG,EAAC,YAAW,EAAE,YAAW,OAAM,SAAS,GAAE;AAAC,iBAAM,CAAC,EAAC,KAAI,wCAAuC,GAAE,EAAC,MAAK,OAAG,KAAI,4BAA2B,GAAE,EAAC,MAAK,MAAG,KAAI,iCAAgC,GAAE,EAAC,MAAK,MAAG,KAAI,GAAG,EAAE,eAAe,EAAC,CAAC;AAAA,QAAC,GAAE,OAAM,EAAC,KAAI,oBAAmB,GAAE,WAAU,CAAC,EAAC,OAAM,CAAC,kBAAiB,mBAAkB,qBAAoB,mBAAmB,GAAE,MAAK,EAAC,OAAM,EAAC,MAAK,WAAU,QAAO,oBAAmB,GAAE,eAAc;AAAA,UAAC,MAAK;AAAA,UAAQ,QAAO;AAAA,UACze,WAAU;AAAA,QAAE,GAAE,oBAAmB,EAAC,MAAK,SAAQ,QAAO,mBAAkB,WAAU,GAAE,GAAE,kBAAiB,EAAC,MAAK,WAAU,QAAO,oBAAmB,EAAC,EAAC,CAAC,GAAE,QAAO,EAAC,OAAM,EAAC,MAAK,SAAQ,QAAO,mBAAkB,EAAC,GAAE,SAAQ;AAAA,UAAC,iBAAgB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,uBAAsB,WAAU,oBAAmB,GAAE,SAAQ,aAAW,OAAO,UAAQ,WAAS,OAAO,YAAU,QAAG,kEAAkE,MAAM,GAAG,EAAE,SAAS,UAAU,QAAQ,KAAG,UAAU,UAAU,SAAS,KAAK,KAChhB,gBAAe,SAAQ;AAAA,UAAE,YAAW,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,sBAAqB,iBAAgB,GAAE,WAAU,kBAAiB,EAAC;AAAA,UAAE,iBAAgB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,gCAA+B,gBAAe,+CAA8C,WAAU,YAAW,GAAE,UAAS,SAAS,GAAE;AAAC,gBAAI,GAAE,GAAE;AAAE,mBAAO,EAAE,SAAS,GAAE;AAAC,kBAAG,KAAG,EAAE,EAAE,QAAO,IAAE,GAAG,CAAC,GAAE,IAAE,iDAA+C,GAAEH,GAAE,GAAE,GAAG,EAAE,GAAE,CAAC,GAAE,CAAC;AAAE,kBAAE,EAAE;AAAE,gBAAE,EAAE,aAAa,GAAE,CAAC;AAAE,qBAAO,EAAE,OAAO,IAAE;AAAA,YAAC,CAAC;AAAA,UAAC,EAAC;AAAA,UAC/f,iBAAgB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,gCAA+B,gBAAe,+CAA8C,WAAU,aAAY,EAAC;AAAA,UAAE,oBAAmB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,gCAA+B,gBAAe,kDAAiD,WAAU,aAAY,EAAC;AAAA,UAAE,oBAAmB,EAAC,MAAK,GAAE,iBAAgB;AAAA,YAAC,gBAAe;AAAA,YAA+B,gBAAe;AAAA,YACzc,WAAU;AAAA,UAAY,EAAC;AAAA,UAAE,wBAAuB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,iCAAgC,gBAAe,oEAAmE,WAAU,mBAAkB,EAAC;AAAA,UAAE,uBAAsB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,0BAAyB,gBAAe,wGAAuG,WAAU,YAAW,EAAC;AAAA,QAAC,EAAC,CAAC;AAAA,MAAC;AAAC,UAAE,GAAG;AAAU,QAAE,QAAM,WAAU;AAAC,aAAK,EAAE,MAAM;AAAA,MAAC;AACjgB,QAAE,QAAM,WAAU;AAAC,aAAK,EAAE,MAAM;AAAE,eAAO,QAAQ,QAAQ;AAAA,MAAC;AAAE,QAAE,YAAU,SAAS,GAAE;AAAC,aAAK,EAAE,UAAU,CAAC;AAAA,MAAC;AAAE,QAAE,aAAW,WAAU;AAAC,YAAI,IAAE;AAAK,eAAO,EAAE,SAAS,GAAE;AAAC,iBAAOA,GAAE,GAAE,EAAE,EAAE,WAAW,GAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAE,QAAE,OAAK,SAAS,GAAE,GAAE;AAAC,YAAI,IAAE;AAAK,eAAO,EAAE,SAAS,GAAE;AAAC,iBAAOA,GAAE,GAAE,EAAE,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAE,QAAE,aAAW,SAAS,GAAE;AAAC,aAAK,EAAE,WAAW,CAAC;AAAA,MAAC;AAAE,MAAAG,GAAE,QAAO,EAAE;AACjV,MAAAA,GAAE,oBAAmB,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC,CAAC;AAC7R,MAAAA,GAAE,kBAAiB,EAAC,MAAK,GAAE,gBAAe,GAAE,UAAS,GAAE,gBAAe,GAAE,iBAAgB,GAAE,WAAU,GAAE,iBAAgB,GAAE,UAAS,GAAE,WAAU,GAAE,YAAW,GAAE,YAAW,IAAG,eAAc,IAAG,gBAAe,IAAG,YAAW,IAAG,aAAY,IAAG,YAAW,IAAG,aAAY,IAAG,YAAW,IAAG,aAAY,IAAG,YAAW,IAAG,aAAY,IAAG,YAAW,IAAG,aAAY,IAAG,UAAS,IAAG,WAAU,IAAG,WAAU,IAAG,YAAW,IAAG,YAAW,IAAG,aAAY,IAAG,WAAU,IAAG,YAAW,IAAG,iBAAgB,IAAG,kBAAiB,GAAE,CAAC;AACjf,MAAAA,GAAE,uBAAsB,EAAC,gBAAe,GAAE,UAAS,GAAE,gBAAe,GAAE,UAAS,GAAE,YAAW,GAAE,eAAc,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,UAAS,IAAG,WAAU,IAAG,YAAW,IAAG,WAAU,IAAG,iBAAgB,GAAE,CAAC;AAC5P,MAAAA,GAAE,wBAAuB,EAAC,iBAAgB,GAAE,WAAU,GAAE,iBAAgB,GAAE,WAAU,GAAE,YAAW,IAAG,gBAAe,IAAG,aAAY,IAAG,aAAY,IAAG,aAAY,IAAG,aAAY,IAAG,aAAY,IAAG,WAAU,IAAG,YAAW,IAAG,aAAY,IAAG,YAAW,IAAG,kBAAiB,GAAE,CAAC;AAAE,MAAAA,GAAE,0BAAyB,EAAC,MAAK,EAAC,CAAC;AAAE,MAAAA,GAAE,WAAU,gBAAgB;AAAA,IAAE,GAAG,KAAK,OAAI;AAAA;AAAA;;;AClF9V,kBAAqB;AAA+jB,IAAI,IAAE,SAASc,IAAE,GAAE;AAAC,UAAO,IAAE,OAAO,kBAAgB,EAAC,WAAU,CAAC,EAAC,aAAY,SAAO,SAASA,IAAEC,IAAE;AAAC,IAAAD,GAAE,YAAUC;AAAA,EAAC,KAAG,SAASD,IAAEC,IAAE;AAAC,aAAQ,KAAKA,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAE,CAAC,MAAID,GAAE,CAAC,IAAEC,GAAE,CAAC;AAAA,EAAE,GAAGD,IAAE,CAAC;AAAC;AAAE,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAG,cAAY,OAAO,KAAG,SAAO,EAAE,OAAM,IAAI,UAAU,yBAAuB,OAAO,CAAC,IAAE,+BAA+B;AAAE,WAAS,IAAG;AAAC,SAAK,cAAYA;AAAA,EAAC;AAAC,IAAEA,IAAE,CAAC,GAAEA,GAAE,YAAU,SAAO,IAAE,OAAO,OAAO,CAAC,KAAG,EAAE,YAAU,EAAE,WAAU,IAAI;AAAE;AAAC,IAAI,IAAE,WAAU;AAAC,UAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,aAAQ,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,UAAQ,KAAK,IAAE,UAAU,CAAC,EAAE,QAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,EAAE,CAAC;AAAG,WAAOA;AAAA,EAAC,GAAG,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,KAAI,MAAI,IAAE,UAAW,SAAS,GAAE,GAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,KAAKA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,MAAMA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAK,EAAEA,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAa,IAAEA,KAAE,IAAI,EAAG,SAASD,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAK,GAAE,CAAC;AAAA,IAAC;AAAC,OAAG,IAAE,EAAE,MAAMD,IAAE,KAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,IAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,QAAG,IAAE,EAAE,CAAC,EAAE,OAAM,EAAE,CAAC;AAAE,WAAO,EAAE,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,SAAO,IAAE,EAAC,MAAK,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,GAAE,QAAO,EAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAAS,EAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,IAAG;AAAE,WAAS,EAAEE,IAAE;AAAC,WAAO,SAASC,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,YAAG,EAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,eAAK,IAAG,KAAG;AAAC,cAAG,IAAE,GAAE,MAAI,IAAE,IAAEA,GAAE,CAAC,IAAE,EAAE,SAAOA,GAAE,CAAC,IAAE,EAAE,WAAS,IAAE,EAAE,WAAS,EAAE,KAAK,CAAC,GAAE,KAAG,EAAE,SAAO,EAAE,IAAE,EAAE,KAAK,GAAEA,GAAE,CAAC,CAAC,GAAG,KAAK,QAAO;AAAE,kBAAO,IAAE,GAAE,MAAIA,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,KAAK,IAAGA,GAAE,CAAC,GAAE;AAAA,YAAC,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAEA;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,SAAQ,EAAC,OAAMA,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,YAAE,KAAK;AAAE,gBAAE,SAAQ,IAAEA,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,YAAS,KAAK;AAAE,cAAAA,KAAE,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,YAAS;AAAQ,kBAAG,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,SAAO,KAAG,EAAE,EAAE,SAAO,CAAC,MAAI,MAAIA,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,oBAAE;AAAE;AAAA,cAAQ;AAAC,kBAAG,MAAIA,GAAE,CAAC,MAAI,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAG;AAAC,kBAAE,QAAMA,GAAE,CAAC;AAAE;AAAA,cAAK;AAAC,kBAAG,MAAIA,GAAE,CAAC,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,IAAEA;AAAE;AAAA,cAAK;AAAC,kBAAG,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,EAAE,IAAI,KAAKA,EAAC;AAAE;AAAA,cAAK;AAAC,gBAAE,CAAC,KAAG,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,UAAQ;AAAC,UAAAA,KAAE,EAAE,KAAKF,IAAE,CAAC;AAAA,QAAC,SAAOA,IAAE;AAAC,UAAAE,KAAE,CAAC,GAAEF,EAAC,GAAE,IAAE;AAAA,QAAC,UAAC;AAAQ,cAAE,IAAE;AAAA,QAAC;AAAC,YAAG,IAAEE,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,eAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,MAAC,EAAE,CAACA,IAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAE,GAAE,GAAE;AAAC,MAAG,KAAG,MAAI,UAAU,OAAO,UAAQ,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,IAAE,GAAE,IAAI,EAAC,KAAG,KAAK,MAAI,MAAI,IAAE,MAAM,UAAU,MAAM,KAAK,GAAE,GAAE,CAAC,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAOA,GAAE,OAAO,KAAG,MAAM,UAAU,MAAM,KAAK,CAAC,CAAC;AAAC;AAAC,IAAI,IAAE,CAAC,QAAO,YAAW,aAAY,YAAW,aAAY,iBAAgB,kBAAiB,cAAa,eAAc,cAAa,eAAc,YAAW,aAAY,aAAY,cAAa,cAAa,aAAa;AAA7N,IAA+N,IAAE,CAAC,QAAO,kBAAiB,YAAW,kBAAiB,mBAAkB,aAAY,mBAAkB,YAAW,aAAY,cAAa,eAAc,iBAAgB,kBAAiB,cAAa,eAAc,cAAa,eAAc,cAAa,eAAc,cAAa,eAAc,cAAa,eAAc,YAAW,aAAY,aAAY,cAAa,cAAa,eAAc,aAAY,cAAa,mBAAkB,kBAAkB;AAAxqB,IAA0qB,IAAE,EAAC,MAAK,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,OAAM,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,QAAO,CAAC,CAAC,EAAC;AAA7xB,IAA+xB,IAAE,EAAC,MAAK,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE,GAAE,OAAM,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,QAAO,CAAC,CAAC,EAAC;AAAl2B,IAAo2B,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAn9B,IAAq9B,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,oBAAkBA,GAAE,QAAQ,QAAMA;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAAS,cAAc,QAAQ,GAAEA,cAAa,SAAE,CAAC,GAAE,gBAAE,SAASA,IAAE,CAAC,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,YAAE,QAAM,EAAEA,GAAE,KAAK,GAAE,EAAE,SAAO,EAAEA,GAAE,MAAM,GAAE,IAAE,EAAE,WAAW,IAAI,GAAEA,cAAa,YAAU,EAAE,aAAaA,IAAE,GAAE,CAAC,IAAE,EAAE,UAAUA,IAAE,GAAE,CAAC,GAAE,EAAE,QAAM;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOA,cAAa,UAAG,IAAEA,GAAE,MAAM,MAAM,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,UAAU,MAAK,CAAC,GAAE,gBAAE,SAASA,EAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,GAAE,KAAI,EAAE,MAAM,WAAU,CAAC,QAAO,EAAE,KAAK,GAAE,GAAE,CAAC,CAAC,IAAE;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,SAAS,cAAc,QAAQ,GAAE,IAAE,EAAE,WAAW,IAAI,GAAE,EAAE,QAAM,EAAEA,GAAE,KAAK,GAAE,EAAE,SAAO,EAAEA,GAAE,MAAM,GAAE,EAAE,UAAUA,IAAE,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,aAAa,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOA,cAAa,mBAAiBA,cAAa,kBAAgB,CAAC,GAAE,EAAEA,EAAC,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,cAAEA,IAAE,EAAE,QAAM;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,GAAE,CAAC,GAAE,gBAAE,WAAW,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAGA,KAAE,KAAGA,MAAG,IAAI,OAAM,IAAI,MAAM,gDAAgD,OAAOA,EAAC,CAAC;AAAE,MAAG,CAAC,OAAO,UAAUA,EAAC,EAAE,OAAM,IAAI,MAAM,yCAAyC,OAAOA,EAAC,CAAC;AAAC;AAAC,IAAI,IAAE,EAAC,SAAQ,aAAY,iBAAgB,MAAG,oBAAmB,OAAG,oBAAmB,MAAG,WAAU,OAAM;AAAE,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,OAAKA;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,sBAAoB,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,KAAK,IAAI;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,EAAE,KAAK,IAAI,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,WAAS,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,EAAE,KAAK,IAAI,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,oBAAkB,WAAU;AAAC,WAAM;AAAA,EAAmB,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAEA,EAAC,GAAE;AAAQ;AAAC,IAAI,KAAG,WAAU;AAAC,WAAS,EAAE,GAAE;AAAC,QAAI,GAAEI,KAAE;AAAK,YAAO,KAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,OAAG,KAAK,eAAa,IAAI,YAAAJ,KAAE,EAAC,YAAW,SAASA,IAAEK,IAAE;AAAC,UAAG,EAAE,cAAa;AAAC,YAAID,KAAE,EAAE,aAAa,QAAQ,QAAO,EAAE;AAAE,eAAM,GAAG,OAAOA,IAAE,GAAG,EAAE,OAAOJ,EAAC;AAAA,MAAC;AAAC,aAAM,GAAG,OAAOK,IAAE,GAAG,EAAE,OAAOL,EAAC;AAAA,IAAC,EAAC,CAAC,GAAE,EAAE,WAAU;AAAA,MAAC,KAAI;AAAO,YAAE;AAAE;AAAA,MAAM,KAAI;AAAQ,YAAE;AAAE;AAAA,MAAM,KAAI;AAAA,MAAO;AAAQ,YAAE;AAAA,IAAC;AAAC,SAAK,aAAa,WAAW,EAAC,iBAAgB,GAAE,iBAAgB,EAAE,iBAAgB,oBAAmB,EAAE,oBAAmB,oBAAmB,EAAE,oBAAmB,YAAW,KAAK,WAAU,CAAC,GAAE,KAAK,aAAa,UAAW,SAASA,IAAE;AAAC,UAAGI,GAAE,SAAOJ,GAAE,MAAM,QAAOI,GAAE,QAAMJ,GAAE,MAAM,OAAM,QAAMA,GAAE,cAAc,CAAAI,GAAE,QAAM,CAAC;AAAA,WAAM;AAAC,YAAIH,KAAEG,GAAE,gBAAgBJ,GAAE,eAAcA,GAAE,kBAAkB;AAAE,QAAAA,GAAE,qBAAmBC,GAAE,eAAa,EAAC,kBAAiB,IAAG,MAAK,IAAI,GAAGD,GAAE,gBAAgB,EAAC,IAAGI,GAAE,QAAM,CAACH,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO,EAAE,UAAU,kBAAgB,SAASD,IAAE,GAAE;AAAC,QAAI,IAAE,MAAKI,KAAE,EAAC,WAAUJ,GAAE,IAAK,SAASA,IAAEC,IAAE;AAAC,aAAM,EAAC,GAAED,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,GAAEA,GAAE,GAAE,OAAMA,GAAE,YAAW,MAAK,EAAEC,EAAC,EAAC;AAAA,IAAC,CAAE,EAAC;AAAE,WAAO,QAAM,MAAIG,GAAE,cAAY,EAAE,IAAK,SAASJ,IAAEC,IAAE;AAAC,aAAM,EAAC,GAAED,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,GAAE,OAAMA,GAAE,YAAW,MAAK,EAAEC,EAAC,EAAC;AAAA,IAAC,CAAE,IAAGG;AAAA,EAAC,GAAE,EAAE,UAAU,gBAAc,SAASJ,IAAEI,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAOA,MAAGA,GAAE,kBAAgBA,GAAE,mBAAiB,KAAK,eAAa,KAAK,aAAWA,GAAE,gBAAe,KAAK,aAAa,WAAW,EAAC,YAAW,KAAK,WAAU,CAAC,IAAGJ,cAAa,UAAG,IAAE,UAAU,MAAK,CAAC,GAAE,gBAAE,SAASA,EAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,KAAI,EAAE,MAAM,WAAU,CAAC,QAAO,EAAE,KAAK,GAAEA,GAAE,MAAM,CAAC,GAAEA,GAAE,MAAM,CAAC,CAAC,CAAC,MAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAEA,IAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAOA,KAAE,GAAE,CAAC,GAAE,KAAK,aAAa,KAAK,EAAC,OAAMA,GAAC,GAAE,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,aAAa,MAAM;AAAA,EAAC,GAAE,EAAE,UAAU,QAAM,WAAU;AAAC,SAAK,aAAa,MAAM;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,WAAU;AAAC,WAAO,KAAK,aAAa,WAAW;AAAA,EAAC,GAAE;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,CAAC;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAOC,GAAE,UAAQ,aAAY,QAAMA,GAAE,uBAAqBA,GAAE,qBAAmB,EAAE,qBAAoB,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB,EAAE,kBAAiB,QAAMA,GAAE,uBAAqBA,GAAE,qBAAmB,EAAE,qBAAoB,QAAMA,GAAE,cAAYA,GAAE,YAAU,EAAE,YAAWA;AAAA,UAAC,EAAED,EAAC,GAAE,CAAC,IAAG,IAAE,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,cAAa,SAAE,EAAC,QAAOA,GAAE,MAAM,CAAC,GAAE,OAAMA,GAAE,MAAM,CAAC,EAAC,IAAE,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,IAAE,KAAK,KAAG,KAAK,OAAOA,KAAE,KAAK,OAAK,IAAE,KAAK,GAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,cAAa,SAAEA,KAAE,gBAAE,WAAWA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,GAAG,GAAE,iBAAiB,GAAE,CAAC,IAAE,EAAE,QAAMA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAM,IAAE,EAAE,SAAOA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAMA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAM,IAAE,EAAE,QAAMA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAO,IAAE,EAAE,SAAOA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAOA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAO,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,eAAE,OAAO,MAAIA,GAAE,OAAO,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,qBAAqB;AAAA,EAAC,CAAE,GAAE,aAAE,OAAO,MAAIA,GAAE,QAAQ,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,sBAAsB;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,kCAAiC,IAAE,EAAE,gCAA+B,IAAEA,GAAE,cAAa,IAAE,EAAE,kBAAkB,CAAC,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,kBAAkB,CAAC,EAAE,IAAE,EAAE,QAAO,IAAE,EAAE,kBAAkB,CAAC,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,kBAAkB,CAAC,EAAE,IAAE,EAAE,QAAO,IAAE,IAAE,KAAK,MAAM,IAAE,MAAI,IAAE,MAAI,IAAE,MAAI,IAAE,EAAE,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,QAAID,IAAEE,KAAEN,GAAE,cAAaE,KAAEG,GAAE,kCAAiCE,KAAEF,GAAE;AAA+B,IAAAD,KAAEC,GAAE,4BAA0BA,GAAE,4BAA0B,KAAK,KAAGA,GAAE,kCAAgC;AAAI,QAAIF,KAAEG,GAAE,kBAAkBJ,EAAC,EAAE,IAAED,GAAE,OAAMO,KAAEF,GAAE,kBAAkBJ,EAAC,EAAE,IAAED,GAAE,QAAOQ,KAAEH,GAAE,kBAAkBC,EAAC,EAAE,IAAEN,GAAE,OAAMS,KAAEJ,GAAE,kBAAkBC,EAAC,EAAE,IAAEN,GAAE;AAAO,WAAO,GAAGG,KAAE,KAAK,MAAM,EAAEM,KAAEF,KAAGC,KAAEN,EAAC,CAAC;AAAA,EAAC,EAAEH,IAAE,GAAE,CAAC;AAAE,SAAM,EAAC,SAAQ,IAAE,EAAE,OAAM,SAAQ,IAAE,EAAE,QAAO,OAAM,IAAE,EAAE,OAAM,QAAO,IAAE,EAAE,QAAO,UAAS,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,OAAKA,GAAE,OAAO,OAAM,IAAI,MAAM,mCAAmC,OAAOA,GAAE,MAAM,CAAC;AAAE,SAAM,CAAC,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,GAAE,CAACA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC;AAAE;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,KAAG,IAAE,KAAG,GAAE,KAAG,IAAE,KAAG,GAAE,KAAG,IAAE,KAAG,GAAE,KAAG,IAAE,KAAG,GAAE,KAAG,IAAE,KAAG,GAAE,KAAG,IAAE,KAAG;AAAE,SAAO,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,EAAC,gBAAe,MAAE;AAAG,WAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,IAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,IAAE,KAAG,IAAE,EAAE,IAAE,KAAG,IAAE,EAAE,iBAAe,IAAE,EAAE,UAAS,IAAE,KAAK,IAAI,CAAC,IAAE,IAAE,KAAK,IAAI,CAAC,IAAE,GAAE,IAAE,KAAK,IAAI,CAAC,IAAE,IAAE,KAAK,IAAI,CAAC,IAAE;AAAE,QAAE,IAAE,EAAE,QAAM,EAAE,SAAQ,IAAE,IAAE,EAAE,SAAO,EAAE;AAAQ,QAAI,IAAE,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAE,CAAC;AAAE,MAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAI,IAAE,SAASA,IAAEC,IAAEI,IAAE,GAAE;AAAC,QAAI,IAAEJ,KAAED,IAAE,IAAE,IAAEK;AAAE,QAAG,MAAI,EAAE,OAAM,IAAI,MAAM,iCAAiC,OAAOL,IAAE,sBAAsB,CAAC;AAAE,QAAI,IAAE,IAAE;AAAE,WAAM,EAAC,OAAM,GAAE,QAAOK,KAAEL,KAAE,EAAC;AAAA,EAAC,EAAE,GAAE,KAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,SAAO,KAAG,WAAU;AAAC,WAAO,IAAE,IAAEA,IAAE,EAAE,KAAK,GAAE,EAAE,MAAM;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,EAAE,kBAAiB,IAAE,EAAE,iBAAgB,IAAE,EAAE,YAAW,IAAE,EAAE,wBAAuB,IAAE,GAAGA,EAAC,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,WAAOA,KAAE,EAAC,SAAQA,GAAE,UAAQD,GAAE,OAAM,SAAQC,GAAE,UAAQD,GAAE,QAAO,OAAMC,GAAE,QAAMD,GAAE,OAAM,QAAOC,GAAE,SAAOD,GAAE,QAAO,UAASC,GAAE,SAAQ,IAAE,EAAC,SAAQ,MAAGD,GAAE,OAAM,SAAQ,MAAGA,GAAE,QAAO,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,UAAS,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,QAAG,WAASA,OAAIA,KAAE,QAAI,CAACA,GAAE,QAAM,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,QAAID,KAAEH,GAAE,QAAO,IAAEA,GAAE;AAAM,OAAGA,IAAE,YAAY,GAAE,GAAGD,IAAE,KAAK;AAAE,QAAIE,IAAEK,IAAE,IAAEH,KAAE,GAAE,IAAEJ,GAAE,SAAOA,GAAE,OAAM,IAAE,GAAE,IAAE;AAAE,WAAO,IAAE,KAAGE,KAAEF,GAAE,OAAMO,KAAEP,GAAE,QAAM,GAAE,KAAG,IAAE,IAAE,KAAG,MAAIE,KAAEF,GAAE,SAAO,GAAEO,KAAEP,GAAE,QAAO,KAAG,IAAE,IAAE,KAAG,IAAGA,GAAE,QAAME,IAAEF,GAAE,SAAOO,IAAE,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAA,EAAC,EAAE,GAAE,GAAE,CAAC,GAAE,KAAG,IAAE,GAAE,IAAE,EAAE,OAAM,IAAE,EAAE,QAAO,IAAE,OAAG,IAAE,EAAE,OAAM,IAAE,EAAE,QAAO,IAAE,IAAE,KAAG,GAAE,IAAE,KAAK,IAAI,EAAE,QAAQ,GAAE,IAAE,KAAK,IAAI,EAAE,QAAQ,GAAE,IAAE,EAAE,SAAQ,IAAE,EAAE,SAAQ,IAAE,IAAE,GAAE,IAAE,IAAE,IAAG,IAAE,IAAI,MAAM,EAAE,GAAG,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,CAAC,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,IAAE,MAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,MAAG,IAAE,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,EAAE,IAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,GAAG,CAAC;AAAG,SAAM,EAAC,aAAY,KAAG,WAAU;AAAC,QAAIN,KAAE,GAAGD,EAAC,GAAEK,KAAE,SAAE,GAAG,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAED,KAAE,WAAS,IAAE,aAAW,WAAU,IAAE,MAAE,UAAU,WAAE,KAAEH,IAAE,SAAS,CAAC,GAAEI,IAAE,YAAWD,IAAE,GAAE,CAAC,EAAE,QAAO,EAAE,KAAK,CAAC;AAAE,WAAO,QAAM,IAAE,GAAG,GAAE,CAAC,IAAE;AAAA,EAAC,CAAE,GAAE,SAAQ,GAAE,sBAAqB,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,MAAI,IAAE,OAAIA,KAAE,KAAGA,MAAG,IAAEA,MAAG,KAAG,IAAE;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,KAAG,WAAU;AAAC,QAAI,IAAE,SAASA,IAAE;AAAC,aAAO,KAAG,WAAU;AAAC,eAAM,CAAC,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,CAAC,GAAE,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,EAAEA,EAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,WAAM,EAAC,OAAM,QAAE,CAAC,GAAE,QAAO,QAAE,CAAC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,QAAMA,GAAE;AAAW;AAAC,SAAS,GAAGA,IAAE;AAAC,WAAQ,IAAE,EAAC,cAAa,EAAC,mBAAkB,CAAC,EAAC,EAAC,GAAE,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,GAAE,IAAEA,GAAE,QAAO,EAAE,GAAE;AAAC,QAAI,IAAEA,GAAE,CAAC;AAAE,QAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,EAAE,aAAa,kBAAkB,KAAK,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,aAAa,sBAAoB,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,IAAE,GAAE,QAAO,IAAE,EAAC,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAII,IAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOJ,GAAE,KAAM,SAASA,IAAEC,IAAE;AAAC,mBAAO,KAAK,IAAI,MAAM,MAAKA,GAAE,KAAK,IAAE,KAAK,IAAI,MAAM,MAAKD,GAAE,KAAK;AAAA,UAAC,CAAE,GAAEI,KAAE,SAAEJ,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAM,CAACA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,IAAI;AAAA,UAAC,CAAE,CAAC,GAAE,IAAE,SAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAOA,GAAE,MAAM,CAAC;AAAA,UAAC,CAAE,CAAC,GAAE,CAAC,GAAE,MAAE,uBAAuBI,IAAE,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,IAAG,IAAE,EAAE,KAAK,GAAG,MAAM,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAEJ,GAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,mBAAO,EAAE,QAAQA,EAAC,IAAE;AAAA,UAAE,CAAE,GAAE,QAAE,CAACG,IAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGJ,IAAE,GAAE;AAAC,SAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,QAAI,IAAE,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,GAAEA,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,OAAM,CAAC;AAAE,WAAO,QAAMA,GAAE,MAAI,EAAE,IAAEA,GAAE,IAAE,EAAE,QAAO;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,cAAG,IAAE,QAAE,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAEA,GAAE,WAAS,EAAE,OAAM,IAAI,MAAM,4GAA0G,GAAG,OAAOA,GAAE,QAAO,oBAAoB,EAAE,OAAO,CAAC,CAAC;AAAE,iBAAO,IAAE,CAAC,GAAE,CAAC,GAAE,EAAE,OAAO,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAI,KAAG,IAAEA,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC,GAAE,IAAE,KAAK,MAAM,EAAE,IAAE,CAAC,GAAE,IAAE,KAAK,MAAM,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,KAAG,KAAG,KAAG,IAAE,KAAG,KAAG,IAAG;AAAC,iBAAI,IAAE,KAAK,OAAO,EAAE,aAAW,KAAG,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,MAAI,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,KAAE,EAAE,IAAI,GAAE,GAAE,CAAC,GAAE,KAAG,GAAE,IAAE,KAAK,IAAI,GAAE,CAAC,GAAE,KAAG,IAAE,GAAE,KAAG,IAAE;AAAE,iBAAG,EAAE,yBAAuB,IAAE,MAAI,EAAE,IAAE,IAAE,IAAE,GAAE,EAAE,IAAE,IAAE,IAAE;AAAA,UAAE;AAAC,iBAAO,EAAE,QAAQ,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,EAAE,KAAI,IAAE,EAAE,OAAK,EAAE,OAAM,IAAE,EAAE,MAAI,EAAE;AAAO,SAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,WAAO,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,IAAGA,GAAE,IAAE,MAAI,IAAE,IAAG,IAAGA,GAAE,IAAE,MAAI,IAAE,IAAG,GAAEA,GAAE,KAAG,IAAE,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAM,YAAU,WAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,QAAI,IAAEA,GAAE,yBAAyB,QAAQ,CAAC,GAAE,IAAE,EAAC,eAAc,CAAC,YAAW,SAAS,GAAE,aAAYL,GAAE,OAAM,UAAS,ohCAAohC,OAAO,GAAE,8CAA8C,EAAC,GAAE,IAAE,QAAE;AAAE,WAAO,KAAG,WAAU;AAAC,UAAIK,KAAE,EAAE,cAAc,GAAE,CAACL,IAAEC,EAAC,CAAC;AAAE,aAAO,OAAE,EAAE,qBAAqBI,GAAE,QAAOA,GAAE,OAAMA,GAAE,KAAK;AAAA,IAAC,CAAE;AAAA,EAAC,EAAEL,IAAE,GAAE,CAAC,IAAE,KAAG,WAAU;AAAC,QAAI,IAAE,IAAE,GAAE,GAAE,GAAE,IAAE,OAAE,CAAC,GAAE,IAAE,IAAE,GAAE,QAAE,GAAE,IAAE,GAAE,IAAE,SAAQ,IAAE,GAAE,IAAE,WAAS,IAAE,GAAE,IAAE,UAAS,IAAE,GAAE,IAAE,SAAQ,IAAE,GAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAE,WAAO,IAAE,GAAE,IAAE,IAAEA,IAAE,CAAC,GAAE,IAAE,GAAE,EAAE,wBAAwB,CAAC,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,mBAAO,KAAG,WAAU;AAAC,kBAAID,IAAE,GAAED,IAAEK;AAAE,cAAAH,GAAE,sBAAoB,IAAE,QAAE,MAAEL,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAED,KAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEG,KAAE,QAAE,MAAER,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEF,KAAE,QAAE,MAAEH,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAID,KAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEL,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEF,KAAE,QAAE,MAAEH,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEG,KAAE,QAAE,MAAER,IAAE,CAAC,GAAEK,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,IAAG,IAAE,IAAE,IAAE,IAAE,GAAEA,GAAE,MAAM,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,KAAE,IAAE,IAAE,IAAEA,IAAEC,GAAE,MAAM,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEI,GAAE,6BAA2BF,KAAE,IAAE,IAAE,IAAEA,IAAEE,GAAE,MAAM,CAAC,GAAEJ,GAAE,CAAC,GAAEO,KAAE,IAAE,IAAE,IAAEA,IAAEH,GAAE,MAAM,CAAC,GAAEJ,GAAE,CAAC,MAAIE,KAAE,IAAE,IAAEA,IAAEE,GAAE,MAAM,GAAEJ,GAAE,CAAC,GAAEO,KAAE,IAAE,IAAEA,IAAEH,GAAE,MAAM,GAAEJ,GAAE,CAAC;AAAG,kBAAIQ,KAAE,IAAEL,IAAE,IAAED,IAAE,CAAC,CAAC,GAAEO,KAAE,IAAE,GAAE,IAAEF,IAAE,CAAC,CAAC,GAAE,IAAE,IAAEJ,IAAE,IAAED,IAAE,CAAC,CAAC,GAAEQ,KAAE,IAAE,GAAE,IAAEH,IAAE,CAAC,CAAC,GAAE,IAAE,OAAE,CAAC,QAAEC,IAAE,CAACJ,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEK,IAAE,CAACL,GAAE,UAAS,CAAC,CAAC,GAAE,QAAE,GAAE,CAACA,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEM,IAAE,CAACN,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAE,kBAAGA,GAAE,aAAa,UAAQ,IAAE,GAAE,IAAEA,GAAE,cAAa,EAAE,GAAE;AAAC,oBAAI,IAAEA,GAAE,sBAAoB,IAAEA,GAAE,sBAAqB,IAAE,QAAO,IAAE;AAAO,gBAAAA,GAAE,sBAAoB,IAAE,QAAE,MAAEL,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAI,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC;AAAG,oBAAI,IAAE,IAAE,IAAE,IAAE,GAAEK,GAAE,MAAM,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,IAAE,IAAE,IAAE,IAAE,GAAEI,GAAE,MAAM,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC;AAAE,oBAAE,OAAE,CAAC,GAAE,QAAE,GAAE,CAACI,GAAE,UAAS,CAAC,CAAC,GAAE,QAAE,GAAE,CAACA,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAC,CAAE;AAAA,UAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,KAAG,WAAU;AAAC,gBAAIL,KAAE;AAAE,mBAAO,EAAE,gBAAc,QAAM,EAAE,wBAAsBA,KAAE,YAAE,GAAE,CAAC,EAAE,qBAAoB,EAAE,mBAAmB,IAAGA,KAAE,QAAEA,EAAC,KAAGA;AAAA,UAAC,CAAE,GAAE,CAAC,GAAE,GAAG,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,QAAE,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,CAAC,GAAE,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,GAAE,IAAE,EAAE,UAAS,EAAE,EAAE,KAAG,EAAE,QAAM,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,mBAAiB,IAAE,IAAE,EAAE,WAAU,IAAE,GAAG,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,gBAAe,CAAC,IAAG,IAAE,EAAE,aAAa,qBAAqB,QAAM,KAAG,EAAE,SAAO,KAAI;AAAC,gBAAG,EAAE,eAAa,EAAE,OAAK,IAAE,EAAE,cAAc,oBAAkB,CAAC,GAAE,IAAE,EAAE,eAAa,EAAE,sBAAqB,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,qBAAqB,KAAE,IAAE,EAAE,sBAAoB,GAAE,IAAE,EAAC,GAAE,EAAE,IAAE,CAAC,GAAE,GAAE,EAAE,iBAAe,IAAE,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,EAAC,GAAE,EAAE,kBAAkB,KAAK,CAAC;AAAE,cAAE,KAAK,CAAC;AAAA,UAAC;AAAC,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,OAAM,CAAC,CAAC,GAAE,KAAI,GAAE,cAAa,EAAC,qBAAoB,EAAC,MAAK,GAAE,MAAK,IAAE,IAAE,IAAEA,IAAE,MAAK,GAAE,MAAK,IAAE,IAAEA,KAAE,GAAE,OAAM,IAAE,GAAE,QAAO,IAAEA,GAAC,EAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAM,WAASA,KAAE,IAAE,SAASA,IAAE;AAAC,WAAO,KAAG,IAAE,KAAK,IAAI,CAACA,EAAC;AAAA,EAAE,EAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,KAAG,EAAE,oBAAkB,OAAG,IAAE,KAAG,EAAE,kBAAgB,OAAG,IAAEA,GAAE,MAAK,IAAE,IAAE,EAAE,cAAa,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,cAAa,EAAE,EAAE,KAAE,IAAE,IAAG,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAG,IAAE,IAAE,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,MAAI,EAAE,IAAE,IAAE,EAAE,mBAAiB,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,IAAG,IAAE,MAAI,EAAE,IAAE,EAAE,IAAE,CAAC,IAAG,IAAE,MAAI,EAAE,QAAM,GAAG,EAAE,sBAAqB,EAAE,IAAE,CAAC,CAAC,IAAG,EAAE,KAAK,CAAC;AAAE,eAAI,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,EAAC,IAAE,EAAE,CAAC,GAAG,IAAE,EAAE,IAAE,EAAE,iBAAgB,EAAE,IAAE,EAAE,IAAE,EAAE,kBAAiB,EAAE,IAAE,EAAE,IAAE,EAAE,mBAAiB,EAAE,cAAY;AAAG,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAEA,GAAE,OAAM,IAAEA,GAAE,QAAO,IAAEA,GAAE;AAAS,MAAG,QAAM,EAAE,YAAU,QAAM,EAAE,mBAAiB,IAAE,SAASA,IAAEC,IAAE;AAAC,YAAMA,GAAE,WAASD,MAAGC,GAAE,WAAS,QAAMA,GAAE,mBAAiBD,MAAG,KAAK,KAAGC,GAAE,iBAAe;AAAK,WAAO,GAAGD,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,IAAG,MAAI,EAAE,CAAAA,GAAE,UAAQA,GAAE,UAAQ,IAAE,EAAE,QAAOA,GAAE,UAAQA,GAAE,UAAQ,IAAE,EAAE;AAAA,OAAW;AAAC,QAAI,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,IAAE,EAAE,SAAO,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,KAAG,EAAE,OAAM,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,IAAE,EAAE,SAAO,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,KAAG,EAAE;AAAO,IAAAA,GAAE,UAAQA,GAAE,UAAQ,GAAEA,GAAE,UAAQA,GAAE,UAAQ;AAAA,EAAC;AAAC,MAAG,EAAE,YAAW;AAAC,QAAI,IAAE,KAAK,IAAI,IAAE,EAAE,OAAM,IAAE,EAAE,MAAM;AAAE,QAAE,IAAE,EAAE,OAAM,IAAE,IAAE,EAAE;AAAA,EAAM,WAAS,EAAE,aAAY;AAAC,QAAI,IAAE,KAAK,IAAI,IAAE,EAAE,OAAM,IAAE,EAAE,MAAM;AAAE,QAAE,IAAE,EAAE,OAAM,IAAE,IAAE,EAAE;AAAA,EAAM;AAAC,SAAOA,GAAE,QAAM,IAAE,EAAE,QAAOA,GAAE,SAAO,IAAE,EAAE,QAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,QAAI,IAAE,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,GAAEA,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,OAAM,CAAC;AAAE,WAAO,QAAMA,GAAE,MAAIA,GAAE,IAAEA,GAAE,IAAE,EAAE,QAAO;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,QAAMA,IAAE,KAAK,cAAY;AAAA,EAAE;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE;AAAC,QAAI;AAAE,WAAO,KAAK,cAAY,IAAE,QAAM,IAAE,KAAK,cAAY,KAAK,SAAOA,KAAE,KAAK,eAAa,KAAK,cAAY,KAAK,QAAM,IAAE,KAAK,OAAOA,KAAE,KAAK,eAAa,CAAC,KAAG,IAAEA,IAAE,KAAK,cAAY,OAAI,KAAK,WAASA,IAAE,KAAK,cAAY,GAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,iBAAe,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,KAAK,QAAM,GAAE,KAAK,MAAMA,IAAE,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,kBAAgB,WAAU;AAAC,WAAO,KAAK;AAAA,EAAW,GAAEA,GAAE,UAAU,eAAa,WAAU;AAAC,WAAO,KAAK;AAAA,EAAQ,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,cAAY;AAAA,EAAE,GAAEA;AAAC,EAAE;AAAjkB,IAAmkB,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,YAAUA,GAAE,WAAU,KAAK,YAAUA,GAAE,WAAU,KAAK,OAAKA,GAAE,MAAK,KAAK,kBAAgBA,GAAE,iBAAgB,KAAK,gBAAcA,GAAE,eAAc,KAAK,iBAAeA,GAAE,gBAAe,KAAK,IAAE,IAAI,GAAG,KAAK,SAAS,KAAK,SAAS,CAAC,GAAE,KAAK,KAAG,IAAI,GAAG,KAAK,SAAS,KAAK,cAAc,CAAC,GAAE,KAAK,gBAAc;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE,GAAE;AAAC,QAAG,QAAMA,GAAE,QAAOA;AAAE,QAAI,IAAE,KAAK,MAAM,CAAC;AAAE,QAAG,KAAK,iBAAe,EAAE,QAAOA;AAAE,UAAI,KAAK,iBAAe,MAAI,MAAI,KAAK,YAAU,KAAG,QAAM,IAAE,KAAK,kBAAiB,KAAK,gBAAc;AAAE,QAAI,IAAE,KAAK,EAAE,gBAAgB,KAAGA,KAAE,KAAK,EAAE,aAAa,KAAG,IAAE,KAAK,YAAU,GAAE,IAAE,KAAK,GAAG,eAAe,GAAE,KAAK,SAAS,KAAK,cAAc,CAAC,GAAE,IAAE,KAAK,YAAU,KAAK,OAAK,KAAK,IAAI,CAAC,GAAE,IAAE,QAAM,KAAK,kBAAgB,KAAK,kBAAgB,KAAK,gBAAc,KAAK,IAAI,CAAC,IAAE;AAAK,WAAO,KAAK,EAAE,eAAeA,IAAE,KAAK,SAAS,CAAC,GAAE,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,WAAS,SAASA,IAAE;AAAC,WAAO,KAAG,IAAE,KAAK,aAAW,IAAE,KAAK,KAAGA;AAAA,EAAG,GAAEA;AAAC,EAAE;AAA7+C,IAA++C,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE,GAAE;AAAC,QAAI,IAAE;AAAK,QAAG,QAAMA,GAAE,QAAO,KAAK,MAAM,GAAE;AAAK,SAAK,yBAAyBA,EAAC;AAAE,QAAI,IAAE;AAAE,QAAG,CAAC,KAAK,OAAO,qBAAoB;AAAC,UAAG,IAAE,KAAK,OAAO,sBAAsB,QAAO,EAAE,CAAC,GAAEA,IAAE,IAAE;AAAE,UAAE,IAAE;AAAA,IAAC;AAAC,WAAOA,GAAE,IAAK,SAASA,IAAEK,IAAE;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAEL,EAAC,GAAE,EAAC,GAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,EAAC,CAAC;AAAE,aAAO,QAAMA,GAAE,MAAI,EAAE,IAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,IAAG;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS;AAAA,EAAI,GAAEA,GAAE,UAAU,2BAAyB,SAASA,IAAE;AAAC,QAAI,IAAE;AAAK,YAAM,KAAK,YAAU,KAAK,SAAS,WAASA,GAAE,WAAS,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAE,GAAEA;AAAC,EAAE;AAAzyE,IAA2yE,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,SAAO,CAAC,GAAE,KAAK,gBAAc,IAAI,GAAG,CAAC,GAAE,KAAK,YAAU,GAAE,KAAK,iBAAe,GAAE,KAAK,gBAAc;AAAA,EAAE;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE,GAAE;AAAC,QAAG,QAAMA,GAAE,QAAOA;AAAE,QAAI,GAAE,IAAE,KAAK,MAAM,CAAC;AAAE,QAAG,KAAK,iBAAe,EAAE,QAAOA;AAAE,QAAG,OAAK,KAAK,cAAc,KAAE;AAAA,SAAM;AAAC,eAAQ,IAAEA,KAAE,IAAE,KAAK,YAAU,KAAK,gBAAe,IAAE,IAAE,KAAK,eAAc,IAAE,GAAE,IAAE,GAAE,KAAG,IAAE,KAAK,OAAO,WAAS,MAAI,KAAI,IAAE,GAAE,IAAE,KAAK,QAAO,IAAE,EAAE,QAAO,KAAI;AAAC,YAAI,IAAE,EAAE,CAAC;AAAE,YAAG,IAAE,EAAE,WAAS,EAAE;AAAM,aAAG,EAAE,UAAS,KAAG,EAAE;AAAA,MAAQ;AAAC,UAAI,IAAE,KAAG,OAAK;AAAG,UAAE,IAAE,KAAG,IAAE,KAAK,OAAO,gBAAc,KAAK,IAAI,CAAC,IAAG,KAAK,OAAO,QAAQ,EAAC,UAAS,GAAE,UAAS,EAAC,CAAC,GAAE,KAAK,OAAO,SAAO,KAAK,OAAO,cAAY,KAAK,OAAO,IAAI;AAAA,IAAC;AAAC,WAAO,KAAK,YAAUA,IAAE,KAAK,iBAAe,GAAE,KAAK,gBAAc,GAAE,KAAK,cAAc,eAAeA,IAAE,CAAC;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAnkG,IAAqkG,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE,GAAE;AAAC,QAAI,IAAE;AAAK,QAAG,QAAMA,GAAE,QAAO,KAAK,MAAM,GAAE;AAAK,QAAI,IAAE;AAAE,QAAG,CAAC,KAAK,OAAO,qBAAoB;AAAC,UAAG,IAAE,KAAK,OAAO,sBAAsB,QAAO,EAAE,CAAC,GAAEA,IAAE,IAAE;AAAE,UAAE,IAAE;AAAA,IAAC;AAAC,WAAO,KAAK,yBAAyBA,EAAC,GAAEA,GAAE,IAAK,SAASA,IAAEK,IAAE;AAAC,UAAI,IAAE,EAAE,EAAE,CAAC,GAAEL,EAAC,GAAE,EAAC,GAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,EAAC,CAAC;AAAE,aAAO,QAAMA,GAAE,MAAI,EAAE,IAAE,EAAE,SAASK,EAAC,EAAE,MAAML,GAAE,GAAE,GAAE,CAAC,IAAG;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS;AAAA,EAAI,GAAEA,GAAE,UAAU,2BAAyB,SAASA,IAAE;AAAC,QAAI,IAAE;AAAK,YAAM,KAAK,YAAU,KAAK,SAAS,WAASA,GAAE,WAAS,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,WAASA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAE,GAAEA;AAAC,EAAE;AAA/3H,IAAi4H,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,QAAG,QAAMA,GAAE,eAAe,MAAK,kBAAgB,IAAI,GAAGA,GAAE,cAAc;AAAA,SAAM;AAAC,UAAG,QAAMA,GAAE,cAAc,OAAM,IAAI,MAAM,+DAA6D,GAAG,OAAOA,IAAE,GAAG,CAAC;AAAE,WAAK,kBAAgB,IAAI,GAAGA,GAAE,aAAa;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE,GAAE,GAAE,GAAE;AAAC,QAAG,WAAS,MAAI,IAAE,QAAI,QAAMA,GAAE,QAAO,KAAK,gBAAgB,MAAM,GAAE;AAAK,QAAI,IAAE,QAAM,IAAE,SAASA,IAAEC,IAAE;AAAC,cAAOD,GAAE,QAAMC,GAAE,QAAMD,GAAE,SAAOC,GAAE,UAAQ;AAAA,IAAC,EAAE,GAAE,CAAC,IAAE,GAAE,IAAE,IAAE,GAAGD,IAAE,CAAC,IAAEA,IAAE,IAAE,KAAK,gBAAgB,MAAM,GAAE,GAAE,CAAC;AAAE,WAAO,IAAE,GAAG,GAAE,CAAC,IAAE;AAAA,EAAC,GAAEA;AAAC,EAAE;AAA75I,IAA+5I,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,QAAMA,GAAE;AAAA,EAAK;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE;AAAC,QAAI,IAAE;AAAK,QAAG,QAAMA,GAAE,QAAO,KAAK,oBAAkB,MAAK;AAAK,YAAM,KAAK,qBAAmB,KAAK,kBAAkB,WAASA,GAAE,WAAS,KAAK,oBAAkBA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAO,IAAI,GAAG,EAAE,KAAK;AAAA,IAAC,CAAE;AAAG,aAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAO,EAAE,GAAE;AAAC,UAAI,IAAEA,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,CAAC;AAAE,QAAE,QAAM,KAAK,kBAAkB,CAAC,EAAE,MAAM,EAAE,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC,GAAEA;AAAC,EAAE;AAA7zJ,IAA+zJ,KAAG,EAAC,0BAAyB,OAAG,8BAA6B,GAAE,kBAAiB,CAAC,GAAE,iBAAgB,CAAC,GAAE,WAAU,GAAE,UAAS,WAAS,UAAS,MAAI,iBAAgB,KAAI,gBAAe,KAAI,eAAc,KAAG,eAAc,KAAG,SAAQ,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,cAAa,CAAC,CAAC,GAAE,iBAAgB,KAAE;AAAplK,IAAslK,KAAG,EAAC,SAAQ,QAAO,WAAU,QAAO,iBAAgB,MAAG,oBAAmB,OAAG,oBAAmB,MAAG,kBAAiB,kEAAiE,kBAAiB,sEAAqE;AAAj2K,IAAm2K,KAAG,EAAC,UAAS,GAAE,gBAAe,MAAE;AAAn4K,IAAq4K,KAAG,EAAC,2BAA0B,OAAG,gBAAe,OAAG,eAAc,CAAC,GAAE,YAAW,GAAE,UAAS,MAAK,WAAU,IAAG,gBAAe,GAAE,qBAAoB,GAAE,cAAa,GAAE,sBAAqB,GAAE,cAAa,MAAG,qBAAoB,KAAI,oBAAmB,MAAG,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,IAAE;AAAzrL,IAA2rL,KAAG;AAA9rL,IAAisL,KAAG,EAAC,QAAO,GAAE,QAAO,GAAE,QAAO,MAAK,QAAO,MAAK,YAAW,KAAE;AAA5vL,IAA8vL,KAAG,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,IAAG,CAAC,GAAE,YAAW,OAAM;AAA52L,IAA82L,KAAG,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,GAAE,CAAC,GAAE,YAAW,OAAM;AAA39L,IAA69L,KAAG,EAAC,cAAa,IAAG,iBAAgB,KAAI,kBAAiB,KAAI,sBAAqB,WAAU,kBAAiB,OAAG,gBAAe,MAAE;AAA9lM,IAAgmM,KAAG,EAAC,cAAa,IAAG,iBAAgB,GAAE,kBAAiB,GAAE,sBAAqB,WAAU,kBAAiB,OAAG,gBAAe,MAAE;AAA7tM,IAA+tM,KAAG,EAAC,YAAW,GAAE,uBAAsB,IAAE;AAAxwM,IAA0wM,KAAG,EAAC,OAAM,IAAE;AAAtxM,IAAwxM,KAAG,EAAC,eAAc,EAAC,WAAU,IAAG,WAAU,MAAI,MAAK,IAAG,gBAAe,GAAE,uBAAsB,KAAI,EAAC;AAA13M,IAA43M,KAAG,EAAC,eAAc,EAAC,WAAU,IAAG,WAAU,MAAI,MAAK,IAAG,gBAAe,GAAE,uBAAsB,KAAI,EAAC;AAA99M,IAAg+M,KAAG,EAAC,eAAc,EAAC,WAAU,IAAG,WAAU,KAAG,MAAK,IAAG,gBAAe,GAAE,uBAAsB,MAAK,qBAAoB,KAAE,EAAC;AAAxlN,IAA0lN,KAAG,EAAC,YAAW,OAAM;AAA/mN,IAAinN,KAAG,EAAC,0BAAyB,IAAE;AAAE,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,OAAKA;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,sBAAoB,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,EAAE,KAAK,IAAI,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,EAAE,KAAK,IAAI,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,WAAS,WAAU;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAASA,IAAE;AAAC,eAAM,CAAC,GAAE,KAAK,IAAI;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,oBAAkB,WAAU;AAAC,WAAM;AAAA,EAAQ,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAEA,EAAC,GAAE;AAAQ;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAK,gBAAcA,IAAE,KAAK,gBAAc,GAAE,KAAK,kBAAgB,GAAE,KAAK,qBAAmB,GAAE,KAAK,qBAAmB,GAAE,KAAK,YAAU,GAAE,KAAK,mBAAiB,MAAK,KAAK,+BAA6B,MAAK,KAAK,UAAQ,SAASA,IAAE;AAAC,cAAMA,GAAE,6BAA2BA,GAAE,2BAAyB,QAAI,QAAMA,GAAE,iCAA+BA,GAAE,+BAA6B,IAAG,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB;AAAI,eAAQC,KAAE,CAAC,GAAEI,KAAE,GAAEA,KAAEL,GAAE,aAAW;AAAC,iBAAQI,KAAE,CAAC,GAAEE,KAAE,CAAC,GAAEJ,KAAE,CAAC,GAAEK,KAAE,CAAC,GAAE,IAAEF,IAAE,IAAEL,GAAE,QAAQ,UAAQA,GAAE,QAAQ,CAAC,MAAIA,GAAE,QAAQK,EAAC,KAAG;AAAC,cAAIG,KAAE,GAAGR,GAAE,UAASA,GAAE,UAAS,GAAEA,GAAE,QAAQ,MAAM;AAAE,cAAG,MAAI,KAAGA,GAAE,yBAAyB,CAAAE,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,GAAE,GAAEK,GAAE,KAAK,GAAE,GAAEA,GAAE,KAAKC,EAAC,GAAED,GAAE,KAAKC,EAAC;AAAA,eAAM;AAAC,qBAAQC,KAAE,GAAEA,KAAET,GAAE,aAAa,QAAO,EAAES,GAAE,CAAAP,GAAE,KAAKF,GAAE,aAAaS,EAAC,CAAC,GAAEF,GAAE,KAAKC,EAAC;AAAE,gBAAGR,GAAE,+BAA6B,GAAE;AAAC,kBAAIU,KAAE,MAAIV,GAAE,QAAQ,SAAO,IAAE,IAAE,GAAGA,GAAE,UAASA,GAAE,UAAS,IAAE,GAAEA,GAAE,QAAQ,MAAM;AAAE,cAAAO,GAAE,KAAK,KAAK,KAAKC,KAAEE,EAAC,CAAC,GAAER,GAAE,KAAKF,GAAE,4BAA4B;AAAA,YAAC;AAAA,UAAC;AAAC;AAAA,QAAG;AAAC,iBAAQ,IAAE,GAAE,IAAEE,GAAE,QAAO,EAAE,GAAE;AAAC,cAAI,IAAE,KAAK,KAAKA,GAAE,CAAC,CAAC;AAAE,UAAAE,GAAE,KAAKG,GAAE,CAAC,IAAE,CAAC,GAAED,GAAE,KAAKC,GAAE,CAAC,IAAE,CAAC;AAAA,QAAC;AAAC,YAAI,IAAE,GAAE,IAAE;AAAE,YAAGP,GAAE,iBAAiB,SAAO,EAAE,KAAEA,GAAE,iBAAiBK,EAAC,GAAE,IAAEL,GAAE,gBAAgBK,EAAC;AAAA,aAAM;AAAC,cAAI,IAAEL,GAAE,QAAQK,EAAC;AAAE,cAAE,KAAK,KAAKL,GAAE,kBAAgB,CAAC,GAAE,IAAE,KAAK,KAAKA,GAAE,iBAAe,CAAC;AAAA,QAAC;AAAC,iBAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAEI,GAAE,QAAO,EAAE,GAAE;AAAC,cAAI,IAAE,EAAC,UAAS,IAAEJ,GAAE,iBAAe,GAAE,UAAS,IAAEA,GAAE,iBAAe,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,UAAAA,GAAE,mBAAiB,EAAE,QAAM,GAAE,EAAE,SAAO,MAAI,EAAE,QAAMM,GAAE,CAAC,GAAE,EAAE,SAAOF,GAAE,CAAC,IAAGH,GAAE,KAAK,CAAC;AAAA,QAAC;AAAC,QAAAI,KAAE;AAAA,MAAC;AAAC,aAAOJ;AAAA,IAAC,EAAE,EAAE;AAAE,QAAI,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASD,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAK,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAM,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC;AAAE,SAAK,eAAa,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,KAAK,+BAA6B,KAAK,qBAAmB,SAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,IAAE;AAAA,EAAI;AAAC,SAAOA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,SAASA,IAAE;AAAC,kBAAIC;AAAE,kBAAG,SAAOA,KAAE,QAAMD,KAAE,KAAG,EAAE,CAAC,GAAEA,EAAC,GAAG,aAAWC,GAAE,WAAS,IAAGA,GAAE,YAAU,EAAE,OAAM,IAAI,MAAM,oBAAoB,OAAOA,GAAE,UAAS,kBAAkB,CAAC;AAAE,kBAAGA,GAAE,WAAS,EAAE,OAAM,IAAI,MAAM,wEAAwE;AAAE,qBAAOA;AAAA,YAAC,EAAE,CAAC,GAAE,QAAMD,MAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,KAAK,WAAS,EAAE,UAAS,KAAK,YAAU,QAAM,IAAE,MAAI,IAAE,GAAGA,EAAC,IAAE,MAAIA,GAAE,cAAY,MAAK,IAAE,GAAGA,EAAC,GAAE,IAAE,KAAG,WAAU;AAAC,qBAAO,KAAE,GAAGA,EAAC,GAAE,SAAS;AAAA,YAAC,CAAE,GAAE,SAAO,IAAE,KAAK,oBAAkB,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,KAAK,WAAW,CAAC,CAAC;AAAA,UAAG,KAAK;AAAE,gBAAG,OAAK,IAAE,EAAE,KAAK,GAAG,OAAO,QAAO,KAAK,MAAM,GAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,gBAAE,EAAE,CAAC,GAAE,IAAE,KAAK,mBAAmB,GAAE,CAAC,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,KAAK,mBAAmB,GAAE,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,EAAE,QAAQ,GAAE,QAAM,KAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,IAAE,EAAE,WAAU,IAAE,EAAE,oBAAmB,IAAE,EAAE,WAAU,IAAE,EAAE,gBAAe,IAAE,EAAE,kBAAiB,IAAE,KAAK,sBAAsB,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,yBAAwB,IAAE,EAAE,4BAA2B,IAAE,EAAE,8BAA6B,IAAE,KAAK,mBAAmB,GAAE,CAAC,GAAE,KAAK,mBAAiB,GAAE,IAAE,KAAK,sBAAoB,QAAM,IAAE,KAAK,0BAA0B,CAAC,IAAE,GAAE,SAAO,IAAE,QAAM,IAAE,GAAG,GAAE,CAAC,IAAE,SAAO,EAAE,QAAS,SAASA,IAAEC,IAAE;AAAC,cAAAD,GAAE,OAAK,EAAEC,EAAC;AAAA,YAAC,CAAE,GAAE,SAAO,IAAE,MAAI,EAAE,QAAS,SAASD,IAAEC,IAAE;AAAC,cAAAD,GAAE,OAAK,EAAEC,EAAC;AAAA,YAAC,CAAE,GAAE,IAAE,EAAC,OAAM,GAAE,WAAU,GAAE,aAAY,EAAC,GAAE,SAAO,MAAI,IAAE,KAAG,WAAU;AAAC,kBAAID,KAAE,WAAE,GAAE,CAAC,GAAEC,KAAE,IAAED,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC;AAAE,qBAAO,UAAEC,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,WAAW;AAAA,YAAC,CAAE,GAAE,KAAK,sBAAoB,QAAE,CAAC,GAAE,IAAE,EAAC,kBAAiB,IAAG,MAAK,IAAI,GAAG,CAAC,EAAC,GAAE,EAAE,eAAa,IAAG,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,QAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAED,GAAE,UAAU,4BAA0B,SAASA,IAAE;AAAC,QAAI,IAAE,KAAK;AAA6B,WAAO,MAAI,EAAE,OAAK,KAAK,+BAA6BA,MAAG,KAAK,+BAA6B,GAAG,GAAEA,IAAE,EAAE,GAAE,QAAEA,EAAC,IAAG,QAAE,CAAC,GAAE,KAAK;AAAA,EAA4B,GAAEA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,cAAc,QAAQ,GAAE,KAAK,cAAc,QAAQ,GAAE,QAAE,CAAC,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,4BAA4B,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,mBAAiB,MAAK,KAAK,uBAAqB,QAAE,KAAK,4BAA4B,GAAE,KAAK,+BAA6B,SAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,IAAG,KAAK,kCAAgC,MAAK,KAAK,qCAAmC,MAAK,KAAK,iCAA+B,MAAK,KAAK,oCAAkC;AAAA,EAAI,GAAEA,GAAE,UAAU,aAAW,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,GAAGA,IAAE,EAAE,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,SAAQ,IAAE,KAAK,cAAc,QAAQ,CAAC,GAAE,IAAE,GAAG,CAAC,GAAE,IAAE,EAAE,OAAM,CAAC,GAAE,GAAG,CAAC,IAAE,EAAE,QAAO,CAAC,GAAE,KAAK,cAAa,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,OAAK,IAAE,EAAE,KAAK,GAAG,UAAQ,QAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,KAAG,CAAC,GAAE,GAAG,GAAE,KAAK,UAAS,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,yBAASD,OAAIA,KAAE,CAAC;AAAG,uBAAQK,KAAEJ,GAAE,MAAKG,KAAEH,GAAE,KAAIK,KAAEL,GAAE,OAAKA,GAAE,OAAMC,KAAED,GAAE,MAAIA,GAAE,QAAOM,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,oBAAIJ,KAAEH,GAAEO,EAAC,GAAEC,KAAEL,GAAE,aAAa,qBAAoBM,MAAGD,GAAE,OAAKH,OAAI,IAAEC,KAAGI,MAAGF,GAAE,OAAKJ,OAAI,IAAEF,KAAGU,KAAEJ,GAAE,SAAO,IAAEF,KAAG,IAAEE,GAAE,UAAQ,IAAEN;AAAG,gBAAAM,GAAE,OAAKC,IAAED,GAAE,OAAKE,IAAEF,GAAE,QAAMI,IAAEJ,GAAE,SAAO,GAAEA,GAAE,OAAKC,KAAEG,IAAEJ,GAAE,OAAKE,KAAE;AAAE,oBAAI,IAAEP,GAAE,aAAa;AAAkB,qBAAG,EAAE,QAAS,SAASH,IAAE;AAAC,sBAAIC,MAAGD,GAAE,IAAEK,OAAI,IAAEC,KAAGC,MAAGP,GAAE,IAAEI,OAAI,IAAEF;AAAG,kBAAAF,GAAE,IAAEC,IAAED,GAAE,IAAEO;AAAA,gBAAC,CAAE;AAAA,cAAC;AAAC,qBAAOP;AAAA,YAAC,EAAE,GAAE,CAAC,GAAE,QAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,qBAAmB,SAASA,IAAE,GAAE;AAAC,WAAO,GAAE,GAAE,GAAG,GAAGA,IAAE,GAAE,EAAC,gCAA+B,GAAE,kCAAiC,GAAE,iCAAgC,GAAE,CAAC,GAAE,GAAE,EAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,qBAAmB,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,gBAAG,IAAE,GAAG,CAAC,GAAE,IAAE,GAAG,GAAE,IAAGA,EAAC,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,SAAQ,IAAE,EAAE,sBAAqB,WAAS,KAAK,aAAW,WAAS,KAAK,aAAW,YAAU,KAAK,UAAU,OAAM,IAAI,MAAM,mDAAiD,WAAW,OAAO,KAAK,SAAS,CAAC;AAAE,mBAAO,IAAE,CAAC,SAAQ,mBAAkB,sBAAqB,UAAU,GAAE,KAAK,sBAAoB,EAAE,KAAK,yBAAyB,GAAE,IAAE,KAAK,cAAc,QAAQ,GAAE,CAAC,GAAE,CAAC,GAAE,KAAK,sCAAsC,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,SAAO,IAAE,EAAE,KAAK,MAAI,QAAE,CAAC,GAAE,QAAE,CAAC,GAAE,CAAC,GAAE,IAAI,MAAI,IAAE,EAAE,WAAU,IAAE,EAAE,oBAAmB,IAAE,EAAE,WAAU,IAAE,EAAE,gBAAe,IAAE,EAAE,kBAAiB,CAAC,GAAE,KAAK,8CAA8C,GAAEA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,QAAE,CAAC,GAAE,QAAE,CAAC,GAAE,CAAC,GAAE,EAAE,EAAC,WAAU,EAAC,GAAE,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,gDAA8C,SAASA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,eAAO,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,mBAAQI,KAAE,CAAC,GAAED,KAAE,GAAE,IAAEJ,IAAEI,KAAE,EAAE,QAAOA,MAAI;AAAC,gBAAIF,KAAE,EAAEE,EAAC,GAAEG,KAAEL,GAAE,GAAE,IAAEA,GAAE,GAAE,IAAED,GAAE,UAASQ,KAAE,KAAK,IAAI,CAAC,IAAEF,KAAE,KAAK,IAAI,CAAC,IAAE,GAAEG,KAAE,KAAK,IAAI,CAAC,IAAEH,KAAE,KAAK,IAAI,CAAC,IAAE,GAAEK,KAAE,EAAE,CAAC,GAAEV,EAAC;AAAE,YAAAU,GAAE,IAAEH,IAAEG,GAAE,IAAEF,IAAEL,GAAE,KAAKO,EAAC;AAAA,UAAC;AAAC,iBAAOP;AAAA,QAAC,EAAE,GAAE,CAAC,GAAE,IAAE,MAAK,KAAK,uBAAqB,IAAE,KAAG,WAAU;AAAC,cAAIJ,KAAE,EAAE,OAAMI,KAAEJ,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAEC,KAAE,SAASF,IAAE;AAAC,gBAAIC,KAAE,GAAG,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;AAAE,YAAAA,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAGD,IAAE,GAAE,CAAC,GAAEC,GAAE,CAAC,EAAE,CAAC,IAAE,GAAGD,IAAE,GAAE,CAAC;AAAE,qBAAQK,KAAEL,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,IAAED,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,IAAED,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,IAAED,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,GAAEG,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,UAAQE,KAAE,GAAEA,KAAEL,GAAE,QAAOK,KAAI,CAAAL,GAAEG,EAAC,EAAEE,EAAC,KAAGD;AAAE,mBAAOJ;AAAA,UAAC,EAAE,CAAC,GAAEM,KAAE,SAAE,GAAGL,IAAE,EAAC,OAAM,GAAE,QAAOG,GAAC,GAAEL,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAES,KAAE,CAAC,GAAEJ,IAAE,GAAE,CAAC;AAAE,iBAAO,QAAE,MAAE,UAAU,QAAE,GAAEI,EAAC,GAAEF,IAAE,YAAW,YAAW,GAAE,CAACP,GAAE,QAAOA,GAAE,KAAK,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAA,QAAC,CAAE,GAAE,QAAE,CAAC,IAAG,CAAC,GAAE,EAAC,WAAU,GAAE,oBAAmB,GAAE,gBAAe,GAAE,kBAAiB,EAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,wCAAsC,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAE,KAAK,qBAAmBA,GAAE,CAAC,IAAE,MAAK,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,UAAE,KAAK;AAAE,oBAAO,IAAE,EAAE,KAAK,EAAE,CAAC,KAAG,MAAG,CAAC,GAAE,IAAI,IAAE,CAAC,GAAE,GAAG,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,GAAG,EAAE,KAAK,GAAE,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,MAAM,GAAE,EAAE,GAAE,IAAE,EAAE,MAAM,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,MAAM,GAAE,EAAE,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,yBAASA,OAAIA,KAAE;AAAI,uBAAQD,KAAE,CAAC,GAAE,IAAE,GAAE,IAAEJ,GAAE,QAAO,KAAI;AAAC,oBAAIE,KAAE,EAAE,CAAC,GAAED,GAAE,CAAC,CAAC;AAAE,gBAAAI,OAAIH,GAAE,QAAMF,GAAE,CAAC,EAAE,QAAOI,GAAE,KAAKF,EAAC;AAAA,cAAC;AAAC,qBAAOE;AAAA,YAAC,EAAE,GAAE,GAAE,IAAE,GAAE,IAAE,KAAK,qBAAmB,SAASJ,IAAEC,IAAEI,IAAE;AAAC,qBAAO,KAAG,WAAU;AAAC,oBAAID,KAAE,QAAEJ,IAAE,CAAC,CAAC,CAAC,GAAE,IAAEI,GAAE,MAAM,CAAC;AAAE,oBAAG,MAAI,GAAE;AAAC,sBAAIF,KAAEE;AAAE,0BAAOH,GAAE,YAAW;AAAA,oBAAC,KAAI;AAAO;AAAA,oBAAM,KAAI;AAAU,sBAAAC,KAAE,QAAEA,EAAC;AAAE;AAAA,oBAAM,KAAI;AAAU,4BAAM,IAAI,MAAM,2CAA2C;AAAA,oBAAE;AAAQ,4BAAM,IAAI,MAAM,6BAA6B,OAAOD,GAAE,YAAW,GAAG,CAAC;AAAA,kBAAC;AAAC,sBAAIM,KAAEF,KAAE,MAAE,eAAeH,IAAE,CAACG,GAAE,QAAOA,GAAE,KAAK,CAAC,IAAEH;AAAE,yBAAO,QAAEK,IAAE,CAAC,CAAC,CAAC;AAAA,gBAAC;AAAC,sBAAM,IAAI,MAAM,yCAAyC,OAAO,CAAC,CAAC;AAAA,cAAC,CAAE;AAAA,YAAC,EAAE,GAAE,EAAE,IAAE,MAAK,CAAC,GAAE,EAAC,WAAU,GAAE,oBAAmB,GAAE,WAAU,GAAE,gBAAe,GAAE,kBAAiB,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEP,GAAE,UAAU,qBAAmB,SAASA,IAAE,GAAE;AAAC,WAAO,GAAG,GAAG,GAAGA,EAAC,GAAE,GAAE,EAAC,kCAAiC,GAAE,gCAA+B,GAAE,iCAAgC,GAAE,CAAC,GAAE,GAAE,EAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,wBAAsB,SAASA,IAAE,GAAE,GAAE,GAAE;AAAC,QAAI,GAAE,GAAE;AAAE,QAAG,QAAM,KAAK,aAAW,KAAK,iBAAgB;AAAC,UAAI,IAAE,GAAG,GAAG,CAAC,GAAE,GAAE,EAAC,gCAA+B,GAAE,kCAAiC,GAAE,iCAAgC,GAAE,CAAC;AAAE,cAAM,KAAK,oCAAkC,KAAK,kCAAgC,IAAI,GAAG,EAAE,IAAG,IAAE,KAAK,gCAAgC,MAAMA,EAAC,GAAE,QAAM,KAAK,uCAAqC,KAAK,qCAAmC,IAAI,GAAG,EAAE,IAAG,IAAE,KAAK,mCAAmC,MAAM,CAAC,GAAE,IAAE,KAAK,gCAAgC,MAAM,CAAC,GAAE,QAAM,KAAK,mCAAiC,KAAK,iCAA+B,IAAI,GAAG,EAAE,IAAG,IAAE,KAAK,+BAA+B,MAAM,GAAE,KAAK,WAAU,GAAE,MAAG,CAAC,GAAE,QAAM,KAAK,sCAAoC,KAAK,oCAAkC,IAAI,GAAG,EAAE,IAAG,IAAE,KAAK,kCAAkC,MAAM,GAAE,KAAK,WAAU,GAAE,MAAG,CAAC,GAAE,QAAM,KAAK,wCAAsC,KAAK,sCAAoC,IAAI,GAAG,EAAE,IAAG,IAAE,KAAK,oCAAoC,MAAM,GAAE,KAAK,SAAS;AAAA,IAAC,MAAM,KAAEA,IAAE,IAAE,GAAE,IAAE;AAAE,WAAM,EAAC,yBAAwB,GAAE,4BAA2B,GAAE,8BAA6B,EAAC;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAIC,KAAE,EAAE,CAAC,GAAE,QAAMD,KAAE,KAAGA,EAAC;AAAE,gBAAG,QAAMC,GAAE,oBAAkBA,GAAE,kBAAgB,GAAG,kBAAiB,QAAMA,GAAE,uBAAqBA,GAAE,qBAAmB,GAAG,qBAAoB,QAAMA,GAAE,uBAAqBA,GAAE,qBAAmB,GAAG,qBAAoB,QAAMA,GAAE,cAAYA,GAAE,YAAU,GAAG,YAAW,QAAMA,GAAE,qBAAmBA,GAAE,mBAAiB,GAAG,mBAAkB,QAAMA,GAAE,iBAAiB,SAAOA,GAAE,WAAU;AAAA,cAAC,KAAI;AAAO,gBAAAA,GAAE,mBAAiB;AAAsE;AAAA,cAAM,KAAI;AAAQ,gBAAAA,GAAE,mBAAiB;AAAuE;AAAA,cAAM,KAAI;AAAA,cAAO;AAAQ,gBAAAA,GAAE,mBAAiB;AAAA,YAAqE;AAAC,mBAAOA;AAAA,UAAC,EAAED,EAAC,GAAE,IAAE,YAAU,OAAO,EAAE,oBAAkB,EAAE,iBAAiB,QAAQ,mBAAmB,IAAE,IAAG,IAAE,YAAU,OAAO,EAAE,oBAAkB,EAAE,iBAAiB,QAAQ,mBAAmB,IAAE,IAAG,CAAC,GAAE,QAAQ,IAAI,CAAC,eAAE,EAAE,kBAAiB,EAAC,WAAU,EAAC,CAAC,GAAE,eAAE,EAAE,kBAAiB,EAAC,WAAU,EAAC,CAAC,CAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,CAAC,GAAE,IAAI,GAAG,GAAE,GAAE,EAAE,iBAAgB,EAAE,oBAAmB,EAAE,oBAAmB,EAAE,SAAS,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI;AAAJ,IAAO;AAAP,IAAU,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,KAAC,SAASA,IAAE;AAAC,UAAGA,GAAE,YAAU,EAAE,OAAM,IAAI,MAAM,oDAAkD,eAAe,OAAOA,GAAE,SAAS,CAAC;AAAE,UAAGA,GAAE,UAAQ,EAAE,OAAM,IAAI,MAAM,+CAA6C,eAAe,OAAOA,GAAE,MAAM,CAAC;AAAE,UAAG,WAASA,GAAE,uBAAsB;AAAC,YAAGA,GAAE,sBAAsB,8BAA4B,KAAGA,GAAE,sBAAsB,8BAA4B,EAAE,OAAM,IAAI,MAAM,2FAAyF,GAAG,OAAOA,GAAE,sBAAsB,2BAA2B,CAAC;AAAE,YAAGA,GAAE,sBAAsB,uBAAqB,EAAE,OAAM,IAAI,MAAM,+DAA6D,eAAe,OAAOA,GAAE,sBAAsB,oBAAoB,CAAC;AAAE,iBAAQ,IAAE,GAAE,IAAEA,GAAE,sBAAsB,iBAAgB,IAAE,EAAE,QAAO,KAAI;AAAC,cAAI,IAAE,EAAE,CAAC;AAAE,cAAG,KAAG,EAAE,OAAM,IAAI,MAAM,gEAA8D,mBAAmB,OAAO,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,EAAEA,EAAC,GAAE,KAAK,SAAO,CAAC,GAAE,KAAK,YAAUA,GAAE,WAAU,KAAK,SAAO,MAAIA,GAAE,QAAO,KAAK,gBAAcA,GAAE,eAAc,KAAK,SAAO;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,QAAM,SAASA,IAAE,GAAE;AAAC,SAAK,gBAAgB,CAAC;AAAE,QAAI,IAAE,KAAK,kBAAkBA,EAAC;AAAE,WAAO,KAAK,aAAaA,IAAE,GAAE,CAAC,GAAE,KAAK,aAAa,CAAC,GAAEA;AAAA,EAAC,GAAEA,GAAE,UAAU,YAAU,WAAU;AAAC,WAAO,KAAK,OAAO,MAAM;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,WAAU;AAAC,WAAO,IAAI,IAAI,KAAK,OAAO,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAE,CAAE,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,kBAAgB,SAASA,IAAE;AAAC,QAAI,IAAE;AAAK,SAAK,SAAO,KAAK,OAAO,OAAQ,SAAS,GAAE;AAAC,aAAOA,KAAE,EAAE,iBAAe,EAAE;AAAA,IAAM,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,eAAa,SAASA,IAAE,GAAE,GAAE;AAAC,aAAQ,IAAE,MAAM,KAAK,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,MAAM,KAAK,MAAMA,GAAE,MAAM,EAAE,KAAK,CAAC,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAI,IAAE,EAAE,CAAC;AAAE,UAAG,MAAI,EAAE,QAAO;AAAC,iBAAQ,IAAE,IAAG,IAAE,IAAG,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,cAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,EAAE,CAAC;AAAE,eAAG,KAAK,iBAAe,IAAE,MAAI,IAAE,GAAE,IAAE;AAAA,QAAE;AAAC,YAAG,KAAG,GAAE;AAAC,cAAI,IAAE,KAAK,OAAO,CAAC;AAAE,cAAE,OAAO,OAAO,GAAE,KAAK,YAAYA,GAAE,CAAC,GAAE,GAAE,EAAE,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,KAAG,EAAE;AAAG,cAAI,IAAE,EAAE,QAAQ,CAAC;AAAE,YAAE,OAAO,GAAE,CAAC;AAAA,QAAC,MAAM,GAAE,KAAK,CAAC;AAAA,MAAC,MAAM,GAAE,KAAK,CAAC;AAAA,IAAC;AAAC,aAAQ,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAE,EAAE,CAAC;AAAE,UAAI,IAAE,KAAK,YAAYA,GAAE,CAAC,GAAE,CAAC;AAAE,WAAK,OAAO,KAAK,CAAC,GAAEA,GAAE,CAAC,EAAE,KAAG,EAAE;AAAA,IAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,eAAa,SAASA,IAAE;AAAC,SAAK,OAAO,KAAM,SAASA,IAAE,GAAE;AAAC,aAAO,EAAE,gBAAcA,GAAE;AAAA,IAAa,CAAE,GAAE,KAAK,SAAO,KAAK,OAAO,MAAM,GAAE,KAAK,SAAS;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,SAASA,IAAE,GAAE,GAAE;AAAC,QAAI,IAAE,EAAC,IAAG,KAAG,KAAK,YAAY,GAAE,eAAc,GAAE,WAAU,EAAE,CAAC,GAAEA,GAAE,WAAU,IAAE,EAAE,IAAK,SAASA,IAAE;AAAC,aAAO,EAAE,CAAC,GAAEA,EAAC;AAAA,IAAC,CAAE,EAAC;AAAE,WAAO,WAASA,GAAE,QAAM,EAAE,MAAI,EAAE,CAAC,GAAEA,GAAE,GAAG,IAAG;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,WAAU;AAAC,QAAIA,KAAE,KAAK;AAAO,WAAO,KAAK,UAAQ,GAAEA;AAAA,EAAC,GAAEA,GAAE,UAAU,SAAO,WAAU;AAAC,aAAQA,KAAE,CAAC,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAI,CAAAA,GAAE,CAAC,IAAE,UAAU,CAAC;AAAE,SAAK,SAAO,KAAK,OAAO,OAAQ,SAASC,IAAE;AAAC,aAAM,CAACD,GAAE,SAASC,GAAE,EAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAED,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,SAAO,CAAC;AAAA,EAAC,GAAEA;AAAC,EAAE;AAArrF,IAAurF,KAAG,SAASA,IAAE;AAAC,WAAS,EAAEC,IAAE;AAAC,WAAOD,GAAE,KAAK,MAAKC,EAAC,KAAG;AAAA,EAAI;AAAC,SAAO,EAAE,GAAED,EAAC,GAAE,EAAE,UAAU,oBAAkB,SAASA,IAAE;AAAC,QAAIC,KAAE;AAAK,WAAO,MAAID,GAAE,UAAQ,MAAI,KAAK,OAAO,SAAO,CAAC,CAAC,CAAC,IAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,aAAOC,GAAE,OAAO,IAAK,SAAS,GAAE;AAAC,eAAOA,GAAE,IAAID,IAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,QAAI,IAAE,KAAK,IAAID,GAAE,IAAI,MAAKC,GAAE,IAAI,IAAI,GAAE,IAAE,KAAK,IAAID,GAAE,IAAI,MAAKC,GAAE,IAAI,IAAI,GAAE,IAAE,KAAK,IAAID,GAAE,IAAI,MAAKC,GAAE,IAAI,IAAI,GAAE,IAAE,KAAK,IAAID,GAAE,IAAI,MAAKC,GAAE,IAAI,IAAI;AAAE,QAAG,KAAG,KAAG,KAAG,EAAE,QAAO;AAAE,QAAI,KAAG,IAAE,MAAI,IAAE;AAAG,WAAO,KAAGD,GAAE,IAAI,QAAMA,GAAE,IAAI,SAAOC,GAAE,IAAI,QAAMA,GAAE,IAAI,SAAO;AAAA,EAAE,GAAE;AAAC,EAAE,EAAE;AAAzsG,IAA2sG,KAAG,SAASD,IAAE;AAAC,WAAS,EAAEC,IAAE;AAAC,QAAI,IAAED,GAAE,KAAK,MAAKC,EAAC,KAAG;AAAK,WAAO,EAAE,oBAAkBA,GAAE,sBAAsB,6BAA4B,EAAE,kBAAgBA,GAAE,sBAAsB,iBAAgB,EAAE,iBAAeA,GAAE,sBAAsB,sBAAqB;AAAA,EAAC;AAAC,SAAO,EAAE,GAAED,EAAC,GAAE,EAAE,UAAU,oBAAkB,SAASA,IAAE;AAAC,QAAG,MAAIA,GAAE,UAAQ,MAAI,KAAK,OAAO,OAAO,QAAM,CAAC,CAAC,CAAC;AAAE,aAAQC,KAAE,CAAC,GAAE,IAAE,GAAE,IAAED,IAAE,IAAE,EAAE,QAAO,KAAI;AAAC,eAAQ,IAAE,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,KAAK,QAAO,IAAE,EAAE,QAAO,KAAI;AAAC,YAAI,IAAE,EAAE,CAAC;AAAE,UAAE,KAAK,KAAK,IAAI,GAAE,CAAC,CAAC;AAAA,MAAC;AAAC,MAAAC,GAAE,KAAK,CAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAASD,IAAEC,IAAE;AAAC,aAAQ,IAAE,KAAK,KAAKA,GAAE,SAAS,IAAE,MAAK,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAED,GAAE,UAAU,QAAO,EAAE,GAAE;AAAC,UAAI,IAAEA,GAAE,UAAU,CAAC,GAAE,IAAEC,GAAE,UAAU,CAAC;AAAE,UAAG,EAAE,EAAE,QAAM,KAAK,qBAAmB,EAAE,QAAM,KAAK,oBAAmB;AAAC,aAAG;AAAE,YAAI,IAAE,KAAK,IAAI,EAAE,IAAE,EAAE,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,IAAE,EAAE,GAAE,CAAC,GAAE,IAAE,IAAE,KAAK,gBAAgB,CAAC;AAAE,aAAG,KAAK,IAAI,KAAG,KAAG,IAAE,IAAE,KAAK,IAAI,GAAE,CAAC,EAAE;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,IAAE,KAAK,iBAAe,IAAE,IAAE;AAAA,EAAC,GAAE,EAAE,UAAU,OAAK,SAASD,IAAE;AAAC,QAAIC,KAAE,MAAK,IAAED,GAAE,OAAQ,SAASA,IAAE;AAAC,aAAOA,GAAE,QAAMC,GAAE;AAAA,IAAiB,CAAE,GAAE,IAAE,KAAK,IAAI,MAAM,MAAK,EAAE,CAAC,CAAC,GAAE,EAAE,IAAK,SAASD,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAC,CAAE,GAAE,KAAE,CAAC,GAAE,IAAE,KAAK,IAAI,MAAM,MAAK,EAAE,CAAC,CAAC,GAAE,EAAE,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAC,CAAE,GAAE,KAAE,CAAC,GAAE,IAAE,KAAK,IAAI,MAAM,MAAK,EAAE,CAAC,CAAC,GAAE,EAAE,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAC,CAAE,GAAE,KAAE,CAAC;AAAE,YAAO,IAAE,MAAI,KAAK,IAAI,MAAM,MAAK,EAAE,CAAC,CAAC,GAAE,EAAE,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAC,CAAE,GAAE,KAAE,CAAC,IAAE;AAAA,EAAE,GAAE;AAAC,EAAE,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK,GAAG;AAAU,aAAO,EAAE,OAAQ,SAASA,IAAE,GAAE,GAAE;AAAC,eAAOA,GAAE,CAAC,IAAE,GAAEA;AAAA,MAAC,GAAG,CAAC,CAAC;AAAA,IAAE,KAAK,GAAG;AAAA,IAAQ,KAAK,GAAG;AAAQ,aAAO,EAAE,OAAQ,SAASA,IAAE,GAAE,GAAE;AAAC,eAAOA,GAAE,CAAC,IAAE,GAAEA;AAAA,MAAC,GAAG,CAAC,CAAC;AAAA,IAAE;AAAQ,YAAM,IAAI,MAAM,SAAS,OAAOA,IAAE,oBAAoB,CAAC;AAAA,EAAC;AAAC;AAAC,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAE,WAAS,YAAWA,GAAE,cAAY;AAAa,EAAE,OAAK,KAAG,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAE,UAAQ,WAAUA,GAAE,YAAU,aAAYA,GAAE,UAAQ;AAAS,EAAE,OAAK,KAAG,CAAC,EAAE;AAAE,IAAI,KAAG,OAAO,OAAO,EAAC,WAAU,MAAK,wBAAuB,SAASA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK,GAAG;AAAU,aAAO;AAAA,IAAE,KAAK,GAAG;AAAA,IAAQ,KAAK,GAAG;AAAQ,aAAO;AAAA,IAAE;AAAQ,YAAM,IAAI,MAAM,SAAS,OAAOA,IAAE,oBAAoB,CAAC;AAAA,EAAC;AAAC,GAAE,kBAAiB,SAASA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK,GAAG;AAAU,aAAO;AAAA,IAAE,KAAK,GAAG;AAAA,IAAQ,KAAK,GAAG;AAAQ,aAAO;AAAA,IAAE;AAAQ,YAAM,IAAI,MAAM,SAAS,OAAOA,IAAE,oBAAoB,CAAC;AAAA,EAAC;AAAC,GAAE,wBAAuB,GAAE,CAAC;AAAja,IAAma,KAAG,CAAC,wBAAuB,sBAAqB,qBAAqB;AAAxe,IAA0e,KAAG,EAAC,WAAU,wBAAuB,iBAAgB,KAAE;AAAjiB,IAAmiB,KAAG,CAAC;AAAviB,IAAyiB,KAAG,EAAC,WAAU,IAAG,WAAU,KAAI,MAAK,KAAI,gBAAe,KAAI,iBAAgB,KAAG,eAAc,GAAE,qBAAoB,KAAE;AAA7pB,IAA+pB,KAAG,EAAC,WAAU,IAAG,QAAO,KAAI,eAAc,KAAG,uBAAsB,EAAC,6BAA4B,KAAG,iBAAgB,CAAC,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,OAAK,KAAI,GAAE,sBAAqB,EAAC,EAAC;AAAh4B,IAAk4B,KAAG,EAAC,WAAU,IAAG,QAAO,KAAI,eAAc,MAAI,eAAc,CAAC,EAAC;AAAE,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,WAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,MAAE,CAAC,IAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAE,EAAE,QAAO,EAAE,EAAE,CAAC,CAAC,EAAE,IAAE,EAAE,KAAK;AAAA,EAAC;AAAC,MAAG,SAASA,IAAEC,IAAE;AAAC,YAAOD,GAAEC,GAAE,QAAQ,EAAE,QAAM,OAAID,GAAEC,GAAE,SAAS,EAAE,QAAM,SAAMD,GAAEC,GAAE,aAAa,EAAE,QAAM,OAAID,GAAEC,GAAE,cAAc,EAAE,QAAM;AAAA,EAAG,EAAE,GAAE,CAAC,GAAE;AAAC,QAAI,KAAG,EAAE,SAAS,CAAC,IAAE,EAAE,UAAU,CAAC,KAAG,GAAE,KAAG,EAAE,SAAS,CAAC,IAAE,EAAE,UAAU,CAAC,KAAG,GAAE,IAAE,SAASD,IAAEC,IAAEI,IAAED,IAAEE,IAAE;AAAC,eAAQJ,KAAE,CAAC,iBAAgB,kBAAiB,YAAW,WAAW,GAAEK,KAAE,GAAEJ,KAAE,GAAEK,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,SAACK,KAAE,KAAK,IAAIT,KAAEC,GAAEH,GAAEM,EAAC,CAAC,EAAE,CAAC,CAAC,KAAGD,OAAIA,KAAEM,MAAIF,KAAE,KAAK,IAAIL,KAAED,GAAEH,GAAEM,EAAC,CAAC,EAAE,CAAC,CAAC,KAAGL,OAAIA,KAAEQ;AAAA,MAAE;AAAC,eAAQF,KAAE,GAAEC,KAAE,GAAEE,KAAE,GAAEE,KAAE,OAAO,KAAKT,EAAC,GAAEO,KAAEE,GAAE,QAAOF,MAAI;AAAC,YAAIC,IAAEF,IAAEI,KAAED,GAAEF,EAAC;AAAE,YAAG,EAAEZ,GAAEC,GAAEc,EAAC,CAAC,EAAE,QAAM,KAAI,EAACF,KAAE,KAAK,IAAIT,KAAEC,GAAEU,EAAC,EAAE,CAAC,CAAC,KAAGN,OAAIA,KAAEI,MAAIF,KAAE,KAAK,IAAIL,KAAED,GAAEU,EAAC,EAAE,CAAC,CAAC,KAAGL,OAAIA,KAAEC;AAAA,MAAE;AAAC,aAAM,CAACJ,IAAEJ,IAAEM,IAAEC,EAAC;AAAA,IAAC,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,MAAI,GAAE,MAAI,GAAE,MAAI,GAAE,MAAI,CAAC,GAAE,IAAE,CAAC,KAAG,IAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,EAAE,QAAM,GAAE,GAAE,EAAE,SAAO,CAAC,CAAC,IAAG,IAAE,CAAC;AAAE,QAAG,IAAE,KAAK,IAAI,EAAE,OAAM,EAAE,MAAM,IAAE,EAAE,QAAO,GAAG,QAAMV,IAAE,CAAC;AAAE,QAAI,IAAE,IAAE;AAAE,WAAM,EAAC,MAAK,EAAE,CAAC,IAAE,EAAE,QAAO,MAAK,EAAE,CAAC,IAAE,EAAE,OAAM,OAAM,EAAE,CAAC,IAAE,KAAG,EAAE,QAAO,OAAM,EAAE,CAAC,IAAE,KAAG,EAAE,OAAM,SAAQ,EAAE,CAAC,IAAE,KAAG,EAAE,SAAO,EAAE,CAAC,IAAE,EAAE,QAAO,QAAO,EAAE,CAAC,IAAE,KAAG,EAAE,QAAM,EAAE,CAAC,IAAE,EAAE,MAAK;AAAA,EAAC;AAAC,SAAO,GAAG,QAAMA,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE;AAAE,SAAOA,KAAE,EAAE,QAAM,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,SAAO,EAAE,OAAM,IAAE,GAAE,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAG,EAAE,UAAQ,IAAE,EAAE,QAAM,EAAE,QAAO,IAAE,GAAE,KAAG,EAAE,SAAO,IAAE,EAAE,QAAM,KAAG,EAAE,QAAO,IAAE,KAAG,EAAE,QAAM,EAAE,UAAQ,IAAE,EAAE,QAAM,EAAE,QAAO,IAAE,GAAE,KAAG,EAAE,SAAO,IAAE,EAAE,QAAM,KAAG,EAAE,QAAO,IAAE,MAAI,IAAE,GAAE,IAAE,EAAE,SAAO,EAAE,OAAM,IAAE,GAAE,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAG,EAAE,QAAO,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,IAAE,GAAE,MAAK,IAAE,GAAE,QAAO,GAAE,OAAM,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAI,GAAE,IAAE,QAAMA,KAAE,KAAG,EAAE,CAAC,GAAEA,EAAC;AAAE,MAAG,QAAM,EAAE,UAAU,GAAE,YAAU;AAAA,WAA+B,GAAG,QAAQ,EAAE,SAAS,IAAE,EAAE,OAAM,IAAI,MAAM,wBAAwB,OAAO,EAAE,WAAU,IAAI,IAAE,oBAAoB,OAAO,EAAE,CAAC;AAAE,MAAG,QAAM,EAAE,oBAAkB,EAAE,kBAAgB,OAAI,QAAM,EAAE,iBAAe,EAAE,eAAa,KAAG,EAAE,eAAa,GAAG,OAAM,IAAI,MAAM,4CAA4C;AAAE,MAAG,QAAM,EAAE,0BAAwB,EAAE,wBAAsB,MAAI,KAAG,EAAE,wBAAsB,IAAI,OAAM,IAAI,MAAM,kEAAkE;AAAE,MAAG,0BAAwB,EAAE,aAAW,QAAM,EAAE,mBAAiB,EAAE,iBAAe,OAAI,0BAAwB,EAAE,aAAW,SAAK,EAAE,eAAe,KAAG,QAAM,EAAE,gBAAc,EAAE,cAAY,GAAG,cAAa,EAAE,gBAAc,GAAG,SAAS,SAAM,EAAE,gBAAc,EAAE,gBAAc,SAASA,IAAE;AAAC,QAAIC,KAAE,GAAG,IAAGD,EAAC;AAAE,IAAAC,GAAE,wBAAsB,EAAE,CAAC,GAAE,GAAG,qBAAqB,GAAE,QAAMD,GAAE,0BAAwB,QAAMA,GAAE,sBAAsB,gCAA8BC,GAAE,sBAAsB,8BAA4BD,GAAE,sBAAsB,8BAA6B,QAAMA,GAAE,sBAAsB,oBAAkBC,GAAE,sBAAsB,kBAAgBD,GAAE,sBAAsB,kBAAiB,QAAMA,GAAE,sBAAsB,yBAAuBC,GAAE,sBAAsB,uBAAqBD,GAAE,sBAAsB;AAAuB,WAAOC;AAAA,EAAC,EAAE,EAAE,aAAa,IAAE,EAAE,gBAAc;AAAA,OAAO;AAAC,QAAG,EAAE,gBAAc,GAAG,YAAY,OAAM,IAAI,MAAM,uCAAuC;AAAE,YAAM,EAAE,gBAAc,EAAE,iBAAe,IAAE,EAAE,eAAc,GAAG,IAAG,CAAC,KAAG,EAAE,gBAAc;AAAA,EAAE;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGD,IAAE,GAAE;AAAC,MAAI,IAAE,EAAC,WAAUA,GAAE,WAAU,QAAOA,GAAE,QAAO,eAAcA,GAAE,cAAa;AAAE,SAAO,QAAM,EAAE,cAAY,EAAE,YAAU,EAAE,YAAW,QAAM,EAAE,WAAS,EAAE,SAAO,EAAE,SAAQ,QAAM,EAAE,kBAAgB,EAAE,gBAAc,EAAE,gBAAe;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE;AAAC,SAAK,eAAaA,IAAE,KAAK,uBAAqB,EAAC,QAAO,GAAE,OAAM,EAAC,GAAE,KAAK,sBAAoB,GAAG,GAAG,OAAO,GAAE,2BAAyB,EAAE,aAAW,KAAK,qBAAqB,QAAM,KAAI,KAAK,qBAAqB,SAAO,OAAK,yBAAuB,EAAE,cAAY,KAAK,qBAAqB,QAAM,KAAI,KAAK,qBAAqB,SAAO,MAAK,KAAK,iBAAe,0BAAwB,EAAE,WAAU,KAAK,mBAAiB,KAAK,iBAAe,IAAI,GAAG,EAAE,GAAE,KAAK,uBAAqB,IAAI,GAAG,GAAE,GAAE,KAAK,uBAAqB,IAAI,GAAG,GAAE,GAAE,KAAK,uBAAqB,IAAI,GAAG,GAAE,GAAE,KAAK,uBAAqB,IAAI,GAAG,GAAE,IAAG,KAAK,kBAAgB,EAAE,iBAAgB,EAAE,eAAa,KAAK,eAAa,EAAE,eAAa,KAAK,eAAa,MAAI,EAAE,wBAAsB,KAAK,wBAAsB,EAAE,wBAAsB,KAAK,wBAAsB,KAAI,KAAK,iBAAe,EAAE,gBAAe,KAAK,kBAAgB,KAAK,mBAAiB,EAAE,gBAAc,GAAG,WAAS,KAAK,UAAQ,IAAI,GAAG,EAAE,aAAa,IAAE,EAAE,gBAAc,GAAG,gBAAc,KAAK,UAAQ,IAAI,GAAG,EAAE,aAAa,IAAG,KAAK,oBAAkB,KAAK,oBAAkB,oBAAI;AAAA,EAAK;AAAC,SAAOA,GAAE,UAAU,2BAAyB,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,gBAAG,OAAK,IAAE,KAAK,aAAa,QAAQA,EAAC,GAAG,MAAM,UAAQ,MAAI,EAAE,MAAM,CAAC,KAAG,MAAI,EAAE,MAAM,CAAC,KAAG,OAAK,EAAE,MAAM,CAAC,KAAG,MAAI,EAAE,MAAM,CAAC,EAAE,OAAM,EAAE,QAAQ,GAAE,IAAI,MAAM,wCAAwC,OAAO,EAAE,OAAM,GAAG,CAAC;AAAE,mBAAM,aAAW,WAAE,IAAE,CAAC,GAAE,CAAC,KAAG,IAAE,EAAE,SAAS,GAAE,CAAC,GAAE,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAM,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,iBAAI,EAAE,QAAQ,GAAE,IAAE,EAAC,WAAU,CAAC,GAAE,OAAM,EAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,EAAE,EAAE,GAAE,UAAU,CAAC,IAAE,EAAC,GAAE,EAAE,IAAE,CAAC,GAAE,GAAE,EAAE,IAAE,IAAE,CAAC,GAAE,OAAM,EAAE,IAAE,IAAE,CAAC,EAAC,GAAE,EAAE,UAAU,CAAC,EAAE,QAAM,QAAK,EAAE,GAAE,EAAE,SAAO,EAAE,UAAU,CAAC,EAAE;AAAO,mBAAO,IAAE,MAAI,EAAE,SAAO,IAAG,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,0BAAwB,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,gBAAG,OAAK,IAAE,KAAK,aAAa,QAAQA,EAAC,GAAG,MAAM,UAAQ,MAAI,EAAE,MAAM,CAAC,KAAG,OAAK,EAAE,MAAM,CAAC,EAAE,OAAM,EAAE,QAAQ,GAAE,IAAI,MAAM,wCAAwC,OAAO,EAAE,OAAM,GAAG,CAAC;AAAE,mBAAM,aAAW,WAAE,IAAE,CAAC,GAAE,CAAC,KAAG,IAAE,EAAE,SAAS,GAAE,CAAC,GAAE,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAM,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,iBAAI,EAAE,QAAQ,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,SAAO,IAAG,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,MAAI,EAAE,CAAC,IAAE,EAAC,WAAU,CAAC,EAAC,GAAE,IAAE,KAAG,IAAE,IAAG,EAAE,CAAC,EAAE,MAAI,EAAC,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,IAAE,CAAC,GAAE,MAAK,EAAE,IAAE,CAAC,GAAE,MAAK,EAAE,IAAE,CAAC,GAAE,OAAM,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,GAAE,QAAO,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,EAAC,GAAE,IAAE,KAAG,IAAE,IAAG,EAAE,CAAC,EAAE,QAAM,EAAE,CAAC,GAAE,EAAE,CAAC,EAAE,YAAU,CAAC,GAAE,IAAE,GAAE,IAAE,IAAG,EAAE,EAAE,GAAE,CAAC,EAAE,UAAU,CAAC,IAAE,EAAC,GAAE,EAAE,KAAG,IAAE,IAAE,CAAC,GAAE,GAAE,EAAE,KAAG,IAAE,IAAE,IAAE,CAAC,GAAE,OAAM,EAAE,KAAG,IAAE,IAAE,IAAE,CAAC,EAAC;AAAE,mBAAM,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,WAAS,MAAI,IAAE,KAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,SAASA,IAAE;AAAC,qBAAO,QAAMA,KAAE,KAAG,EAAE,CAAC,GAAEA,EAAC;AAAA,YAAC,EAAE,CAAC,GAAE,QAAMA,MAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,QAAM,IAAE,GAAGA,EAAC,MAAI,IAAE,MAAIA,GAAE,eAAa,KAAG,KAAI,IAAE,GAAGA,EAAC,GAAE,IAAE,GAAG,CAAC,GAAE,IAAE,WAAE,GAAE,CAAC,GAAEA,cAAa,UAAG,EAAE,QAAQ,GAAE,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,KAAK,mBAAmB,GAAE,GAAE,CAAC,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,KAAK,sBAAsB,GAAE,GAAE,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,iBAAI,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,CAAC,EAAE,UAAU,QAAO,EAAE,EAAE,GAAE,CAAC,EAAE,UAAU,CAAC,EAAE,OAAK,EAAE,CAAC,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,KAAG,EAAE,QAAO,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,KAAG,EAAE;AAAM,mBAAM,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,qBAAmB,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,IAAE;AAAK,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,KAAK,eAAa,KAAK,aAAW,GAAG,QAAM,KAAK,YAAW,CAAC,IAAG,IAAE,KAAG,WAAU;AAAC,kBAAIC,KAAE,SAAE,CAAC,CAAC,EAAE,WAAW,MAAK,EAAE,WAAW,MAAK,EAAE,WAAW,MAAK,EAAE,WAAW,IAAI,CAAC,CAAC,GAAEI,KAAE,MAAE,CAAC,CAAC,GAAE,OAAO,GAAED,KAAE,CAAC,EAAE,qBAAqB,QAAO,EAAE,qBAAqB,KAAK;AAAE,qBAAO,KAAE,MAAE,cAAcJ,IAAEC,IAAEI,IAAED,IAAE,YAAW,CAAC,GAAE,OAAO;AAAA,YAAC,CAAE,GAAEJ,GAAE,QAAQ,GAAE,CAAC,GAAE,KAAK,yBAAyB,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAG,IAAE,EAAE,KAAK,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAM,KAAK,aAAa,QAAO,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,iBAAI,IAAE,GAAE,IAAE,EAAE,UAAU,QAAO,EAAE,EAAE,GAAE,UAAU,CAAC,EAAE,IAAE,KAAK,WAAW,OAAK,EAAE,UAAU,CAAC,EAAE,IAAE,KAAK,WAAW,QAAO,EAAE,UAAU,CAAC,EAAE,IAAE,KAAK,WAAW,OAAK,EAAE,UAAU,CAAC,EAAE,IAAE,KAAK,WAAW;AAAM,mBAAO,QAAM,KAAG,KAAK,oBAAkB,EAAE,YAAU,KAAK,eAAe,MAAM,EAAE,WAAU,GAAE,CAAC,IAAG,IAAE,GAAG,KAAK,YAAW,EAAE,WAAU,KAAK,qBAAoB,CAAC,GAAE,KAAK,aAAW,KAAK,iBAAiB,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,wBAAsB,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE;AAAK,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAG,EAAE,QAAM,EAAE,UAAQ,IAAE,KAAK,uBAAsB,IAAE,KAAK,MAAM,KAAK,wBAAsB,EAAE,SAAO,EAAE,KAAK,GAAE,IAAE,MAAE,eAAeA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,GAAE,IAAE,KAAG,KAAK,KAAK,IAAE,EAAE,GAAE,IAAE,IAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,MAAI,IAAE,KAAK,MAAM,KAAK,wBAAsB,EAAE,QAAM,EAAE,MAAM,GAAE,IAAE,KAAK,uBAAsB,IAAE,MAAE,eAAeA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,KAAG,KAAK,KAAK,IAAE,EAAE,GAAE,IAAE,GAAE,IAAE,IAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,IAAG,EAAE,QAAQ,GAAEA,GAAE,QAAQ,GAAE,IAAE,KAAE,GAAE,OAAO,GAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,KAAK,wBAAwB,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,iBAAI,IAAE,EAAE,KAAK,GAAE,EAAE,QAAQ,GAAE,IAAE,EAAE,OAAQ,SAASA,IAAE;AAAC,qBAAOA,GAAE,SAAO,EAAE;AAAA,YAAY,CAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,CAAC,EAAE,UAAU,QAAO,EAAE,EAAE,GAAE,CAAC,EAAE,UAAU,CAAC,EAAE,KAAG,IAAE,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,KAAG,IAAE;AAAE,gBAAG,KAAK,mBAAiB,KAAK,QAAQ,MAAM,GAAE,CAAC,GAAE,KAAK,kBAAiB;AAAC,mBAAI,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,MAAK,kBAAkB,IAAI,EAAE,CAAC,EAAE,EAAE,KAAG,KAAK,kBAAkB,IAAI,EAAE,CAAC,EAAE,IAAG,IAAI,GAAG,EAAE,CAAC,GAAE,EAAE,CAAC,EAAE,YAAU,KAAK,kBAAkB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,WAAU,GAAE,CAAC;AAAE,kBAAE,KAAK,QAAQ,YAAY,GAAE,KAAK,kBAAkB,QAAS,SAASA,IAAEC,IAAE;AAAC,kBAAE,IAAIA,EAAC,KAAG,EAAE,kBAAkB,OAAOA,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAC,mBAAM,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAED,GAAE,UAAU,mBAAiB,SAASA,IAAE;AAAC,QAAGA,IAAE;AAAC,UAAI,IAAE,KAAK,qBAAqB,MAAMA,GAAE,IAAI,GAAE,IAAE,KAAK,qBAAqB,MAAMA,GAAE,IAAI,GAAE,IAAE,KAAK,qBAAqB,MAAMA,GAAE,IAAI,GAAE,IAAE,KAAK,qBAAqB,MAAMA,GAAE,IAAI;AAAE,aAAM,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,QAAO,IAAE,GAAE,OAAM,IAAE,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE;AAAA,EAAI,GAAEA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,aAAa,QAAQ;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,aAAW,MAAK,KAAK,aAAa;AAAA,EAAC,GAAEA,GAAE,UAAU,eAAa,WAAU;AAAC,SAAK,eAAe,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM,GAAE,KAAK,qBAAqB,MAAM;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA,OAAIA,KAAE,KAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,GAAGA,EAAC,GAAE,IAAE,MAAG,EAAE,YAAU,IAAE,YAAU,OAAO,EAAE,YAAU,EAAE,SAAS,QAAQ,mBAAmB,IAAE,IAAG,CAAC,GAAE,eAAE,EAAE,UAAS,EAAC,WAAU,EAAC,CAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,QAAO,2BAAyB,EAAE,YAAU,IAAE,uEAAqE,yBAAuB,EAAE,YAAU,IAAE,qEAAmE,0BAAwB,EAAE,cAAY,IAAE,sEAAqE,CAAC,GAAE,eAAE,GAAE,EAAC,WAAU,EAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,cAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,QAAE,KAAK;AAAE,iBAAM,YAAU,WAAE,KAAG,IAAE,EAAE,IAAI,4CAA2C,CAAC,GAAE,CAAC,GAAE,IAAI,GAAG,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,EAAC,cAAa,eAAc,cAAa,IAAG,YAAW,MAAI,iBAAgB,EAAC,QAAO,KAAI,OAAM,IAAG,EAAC;AAAxG,IAA0G,KAAG,CAAC,eAAc,UAAU;AAAtI,IAAwI,KAAG,EAAC,aAAY,CAAC,GAAE,EAAE,GAAE,UAAS,CAAC,EAAE,EAAC;AAA5K,IAA8K,KAAG,CAAC,GAAE,IAAG,EAAE;AAAzL,IAA2L,KAAG,EAAC,aAAY,CAAC,KAAG,MAAI,CAAC,GAAE,UAAS,CAAC,CAAC,EAAC;AAAlO,IAAoO,KAAG,CAAC,GAAE,GAAE,CAAC;AAA7O,IAA+O,KAAG,EAAC,UAAS,GAAE,gBAAe,MAAE;AAA/Q,IAAiR,KAAG,EAAC,UAAS,GAAE,gBAAe,OAAG,gBAAe,KAAG,WAAU,GAAE;AAAhV,IAAkV,KAAG,CAAC,SAAQ,QAAO,OAAO;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,KAAK,MAAMA,KAAE,CAAC;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE;AAAC,SAAK,gBAAc,IAAI,MAAMA,EAAC,GAAE,KAAK,mBAAiB,IAAG,KAAK,kBAAgB;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,UAAQ,SAASA,IAAE;AAAC,SAAK,cAAc,EAAE,KAAK,gBAAgB,IAAEA,IAAE,KAAK,KAAK,KAAK,gBAAgB;AAAA,EAAC,GAAEA,GAAE,UAAU,UAAQ,WAAU;AAAC,QAAIA,KAAE,KAAK,cAAc,CAAC;AAAE,WAAO,KAAK,SAAS,GAAE,KAAK,kBAAkB,GAAE,KAAK,KAAK,CAAC,GAAE,KAAK,cAAc,KAAK,mBAAiB,CAAC,IAAE,MAAKA;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,WAAM,OAAK,KAAK;AAAA,EAAgB,GAAEA,GAAE,UAAU,OAAK,WAAU;AAAC,WAAO,KAAK,mBAAiB;AAAA,EAAC,GAAEA,GAAE,UAAU,MAAI,WAAU;AAAC,WAAO,KAAK,cAAc,MAAM,GAAE,KAAK,mBAAiB,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,MAAI,WAAU;AAAC,WAAO,KAAK,cAAc,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,WAAKA,KAAE,KAAG,KAAK,KAAK,GAAGA,EAAC,GAAEA,EAAC,IAAG,MAAK,SAASA,IAAE,GAAGA,EAAC,CAAC,GAAEA,KAAE,GAAGA,EAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,WAAK,IAAEA,MAAG,KAAK,oBAAkB;AAAC,UAAI,IAAE,IAAEA;AAAE,UAAG,IAAE,KAAK,oBAAkB,KAAK,KAAK,GAAE,IAAE,CAAC,KAAG,KAAI,CAAC,KAAK,KAAKA,IAAE,CAAC,EAAE;AAAM,WAAK,SAASA,IAAE,CAAC,GAAEA,KAAE;AAAA,IAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,aAAW,SAASA,IAAE;AAAC,WAAO,KAAK,gBAAgB,KAAK,cAAcA,EAAC,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE,GAAE;AAAC,WAAO,KAAK,WAAWA,EAAC,IAAE,KAAK,WAAW,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,WAAS,SAASA,IAAE,GAAE;AAAC,QAAI,IAAE,KAAK,cAAcA,EAAC;AAAE,SAAK,cAAcA,EAAC,IAAE,KAAK,cAAc,CAAC,GAAE,KAAK,cAAc,CAAC,IAAE;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,WAAQ,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,MAAG,IAAE,KAAK,IAAI,IAAE,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,IAAE,IAAE,GAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,EAAE,GAAE;AAAC,aAAQ,IAAE,KAAK,IAAI,IAAE,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,IAAE,IAAE,GAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,KAAG,EAAE,IAAI,GAAE,GAAEA,EAAC,IAAE,GAAE;AAAC,UAAE;AAAG;AAAA,IAAK;AAAC,QAAG,CAAC,EAAE;AAAA,EAAK;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,aAAM,CAAC,GAAE,QAAQ,IAAIA,GAAE,IAAK,SAASA,IAAE;AAAC,eAAOA,GAAE,OAAO;AAAA,MAAC,CAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,GAAE,EAAE,IAAIA,IAAE,GAAE,CAAC,GAAE,GAAE,EAAE,IAAIA,IAAE,GAAE,IAAE,EAAE,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,GAAGA,GAAE,UAASA,GAAE,UAASA,GAAE,IAAG,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,SAAM,EAAC,GAAEA,GAAE,WAAS,IAAE,GAAE,GAAEA,GAAE,WAAS,IAAE,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,SAAOA,GAAE,KAAM,SAASA,IAAE;AAAC,QAAIK,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAEL,GAAE;AAAU,WAAOK,KAAE,GAAE,IAAE,GAAE,IAAE,EAAE,CAAC,EAAE,GAAE,IAAE,EAAE,CAAC,EAAE,IAAG,IAAE,IAAEA,MAAG,KAAG,IAAE,IAAE,KAAG,KAAG;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,EAAE,OAAQ,SAASL,IAAE,GAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,GAAEA;AAAC,GAAG,CAAC,CAAC;AAArD,IAAuD,KAAG,CAAC,CAAC,QAAO,UAAU,GAAE,CAAC,YAAW,UAAU,GAAE,CAAC,QAAO,WAAW,GAAE,CAAC,aAAY,WAAW,GAAE,CAAC,QAAO,eAAe,GAAE,CAAC,iBAAgB,YAAY,GAAE,CAAC,cAAa,YAAY,GAAE,CAAC,iBAAgB,UAAU,GAAE,CAAC,YAAW,WAAW,GAAE,CAAC,aAAY,YAAY,GAAE,CAAC,QAAO,gBAAgB,GAAE,CAAC,kBAAiB,aAAa,GAAE,CAAC,eAAc,aAAa,GAAE,CAAC,kBAAiB,WAAW,GAAE,CAAC,aAAY,YAAY,GAAE,CAAC,cAAa,aAAa,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,MAAI,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,SAAM,CAAC,GAAG,CAAC,GAAE,GAAG,CAAC,CAAC;AAAC,CAAE;AAApiB,IAAsiB,KAAG,GAAG,IAAK,SAASA,IAAE;AAAC,SAAOA,GAAE,CAAC;AAAC,CAAE;AAA1kB,IAA4kB,KAAG,GAAG,IAAK,SAASA,IAAE;AAAC,SAAOA,GAAE,CAAC;AAAC,CAAE;AAAE,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAOA,KAAE,IAAE,IAAEA,KAAE,IAAE,IAAEA;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,GAAE,GAAG,KAAK,MAAMA,GAAE,IAAE,CAAC,GAAE,GAAE,IAAE,CAAC,GAAE,GAAE,GAAG,KAAK,MAAMA,GAAE,IAAE,CAAC,GAAE,GAAE,IAAE,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAM,EAAC,GAAEA,GAAE,IAAE,EAAE,GAAE,GAAEA,GAAE,IAAE,EAAE,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE;AAAG,WAAQ,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC,GAAE,IAAE,GAAG,GAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,QAAID,KAAEC,GAAE,MAAM,CAAC,IAAE;AAAE,WAAM,EAAC,GAAEA,GAAE,IAAIJ,GAAE,GAAEA,GAAE,GAAED,EAAC,GAAE,GAAEK,GAAE,IAAIJ,GAAE,GAAEA,GAAE,GAAEG,KAAEJ,EAAC,EAAC;AAAA,EAAC,EAAEA,IAAE,GAAG,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,QAAI,IAAE,GAAG,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,GAAG,EAAE,GAAE,EAAE,GAAE,GAAE,CAAC;AAAE,QAAE,GAAG,EAAC,GAAE,EAAE,IAAE,GAAE,GAAE,EAAE,IAAE,EAAC,GAAE,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,GAAG,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,IAAI,EAAE,GAAE,EAAE,GAAE,CAAC;AAAE,SAAM,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,MAAK,EAAE,CAAC,GAAE,OAAM,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAM,CAAC,GAAE,IAAE,GAAG,QAAO,IAAE,IAAI,MAAM,CAAC,GAAE,IAAEA,GAAE,MAAK,IAAEA,GAAE,OAAM,IAAE,GAAG,GAAE,GAAE,CAAC;AAAE,IAAE,EAAE,EAAE,IAAE,EAAC,OAAM,GAAE,MAAK,EAAE,EAAE,EAAE,GAAE,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC;AAAE,WAAQ,IAAE,IAAE,GAAE,KAAG,GAAE,EAAE,GAAE;AAAC,QAAI,IAAE,GAAG,CAAC,GAAE,IAAE,GAAG,CAAC;AAAE,MAAE,CAAC,KAAG,CAAC,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAE;AAAC,OAAI,IAAE,GAAE,IAAE,GAAE,EAAE,GAAE;AAAC,QAAE,GAAG,CAAC,GAAE,IAAE,GAAG,CAAC;AAAE,MAAE,CAAC,KAAG,CAAC,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,GAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAE;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,OAAQ,SAASK,IAAE,GAAE,GAAE;AAAC,QAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAM,WAAO,GAAGL,IAAE,GAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,CAAC,MAAIK,MAAG,IAAGA;AAAA,EAAC,GAAG,CAAC,IAAE,EAAE;AAAM;AAAC,SAAS,GAAGL,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,WAAS,MAAI,IAAE,MAAI,WAAS,MAAI,IAAE,KAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAM,CAAC,GAAE,GAAG,CAACA,IAAE,GAAE,GAAE,CAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,qBAAQD,KAAEC,GAAE,OAAMC,KAAEF,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC,GAAED,KAAE,IAAI,GAAGG,KAAEJ,KAAEK,IAAG,SAASP,IAAE;AAAC,qBAAOA,GAAE;AAAA,YAAK,CAAE,GAAEQ,KAAE,GAAEA,KAAEF,IAAE,EAAEE,GAAE,UAAQC,KAAE,GAAEA,KAAEP,IAAE,EAAEO,GAAE,UAAQC,KAAE,GAAEA,KAAEH,IAAE,EAAEG,IAAE;AAAC,kBAAIE,KAAEP,GAAE,IAAIG,IAAEC,IAAEC,EAAC;AAAE,cAAAE,KAAEZ,MAAG,GAAGU,IAAEE,IAAEJ,IAAEC,IAAER,IAAEI,EAAC,KAAGF,GAAE,QAAQ,EAAC,OAAMS,IAAE,MAAK,EAAC,UAASJ,IAAE,UAASC,IAAE,IAAGC,GAAC,EAAC,CAAC;AAAA,YAAC;AAAC,mBAAOP;AAAA,UAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,IAAE,GAAE,EAAE,SAAO,KAAG,CAAC,EAAE,MAAM,IAAG,KAAE,EAAE,QAAQ,GAAE,IAAE,GAAG,EAAE,MAAK,GAAE,CAAC,GAAE,GAAG,GAAE,GAAE,GAAE,EAAE,KAAK,EAAE,MAAI,IAAE,GAAG,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,GAAG,GAAE,GAAE,CAAC,GAAE,EAAE,KAAK,EAAC,WAAU,GAAE,OAAM,EAAC,CAAC;AAAG,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,KAAI;AAAC,WAAQH,IAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAI,GAAE,CAAC,IAAE,UAAU,CAAC;AAAE,UAAO,EAAE,QAAO;AAAA,IAAC,KAAK;AAAE,MAAAA,KAAE;AAAa;AAAA,IAAM,KAAK;AAAE,MAAAA,KAAE,WAAW,OAAO,EAAE,CAAC,GAAE,SAAS;AAAE;AAAA,IAAM;AAAQ,YAAM,MAAM,aAAa;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,SAAK,gBAAc,CAAC,KAAI,GAAG,GAAE,KAAK,OAAK;AAAG,SAAK,gBAAc,CAAC,IAAG,GAAE,CAAC,GAAE,KAAK,cAAY,CAACA,GAAE,CAAC,GAAE,CAAC,GAAE,KAAK,iBAAe,oBAAE,mBAAmB,KAAK,WAAW,GAAE,KAAK,WAAS,oBAAE,gBAAgB,KAAK,gBAAe,KAAK,aAAY,KAAK,aAAa,GAAE,KAAK,YAAU;AAAA,EAAuB;AAAC,SAAOA,GAAE,UAAU,cAAY,WAAU;AAAC,WAAM,aAAa,OAAO,GAAG,OAAO,GAAE,ySAAyS;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAG,QAAE,aAAY,cAAE,QAAO,SAASA,IAAEC,IAAE;AAAC,QAAI,IAAE,QAAE,GAAE,IAAE,IAAI,GAAGA,GAAE,KAAK,GAAE,IAAE,EAAE,iBAAiB,GAAE,CAACD,IAAEC,EAAC,GAAE,SAAS;AAAE,WAAO,OAAE,EAAE,yBAAyB,CAAC;AAAA,EAAC,EAAED,IAAE,CAAC;AAAE,QAAM,IAAI,MAAM,6DAA6D;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE;AAAC,QAAG,KAAK,gBAAc,CAAC,KAAI,GAAG,GAAE,KAAK,OAAK,MAAG,KAAK,yBAAuB,GAAE,MAAIA,GAAE,UAAQA,GAAE,CAAC,MAAI,KAAK,uBAAuB,OAAM,IAAI,MAAM,sDAAsD,OAAO,KAAK,wBAAuB,0BAA0B,EAAE,OAAOA,EAAC,CAAC;AAAE,SAAK,gBAAc,CAAC,IAAG,GAAE,CAAC,GAAE,KAAK,cAAYA;AAAE,QAAI,IAAE,CAACA,GAAE,CAAC,GAAE,CAAC;AAAE,SAAK,iBAAe,oBAAE,mBAAmB,CAAC,GAAE,KAAK,WAAS,oBAAE,gBAAgB,KAAK,gBAAe,GAAE,KAAK,aAAa,GAAE,KAAK,YAAU;AAAA,EAAkB;AAAC,SAAOA,GAAE,UAAU,cAAY,WAAU;AAAC,WAAM,2VAA2V,OAAO,GAAG,OAAO,GAAE,uEAAuE,EAAE,OAAO,KAAK,wBAAuB,uRAAuR;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAG,QAAE,aAAY,cAAE,QAAO,SAASA,IAAEC,IAAE;AAAC,QAAI,IAAE,QAAE,GAAE,IAAE,IAAI,GAAGD,GAAE,KAAK,GAAE,IAAE,EAAE,iBAAiB,GAAE,CAACA,IAAEC,EAAC,GAAE,SAAS;AAAE,WAAO,OAAE,EAAE,yBAAyB,CAAC;AAAA,EAAC,EAAED,IAAE,CAAC;AAAE,QAAM,IAAI,MAAM,uDAAuD;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAI,IAAEA,GAAE,OAAM,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAO,KAAG,WAAU;AAAC,QAAIC,IAAE,GAAE,IAAE,QAAED,IAAE,CAAC,IAAE,GAAE,CAAC,CAAC,GAAE,IAAE,OAAE,GAAE,CAAC,GAAE,IAAE,WAAE,IAAE,GAAE,OAAE,GAAE,OAAO,CAAC,GAAE,CAAC,GAAE,IAAE,YAAGC,KAAE,GAAE,IAAE,GAAE,KAAG,WAAU;AAAC,UAAID,KAAE,IAAEC,IAAE,OAAE,GAAE,OAAO,CAAC;AAAE,aAAO,IAAEA,IAAE,IAAED,IAAE,OAAE,GAAE,OAAO,CAAC,CAAC;AAAA,IAAC,CAAE,IAAG,CAAC;AAAE,WAAO,OAAE,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,KAAG,WAAU;AAAC,QAAI,IAAE,SAASA,IAAEC,IAAE;AAAC,eAAQI,KAAE,CAAC,GAAED,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAI,IAAEJ,GAAE,IAAII,IAAE,CAAC,EAAE,QAAQ,GAAE,IAAEJ,GAAE,IAAII,IAAE,CAAC,EAAE,QAAQ,GAAE,IAAE,GAAG,GAAE,GAAEA,IAAEH,EAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,QAAAI,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,MAAC;AAAC,aAAO,SAAEA,IAAE,CAAC,EAAE,QAAO,CAAC,CAAC;AAAA,IAAC,EAAEL,IAAE,CAAC;AAAE,WAAO,IAAE,KAAE,IAAEA,GAAE,SAAS,GAAE,OAAE,GAAE,OAAO,CAAC,GAAE,SAAS,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,GAAE,EAAE,IAAIA,IAAE,GAAE,CAAC,GAAE,GAAE,EAAE,IAAIA,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,GAAE,IAAE,GAAGA,EAAC,GAAE,CAAC,GAAE,QAAQ,IAAI,CAACA,GAAE,OAAO,GAAE,EAAE,OAAO,GAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,CAAC,IAAG,IAAE,GAAG,GAAE,GAAE,CAAC,GAAG,OAAO,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,MAAM,KAAK,SAASA,IAAEC,IAAE;AAAC,qBAAQI,KAAEJ,GAAE,MAAM,CAAC,GAAEG,KAAE,IAAI,aAAaC,EAAC,GAAEC,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,kBAAIJ,KAAED,GAAE,IAAIK,IAAE,CAAC,GAAEC,KAAEN,GAAE,IAAIK,IAAE,CAAC;AAAE,cAAAF,GAAEE,EAAC,IAAEN,GAAE,IAAIE,IAAEK,IAAED,EAAC;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC,EAAE,GAAE,CAAC,CAAC,GAAE,IAAE,EAAE,IAAK,SAASJ,IAAEC,IAAE;AAAC,mBAAO,KAAGD,IAAE,EAAC,GAAE,EAAE,IAAIC,IAAE,CAAC,GAAE,GAAE,EAAE,IAAIA,IAAE,CAAC,GAAE,OAAMD,IAAE,MAAK,EAAEC,EAAC,EAAC;AAAA,UAAC,CAAE,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,EAAC,WAAU,GAAE,OAAM,IAAE,EAAE,OAAM,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGD,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,aAAO,IAAE,GAAGA,EAAC,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,eAAO,KAAG,WAAU;AAAC,cAAID,KAAE,GAAGJ,IAAEK,EAAC;AAAE,iBAAO,IAAE,KAAE,IAAEL,IAAE,OAAEC,IAAE,OAAO,CAAC,GAAE,SAAS,GAAEG,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,GAAGJ,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,UAAOA,KAAE,KAAG,KAAG;AAAC;AAAC,IAAI,KAAG;AAAP,IAAkF,KAAG;AAA0E,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAO,SAASA,IAAEC,IAAE;AAAC,YAAOD,KAAE,KAAGC,MAAG;AAAA,EAAC,EAAED,IAAE,CAAC,IAAEA,KAAE,KAAK,MAAMA,KAAE,CAAC,IAAE,IAAE;AAAC;AAAC,IAAI,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE;AAAC,SAAK,eAAaA;AAAE,QAAI,IAAE,KAAK,aAAa,OAAO,CAAC,EAAE;AAAM,iBAAE,OAAO,OAAK,EAAE,CAAC,KAAG,OAAK,EAAE,CAAC,GAAG,WAAU;AAAC,aAAM,gBAAgB,OAAO,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,EAAE,CAAC,GAAE,IAAI,IAAE;AAAA,IAA6B,CAAE;AAAE,QAAI,GAAE,GAAE,KAAG,IAAE,EAAE,iBAAgB,IAAE,EAAE,cAAa,EAAC,QAAO,GAAG,EAAE,QAAO,CAAC,GAAE,OAAM,GAAG,EAAE,OAAM,CAAC,EAAC;AAAG,KAAC,SAASA,IAAE;AAAC,mBAAE,OAAO,GAAG,QAAQA,EAAC,KAAG,GAAG,WAAU;AAAC,eAAM,mBAAmB,OAAOA,IAAE,eAAe,IAAE;AAAA,MAA4B,CAAE;AAAA,IAAC,EAAE,EAAE,YAAY,GAAE,SAASA,IAAEC,IAAE;AAAC,mBAAE,OAAO,GAAGD,GAAE,QAAOC,EAAC,GAAG,WAAU;AAAC,eAAM,aAAa,OAAOD,GAAE,QAAO,gCAAgC,IAAE,GAAG,OAAOC,IAAE,GAAG;AAAA,MAAC,CAAE,GAAE,aAAE,OAAO,GAAGD,GAAE,OAAMC,EAAC,GAAG,WAAU;AAAC,eAAM,YAAY,OAAOD,GAAE,OAAM,gCAAgC,IAAE,GAAG,OAAOC,IAAE,GAAG;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,GAAE,EAAE,YAAY,GAAE,KAAK,kBAAgB,GAAE,KAAK,eAAa,EAAE,cAAa,KAAK,eAAa,EAAE;AAAA,EAAY;AAAC,SAAOD,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE;AAAC,WAAO,WAAS,MAAI,IAAE,KAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,eAAM,CAAC,GAAE,KAAK,iBAAiBA,IAAE,GAAE,KAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,mBAAiB,SAASA,IAAE,GAAE,GAAE;AAAC,WAAO,WAAS,MAAI,IAAE,KAAI,WAAS,MAAI,IAAE,QAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,SAASA,IAAE;AAAC,kBAAIC,KAAED;AAAE,kBAAG,QAAMC,GAAE,aAAWA,GAAE,WAAS,IAAGA,GAAE,YAAU,EAAE,OAAM,IAAI,MAAM,oBAAoB,OAAOA,GAAE,UAAS,kBAAkB,CAAC;AAAE,kBAAGA,GAAE,WAAS,GAAE;AAAC,qBAAIA,KAAE,EAAE,EAAE,CAAC,GAAE,EAAE,GAAEA,EAAC,GAAG,iBAAe,KAAGA,GAAE,iBAAe,EAAE,OAAM,IAAI,MAAM,0BAA0B,OAAOA,GAAE,gBAAe,IAAI,IAAE,+BAA+B;AAAE,oBAAGA,GAAE,aAAW,EAAE,OAAM,IAAI,MAAM,qBAAqB,OAAOA,GAAE,WAAU,GAAG,CAAC;AAAA,cAAC;AAAC,qBAAOA;AAAA,YAAC,EAAE,CAAC,GAAE,QAAMD,KAAE,CAAC,GAAE,IAAE,CAAC,CAAC,GAAE,CAAC,CAAC,IAAE,CAAC,CAAC,KAAG,KAAK,WAAS,EAAE,UAAS,IAAE,GAAGA,IAAE,EAAC,kBAAiB,KAAK,iBAAgB,iBAAgB,MAAG,YAAW,YAAW,CAAC,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,SAAQ,IAAE,eAAa,KAAK,eAAa,IAAE,GAAE,EAAE,IAAE,GAAG,GAAE,CAAC,IAAG,CAAC,CAAC,GAAE,IAAE,KAAK,aAAa,QAAQ,CAAC,GAAE,eAAa,KAAK,gBAAc,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,MAAI,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC,IAAG,IAAE,QAAE,CAAC,GAAE,MAAI,KAAK,WAAS,CAAC,GAAE,CAAC,IAAE,IAAE,CAAC,GAAE,GAAG,GAAE,GAAE,KAAK,YAAY,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,GAAG,GAAE,GAAE,KAAK,YAAY,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,IAAE,CAAC,CAAC,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAG,EAAE,OAAM,IAAI,MAAM,yCAAyC;AAAE,mBAAM,CAAC,GAAE,GAAG,GAAE,GAAE,GAAE,GAAE,KAAK,cAAa,KAAK,UAAS,EAAE,gBAAe,EAAE,SAAS,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,gBAAG,GAAE;AAAC,kBAAG,SAAK,EAAE,eAAe,OAAM,IAAI,MAAM,kCAAkC;AAAE,kBAAE,KAAK,cAAc,GAAGA,EAAC,GAAE,KAAK,iBAAgB,CAAC;AAAA,YAAC,MAAM,KAAE,GAAGA,EAAC,GAAE,IAAE,SAASA,IAAEC,IAAEI,IAAED,IAAE;AAAC,kBAAIE,KAAEL,GAAE,QAAO,IAAEA,GAAE,OAAMM,KAAED,MAAGD,GAAE,UAAQ,IAAED,GAAE,MAAIA,GAAE,UAASD,KAAE,KAAGE,GAAE,SAAO,IAAED,GAAE,OAAKA,GAAE,SAAQI,KAAE,CAACJ,GAAE,MAAIC,GAAE,QAAOI,KAAE,CAACL,GAAE,OAAKC,GAAE;AAAM,kBAAG,MAAIF,MAAG,MAAII,MAAG,MAAIC,MAAG,MAAIC,GAAE,QAAOT;AAAE,uBAAQU,KAAE,GAAEE,KAAEZ,IAAEU,KAAEE,GAAE,QAAOF,KAAI,UAAQ,IAAE,GAAE,IAAEE,GAAEF,EAAC,EAAE,WAAU,IAAE,EAAE,QAAO,KAAI;AAAC,oBAAIC,KAAE,EAAE,CAAC;AAAE,gBAAAA,GAAE,KAAGA,GAAE,IAAEF,MAAGN,IAAEQ,GAAE,KAAGA,GAAE,IAAEH,MAAGD;AAAA,cAAC;AAAC,qBAAOP;AAAA,YAAC,EAAE,GAAE,GAAE,KAAK,iBAAgB,CAAC,GAAE,EAAE,mBAAiB,IAAE,SAASA,IAAEC,IAAE;AAAC,uBAAQI,KAAE,GAAED,KAAEJ,IAAEK,KAAED,GAAE,QAAOC,KAAI,UAAQC,KAAE,GAAE,IAAEF,GAAEC,EAAC,EAAE,WAAUC,KAAE,EAAE,QAAOA,MAAI;AAAC,oBAAIC,KAAE,EAAED,EAAC;AAAE,gBAAAC,GAAE,IAAEN,GAAE,QAAM,IAAEM,GAAE;AAAA,cAAC;AAAC,qBAAOP;AAAA,YAAC,EAAE,GAAE,CAAC;AAAG,mBAAO,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,QAAE,CAAC,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE,GAAE;AAAC,QAAI,IAAEA,GAAE,QAAO,IAAEA,GAAE,OAAM,IAAE,KAAG,EAAE,UAAQ,IAAE,EAAE,MAAI,EAAE,UAAS,IAAE,KAAG,EAAE,SAAO,IAAE,EAAE,OAAK,EAAE,SAAQ,IAAE,CAAC,EAAE,MAAI,EAAE;AAAO,WAAM,CAAC,CAAC,EAAE,OAAK,EAAE,OAAM,GAAE,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM;AAAA,EAAC,GAAEA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,aAAa,QAAQ;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA,OAAIA,KAAE,KAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAM,gBAAc,IAAE,SAASA,IAAE;AAAC,gBAAIC,KAAED,MAAG;AAAG,gBAAG,QAAMC,GAAE,iBAAeA,GAAE,eAAa,gBAAe,GAAG,QAAQA,GAAE,YAAY,IAAE,EAAE,OAAM,IAAI,MAAM,wBAAwB,OAAOA,GAAE,cAAa,IAAI,IAAE,oBAAoB,OAAO,EAAE,CAAC;AAAE,gBAAG,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB,EAAC,QAAO,KAAI,OAAM,IAAG,IAAG,QAAMA,GAAE,iBAAeA,GAAE,eAAa,KAAI,GAAGA,GAAE,YAAY,EAAE,QAAQA,GAAE,YAAY,IAAE,EAAE,OAAM,IAAI,MAAM,wBAAwB,OAAOA,GAAE,cAAa,IAAI,IAAE,oBAAoB,OAAO,GAAGA,GAAE,YAAY,GAAE,GAAG,IAAE,oBAAoB,OAAOA,GAAE,cAAa,GAAG,CAAC;AAAE,gBAAG,QAAMA,GAAE,eAAaA,GAAE,aAAW,IAAG,GAAGA,GAAE,YAAY,EAAE,QAAQA,GAAE,UAAU,IAAE,EAAE,OAAM,IAAI,MAAM,sBAAsB,OAAOA,GAAE,YAAW,IAAI,IAAE,oBAAoB,OAAO,GAAGA,GAAE,YAAY,GAAE,GAAG,IAAE,oBAAoB,OAAOA,GAAE,cAAa,GAAG,CAAC;AAAE,gBAAG,QAAMA,GAAE,eAAaA,GAAE,aAAW,IAAG,GAAG,QAAQA,GAAE,UAAU,IAAE,EAAE,OAAM,IAAI,MAAM,sBAAsB,OAAOA,GAAE,YAAW,IAAI,IAAE,oBAAoB,OAAO,IAAG,GAAG,IAAE,oBAAoB,OAAOA,GAAE,cAAa,GAAG,CAAC;AAAE,gBAAG,kBAAgBA,GAAE,gBAAc,OAAKA,GAAE,gBAAc,MAAIA,GAAE,WAAW,OAAM,IAAI,MAAM,yEAAyE;AAAE,mBAAOA;AAAA,UAAC,EAAED,EAAC,GAAG,eAAa,CAAC,GAAE,CAAC,KAAG,IAAE,EAAE,cAAa,IAAE,EAAE,YAAW,IAAE,eAAe,OAAO,GAAE,OAAO,GAAE,IAAE,MAAI,IAAE,KAAG,WAAS,IAAE,KAAG,QAAQ,OAAO,GAAE,GAAG,IAAE,GAAE,CAAC,GAAE,eAAE,EAAE,YAAU,CAAC,CAAC;AAAA,QAAG,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,IAAI,GAAG,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAEC,IAAEI,IAAE;AAAC,gBAAID,KAAE,EAAC,GAAE,OAAM,MAAI,OAAM,KAAG,MAAK,GAAEE,KAAE,eAAe,OAAON,IAAE,OAAO;AAAE,mBAAO,MAAIK,KAAE,KAAG,SAAS,OAAOD,GAAEH,EAAC,GAAE,GAAG,IAAEK,KAAE,KAAG,QAAQ,OAAOD,IAAE,GAAG,EAAE,OAAOD,GAAEH,EAAC,GAAE,GAAG,IAAEK;AAAA,UAAC,EAAE,EAAE,cAAa,EAAE,YAAW,EAAE,UAAU,GAAE,CAAC,GAAE,eAAE,EAAE,YAAU,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,IAAI,GAAG,GAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAI,GAAE,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGN,IAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAOA,IAAE;AAAA,QAAC,KAAK,GAAG;AAAQ,iBAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAA,QAAE,KAAK,GAAG;AAAU,cAAG,IAAE,QAAO,SAAO,IAAE,IAAG;AAAC,gBAAG,WAAS,EAAE,QAAQ,QAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAE,gBAAG,gBAAc,EAAE,QAAQ,QAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAE,gBAAE,EAAE;AAAA,UAAO;AAAC,gBAAM,IAAI,MAAM,oDAAkD,2BAA2B,OAAO,CAAC,CAAC;AAAA,QAAE,KAAK,GAAG;AAAQ,iBAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAA,QAAE;AAAQ,gBAAM,IAAI,MAAM,GAAG,OAAOA,IAAE,iCAAiC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,KAAG,EAAC,gCAA+B,GAAE;AAAzC,IAA2C,KAAG,EAAC,WAAU,EAAC,sBAAqB,wBAAuB,oBAAmB,sBAAqB,qBAAoB,sBAAqB,EAAC;", "names": ["D", "q", "t", "G", "H", "K", "L", "N", "Q", "V", "W", "X", "Y", "Z", "B", "J", "U", "t", "e", "o", "s", "i", "n", "r", "a", "u", "h", "l", "d", "c", "f", "p", "m"]}