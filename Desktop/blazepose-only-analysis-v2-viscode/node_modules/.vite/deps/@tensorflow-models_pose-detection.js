import {
  loadGraphModel
} from "./chunk-K276JTJK.js";
import {
  WebGPUBackend,
  webgpu_util_exports
} from "./chunk-VBRHBFGJ.js";
import "./chunk-F63W4NW4.js";
import {
  Tensor,
  add,
  argMax,
  backend,
  browser_exports,
  cast,
  clipByValue,
  concat,
  dispose,
  div,
  engine,
  env,
  exp,
  expandDims,
  getBackend,
  image,
  minimum,
  mirrorPad,
  mul,
  pad,
  reshape,
  scalar,
  sigmoid,
  slice,
  square,
  squeeze,
  sub,
  tensor1d,
  tensor2d,
  tidy,
  util_exports,
  zeros
} from "./chunk-AFFYF5PH.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4MBMRILA.js";

// node_modules/@mediapipe/pose/pose.js
var require_pose = __commonJS({
  "node_modules/@mediapipe/pose/pose.js"(exports) {
    (function() {
      "use strict";
      var x;
      function aa(a) {
        var b = 0;
        return function() {
          return b < a.length ? { done: false, value: a[b++] } : { done: true };
        };
      }
      var ba = "function" == typeof Object.defineProperties ? Object.defineProperty : function(a, b, c) {
        if (a == Array.prototype || a == Object.prototype) return a;
        a[b] = c.value;
        return a;
      };
      function ca(a) {
        a = ["object" == typeof globalThis && globalThis, a, "object" == typeof window && window, "object" == typeof self && self, "object" == typeof global && global];
        for (var b = 0; b < a.length; ++b) {
          var c = a[b];
          if (c && c.Math == Math) return c;
        }
        throw Error("Cannot find global object");
      }
      var y = ca(this);
      function z(a, b) {
        if (b) a: {
          var c = y;
          a = a.split(".");
          for (var d = 0; d < a.length - 1; d++) {
            var e = a[d];
            if (!(e in c)) break a;
            c = c[e];
          }
          a = a[a.length - 1];
          d = c[a];
          b = b(d);
          b != d && null != b && ba(c, a, { configurable: true, writable: true, value: b });
        }
      }
      z("Symbol", function(a) {
        function b(g) {
          if (this instanceof b) throw new TypeError("Symbol is not a constructor");
          return new c(d + (g || "") + "_" + e++, g);
        }
        function c(g, f) {
          this.h = g;
          ba(this, "description", { configurable: true, writable: true, value: f });
        }
        if (a) return a;
        c.prototype.toString = function() {
          return this.h;
        };
        var d = "jscomp_symbol_" + (1e9 * Math.random() >>> 0) + "_", e = 0;
        return b;
      });
      z("Symbol.iterator", function(a) {
        if (a) return a;
        a = Symbol("Symbol.iterator");
        for (var b = "Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "), c = 0; c < b.length; c++) {
          var d = y[b[c]];
          "function" === typeof d && "function" != typeof d.prototype[a] && ba(d.prototype, a, { configurable: true, writable: true, value: function() {
            return da(aa(this));
          } });
        }
        return a;
      });
      function da(a) {
        a = { next: a };
        a[Symbol.iterator] = function() {
          return this;
        };
        return a;
      }
      function A(a) {
        var b = "undefined" != typeof Symbol && Symbol.iterator && a[Symbol.iterator];
        return b ? b.call(a) : { next: aa(a) };
      }
      function ea(a) {
        if (!(a instanceof Array)) {
          a = A(a);
          for (var b, c = []; !(b = a.next()).done; ) c.push(b.value);
          a = c;
        }
        return a;
      }
      var fa = "function" == typeof Object.assign ? Object.assign : function(a, b) {
        for (var c = 1; c < arguments.length; c++) {
          var d = arguments[c];
          if (d) for (var e in d) Object.prototype.hasOwnProperty.call(d, e) && (a[e] = d[e]);
        }
        return a;
      };
      z("Object.assign", function(a) {
        return a || fa;
      });
      var ha = "function" == typeof Object.create ? Object.create : function(a) {
        function b() {
        }
        b.prototype = a;
        return new b();
      }, ia;
      if ("function" == typeof Object.setPrototypeOf) ia = Object.setPrototypeOf;
      else {
        var ja;
        a: {
          var ka = { a: true }, la = {};
          try {
            la.__proto__ = ka;
            ja = la.a;
            break a;
          } catch (a) {
          }
          ja = false;
        }
        ia = ja ? function(a, b) {
          a.__proto__ = b;
          if (a.__proto__ !== b) throw new TypeError(a + " is not extensible");
          return a;
        } : null;
      }
      var ma = ia;
      function na(a, b) {
        a.prototype = ha(b.prototype);
        a.prototype.constructor = a;
        if (ma) ma(a, b);
        else for (var c in b) if ("prototype" != c) if (Object.defineProperties) {
          var d = Object.getOwnPropertyDescriptor(b, c);
          d && Object.defineProperty(a, c, d);
        } else a[c] = b[c];
        a.za = b.prototype;
      }
      function oa() {
        this.m = false;
        this.j = null;
        this.i = void 0;
        this.h = 1;
        this.v = this.s = 0;
        this.l = null;
      }
      function pa(a) {
        if (a.m) throw new TypeError("Generator is already running");
        a.m = true;
      }
      oa.prototype.u = function(a) {
        this.i = a;
      };
      function qa(a, b) {
        a.l = { ma: b, na: true };
        a.h = a.s || a.v;
      }
      oa.prototype.return = function(a) {
        this.l = { return: a };
        this.h = this.v;
      };
      function D2(a, b, c) {
        a.h = c;
        return { value: b };
      }
      function ra(a) {
        this.h = new oa();
        this.i = a;
      }
      function sa(a, b) {
        pa(a.h);
        var c = a.h.j;
        if (c) return ta(a, "return" in c ? c["return"] : function(d) {
          return { value: d, done: true };
        }, b, a.h.return);
        a.h.return(b);
        return ua(a);
      }
      function ta(a, b, c, d) {
        try {
          var e = b.call(a.h.j, c);
          if (!(e instanceof Object)) throw new TypeError("Iterator result " + e + " is not an object");
          if (!e.done) return a.h.m = false, e;
          var g = e.value;
        } catch (f) {
          return a.h.j = null, qa(a.h, f), ua(a);
        }
        a.h.j = null;
        d.call(a.h, g);
        return ua(a);
      }
      function ua(a) {
        for (; a.h.h; ) try {
          var b = a.i(a.h);
          if (b) return a.h.m = false, { value: b.value, done: false };
        } catch (c) {
          a.h.i = void 0, qa(a.h, c);
        }
        a.h.m = false;
        if (a.h.l) {
          b = a.h.l;
          a.h.l = null;
          if (b.na) throw b.ma;
          return { value: b.return, done: true };
        }
        return { value: void 0, done: true };
      }
      function va(a) {
        this.next = function(b) {
          pa(a.h);
          a.h.j ? b = ta(a, a.h.j.next, b, a.h.u) : (a.h.u(b), b = ua(a));
          return b;
        };
        this.throw = function(b) {
          pa(a.h);
          a.h.j ? b = ta(a, a.h.j["throw"], b, a.h.u) : (qa(a.h, b), b = ua(a));
          return b;
        };
        this.return = function(b) {
          return sa(a, b);
        };
        this[Symbol.iterator] = function() {
          return this;
        };
      }
      function wa(a) {
        function b(d) {
          return a.next(d);
        }
        function c(d) {
          return a.throw(d);
        }
        return new Promise(function(d, e) {
          function g(f) {
            f.done ? d(f.value) : Promise.resolve(f.value).then(b, c).then(g, e);
          }
          g(a.next());
        });
      }
      function E(a) {
        return wa(new va(new ra(a)));
      }
      z("Promise", function(a) {
        function b(f) {
          this.i = 0;
          this.j = void 0;
          this.h = [];
          this.u = false;
          var h = this.l();
          try {
            f(h.resolve, h.reject);
          } catch (k) {
            h.reject(k);
          }
        }
        function c() {
          this.h = null;
        }
        function d(f) {
          return f instanceof b ? f : new b(function(h) {
            h(f);
          });
        }
        if (a) return a;
        c.prototype.i = function(f) {
          if (null == this.h) {
            this.h = [];
            var h = this;
            this.j(function() {
              h.m();
            });
          }
          this.h.push(f);
        };
        var e = y.setTimeout;
        c.prototype.j = function(f) {
          e(f, 0);
        };
        c.prototype.m = function() {
          for (; this.h && this.h.length; ) {
            var f = this.h;
            this.h = [];
            for (var h = 0; h < f.length; ++h) {
              var k = f[h];
              f[h] = null;
              try {
                k();
              } catch (l) {
                this.l(l);
              }
            }
          }
          this.h = null;
        };
        c.prototype.l = function(f) {
          this.j(function() {
            throw f;
          });
        };
        b.prototype.l = function() {
          function f(l) {
            return function(m) {
              k || (k = true, l.call(h, m));
            };
          }
          var h = this, k = false;
          return { resolve: f(this.I), reject: f(this.m) };
        };
        b.prototype.I = function(f) {
          if (f === this) this.m(new TypeError("A Promise cannot resolve to itself"));
          else if (f instanceof b) this.L(f);
          else {
            a: switch (typeof f) {
              case "object":
                var h = null != f;
                break a;
              case "function":
                h = true;
                break a;
              default:
                h = false;
            }
            h ? this.F(f) : this.s(f);
          }
        };
        b.prototype.F = function(f) {
          var h = void 0;
          try {
            h = f.then;
          } catch (k) {
            this.m(k);
            return;
          }
          "function" == typeof h ? this.M(h, f) : this.s(f);
        };
        b.prototype.m = function(f) {
          this.v(2, f);
        };
        b.prototype.s = function(f) {
          this.v(1, f);
        };
        b.prototype.v = function(f, h) {
          if (0 != this.i) throw Error("Cannot settle(" + f + ", " + h + "): Promise already settled in state" + this.i);
          this.i = f;
          this.j = h;
          2 === this.i && this.K();
          this.H();
        };
        b.prototype.K = function() {
          var f = this;
          e(function() {
            if (f.D()) {
              var h = y.console;
              "undefined" !== typeof h && h.error(f.j);
            }
          }, 1);
        };
        b.prototype.D = function() {
          if (this.u) return false;
          var f = y.CustomEvent, h = y.Event, k = y.dispatchEvent;
          if ("undefined" === typeof k) return true;
          "function" === typeof f ? f = new f("unhandledrejection", { cancelable: true }) : "function" === typeof h ? f = new h("unhandledrejection", { cancelable: true }) : (f = y.document.createEvent("CustomEvent"), f.initCustomEvent("unhandledrejection", false, true, f));
          f.promise = this;
          f.reason = this.j;
          return k(f);
        };
        b.prototype.H = function() {
          if (null != this.h) {
            for (var f = 0; f < this.h.length; ++f) g.i(this.h[f]);
            this.h = null;
          }
        };
        var g = new c();
        b.prototype.L = function(f) {
          var h = this.l();
          f.T(h.resolve, h.reject);
        };
        b.prototype.M = function(f, h) {
          var k = this.l();
          try {
            f.call(h, k.resolve, k.reject);
          } catch (l) {
            k.reject(l);
          }
        };
        b.prototype.then = function(f, h) {
          function k(p, n) {
            return "function" == typeof p ? function(q2) {
              try {
                l(p(q2));
              } catch (t2) {
                m(t2);
              }
            } : n;
          }
          var l, m, r = new b(function(p, n) {
            l = p;
            m = n;
          });
          this.T(k(f, l), k(h, m));
          return r;
        };
        b.prototype.catch = function(f) {
          return this.then(void 0, f);
        };
        b.prototype.T = function(f, h) {
          function k() {
            switch (l.i) {
              case 1:
                f(l.j);
                break;
              case 2:
                h(l.j);
                break;
              default:
                throw Error("Unexpected state: " + l.i);
            }
          }
          var l = this;
          null == this.h ? g.i(k) : this.h.push(k);
          this.u = true;
        };
        b.resolve = d;
        b.reject = function(f) {
          return new b(function(h, k) {
            k(f);
          });
        };
        b.race = function(f) {
          return new b(function(h, k) {
            for (var l = A(f), m = l.next(); !m.done; m = l.next()) d(m.value).T(h, k);
          });
        };
        b.all = function(f) {
          var h = A(f), k = h.next();
          return k.done ? d([]) : new b(function(l, m) {
            function r(q2) {
              return function(t2) {
                p[q2] = t2;
                n--;
                0 == n && l(p);
              };
            }
            var p = [], n = 0;
            do
              p.push(void 0), n++, d(k.value).T(r(p.length - 1), m), k = h.next();
            while (!k.done);
          });
        };
        return b;
      });
      function xa(a, b) {
        a instanceof String && (a += "");
        var c = 0, d = false, e = { next: function() {
          if (!d && c < a.length) {
            var g = c++;
            return { value: b(g, a[g]), done: false };
          }
          d = true;
          return { done: true, value: void 0 };
        } };
        e[Symbol.iterator] = function() {
          return e;
        };
        return e;
      }
      z("Array.prototype.keys", function(a) {
        return a ? a : function() {
          return xa(this, function(b) {
            return b;
          });
        };
      });
      z("Array.prototype.fill", function(a) {
        return a ? a : function(b, c, d) {
          var e = this.length || 0;
          0 > c && (c = Math.max(0, e + c));
          if (null == d || d > e) d = e;
          d = Number(d);
          0 > d && (d = Math.max(0, e + d));
          for (c = Number(c || 0); c < d; c++) this[c] = b;
          return this;
        };
      });
      function F(a) {
        return a ? a : Array.prototype.fill;
      }
      z("Int8Array.prototype.fill", F);
      z("Uint8Array.prototype.fill", F);
      z("Uint8ClampedArray.prototype.fill", F);
      z("Int16Array.prototype.fill", F);
      z("Uint16Array.prototype.fill", F);
      z("Int32Array.prototype.fill", F);
      z("Uint32Array.prototype.fill", F);
      z("Float32Array.prototype.fill", F);
      z("Float64Array.prototype.fill", F);
      z("Object.is", function(a) {
        return a ? a : function(b, c) {
          return b === c ? 0 !== b || 1 / b === 1 / c : b !== b && c !== c;
        };
      });
      z("Array.prototype.includes", function(a) {
        return a ? a : function(b, c) {
          var d = this;
          d instanceof String && (d = String(d));
          var e = d.length;
          c = c || 0;
          for (0 > c && (c = Math.max(c + e, 0)); c < e; c++) {
            var g = d[c];
            if (g === b || Object.is(g, b)) return true;
          }
          return false;
        };
      });
      z("String.prototype.includes", function(a) {
        return a ? a : function(b, c) {
          if (null == this) throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");
          if (b instanceof RegExp) throw new TypeError("First argument to String.prototype.includes must not be a regular expression");
          return -1 !== this.indexOf(b, c || 0);
        };
      });
      var ya = this || self;
      function G2(a, b) {
        a = a.split(".");
        var c = ya;
        a[0] in c || "undefined" == typeof c.execScript || c.execScript("var " + a[0]);
        for (var d; a.length && (d = a.shift()); ) a.length || void 0 === b ? c[d] && c[d] !== Object.prototype[d] ? c = c[d] : c = c[d] = {} : c[d] = b;
      }
      ;
      function Aa(a) {
        var b;
        a: {
          if (b = ya.navigator) {
            if (b = b.userAgent) break a;
          }
          b = "";
        }
        return -1 != b.indexOf(a);
      }
      ;
      var Ba = Array.prototype.map ? function(a, b) {
        return Array.prototype.map.call(a, b, void 0);
      } : function(a, b) {
        for (var c = a.length, d = Array(c), e = "string" === typeof a ? a.split("") : a, g = 0; g < c; g++) g in e && (d[g] = b.call(void 0, e[g], g, a));
        return d;
      };
      var Ca = {}, Da = null;
      function Ea(a) {
        var b = a.length, c = 3 * b / 4;
        c % 3 ? c = Math.floor(c) : -1 != "=.".indexOf(a[b - 1]) && (c = -1 != "=.".indexOf(a[b - 2]) ? c - 2 : c - 1);
        var d = new Uint8Array(c), e = 0;
        Fa(a, function(g) {
          d[e++] = g;
        });
        return e !== c ? d.subarray(0, e) : d;
      }
      function Fa(a, b) {
        function c(k) {
          for (; d < a.length; ) {
            var l = a.charAt(d++), m = Da[l];
            if (null != m) return m;
            if (!/^[\s\xa0]*$/.test(l)) throw Error("Unknown base64 encoding at char: " + l);
          }
          return k;
        }
        Ga();
        for (var d = 0; ; ) {
          var e = c(-1), g = c(0), f = c(64), h = c(64);
          if (64 === h && -1 === e) break;
          b(e << 2 | g >> 4);
          64 != f && (b(g << 4 & 240 | f >> 2), 64 != h && b(f << 6 & 192 | h));
        }
      }
      function Ga() {
        if (!Da) {
          Da = {};
          for (var a = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""), b = ["+/=", "+/", "-_=", "-_.", "-_"], c = 0; 5 > c; c++) {
            var d = a.concat(b[c].split(""));
            Ca[c] = d;
            for (var e = 0; e < d.length; e++) {
              var g = d[e];
              void 0 === Da[g] && (Da[g] = e);
            }
          }
        }
      }
      ;
      var Ha = "undefined" !== typeof Uint8Array, Ia = !(Aa("Trident") || Aa("MSIE")) && "function" === typeof ya.btoa;
      function Ja(a) {
        if (!Ia) {
          var b;
          void 0 === b && (b = 0);
          Ga();
          b = Ca[b];
          for (var c = Array(Math.floor(a.length / 3)), d = b[64] || "", e = 0, g = 0; e < a.length - 2; e += 3) {
            var f = a[e], h = a[e + 1], k = a[e + 2], l = b[f >> 2];
            f = b[(f & 3) << 4 | h >> 4];
            h = b[(h & 15) << 2 | k >> 6];
            k = b[k & 63];
            c[g++] = l + f + h + k;
          }
          l = 0;
          k = d;
          switch (a.length - e) {
            case 2:
              l = a[e + 1], k = b[(l & 15) << 2] || d;
            case 1:
              a = a[e], c[g] = b[a >> 2] + b[(a & 3) << 4 | l >> 4] + k + d;
          }
          return c.join("");
        }
        for (b = ""; 10240 < a.length; ) b += String.fromCharCode.apply(null, a.subarray(0, 10240)), a = a.subarray(10240);
        b += String.fromCharCode.apply(
          null,
          a
        );
        return btoa(b);
      }
      var Ka = RegExp("[-_.]", "g");
      function La(a) {
        switch (a) {
          case "-":
            return "+";
          case "_":
            return "/";
          case ".":
            return "=";
          default:
            return "";
        }
      }
      function Ma(a) {
        if (!Ia) return Ea(a);
        Ka.test(a) && (a = a.replace(Ka, La));
        a = atob(a);
        for (var b = new Uint8Array(a.length), c = 0; c < a.length; c++) b[c] = a.charCodeAt(c);
        return b;
      }
      var Na;
      function Oa() {
        return Na || (Na = new Uint8Array(0));
      }
      var Pa = {};
      var Qa = "function" === typeof Uint8Array.prototype.slice, H2 = 0, K2 = 0;
      function Ra(a) {
        var b = 0 > a;
        a = Math.abs(a);
        var c = a >>> 0;
        a = Math.floor((a - c) / 4294967296);
        b && (c = A(Sa(c, a)), b = c.next().value, a = c.next().value, c = b);
        H2 = c >>> 0;
        K2 = a >>> 0;
      }
      var Ta = "function" === typeof BigInt;
      function Sa(a, b) {
        b = ~b;
        a ? a = ~a + 1 : b += 1;
        return [a, b];
      }
      ;
      function Ua(a, b) {
        this.i = a >>> 0;
        this.h = b >>> 0;
      }
      function Va(a) {
        if (!a) return Wa || (Wa = new Ua(0, 0));
        if (!/^-?\d+$/.test(a)) return null;
        if (16 > a.length) Ra(Number(a));
        else if (Ta) a = BigInt(a), H2 = Number(a & BigInt(4294967295)) >>> 0, K2 = Number(a >> BigInt(32) & BigInt(4294967295));
        else {
          var b = +("-" === a[0]);
          K2 = H2 = 0;
          for (var c = a.length, d = b, e = (c - b) % 6 + b; e <= c; d = e, e += 6) d = Number(a.slice(d, e)), K2 *= 1e6, H2 = 1e6 * H2 + d, 4294967296 <= H2 && (K2 += H2 / 4294967296 | 0, H2 %= 4294967296);
          b && (b = A(Sa(H2, K2)), a = b.next().value, b = b.next().value, H2 = a, K2 = b);
        }
        return new Ua(H2, K2);
      }
      var Wa;
      function Xa(a, b) {
        return Error("Invalid wire type: " + a + " (at position " + b + ")");
      }
      function Ya() {
        return Error("Failed to read varint, encoding is invalid.");
      }
      function Za(a, b) {
        return Error("Tried to read past the end of the data " + b + " > " + a);
      }
      ;
      function L2() {
        throw Error("Invalid UTF8");
      }
      function $a(a, b) {
        b = String.fromCharCode.apply(null, b);
        return null == a ? b : a + b;
      }
      var ab = void 0, bb, cb = "undefined" !== typeof TextDecoder, db, eb = "undefined" !== typeof TextEncoder;
      var fb;
      function gb(a) {
        if (a !== Pa) throw Error("illegal external caller");
      }
      function hb(a, b) {
        gb(b);
        this.V = a;
        if (null != a && 0 === a.length) throw Error("ByteString should be constructed with non-empty values");
      }
      function ib() {
        return fb || (fb = new hb(null, Pa));
      }
      function jb(a) {
        gb(Pa);
        var b = a.V;
        b = null == b || Ha && null != b && b instanceof Uint8Array ? b : "string" === typeof b ? Ma(b) : null;
        return null == b ? b : a.V = b;
      }
      ;
      function kb(a) {
        if ("string" === typeof a) return { buffer: Ma(a), C: false };
        if (Array.isArray(a)) return { buffer: new Uint8Array(a), C: false };
        if (a.constructor === Uint8Array) return { buffer: a, C: false };
        if (a.constructor === ArrayBuffer) return { buffer: new Uint8Array(a), C: false };
        if (a.constructor === hb) return { buffer: jb(a) || Oa(), C: true };
        if (a instanceof Uint8Array) return { buffer: new Uint8Array(a.buffer, a.byteOffset, a.byteLength), C: false };
        throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers");
      }
      ;
      function lb(a, b) {
        this.i = null;
        this.m = false;
        this.h = this.j = this.l = 0;
        mb(this, a, b);
      }
      function mb(a, b, c) {
        c = void 0 === c ? {} : c;
        a.S = void 0 === c.S ? false : c.S;
        b && (b = kb(b), a.i = b.buffer, a.m = b.C, a.l = 0, a.j = a.i.length, a.h = a.l);
      }
      lb.prototype.reset = function() {
        this.h = this.l;
      };
      function M(a, b) {
        a.h = b;
        if (b > a.j) throw Za(a.j, b);
      }
      function nb(a) {
        var b = a.i, c = a.h, d = b[c++], e = d & 127;
        if (d & 128 && (d = b[c++], e |= (d & 127) << 7, d & 128 && (d = b[c++], e |= (d & 127) << 14, d & 128 && (d = b[c++], e |= (d & 127) << 21, d & 128 && (d = b[c++], e |= d << 28, d & 128 && b[c++] & 128 && b[c++] & 128 && b[c++] & 128 && b[c++] & 128 && b[c++] & 128))))) throw Ya();
        M(a, c);
        return e;
      }
      function ob(a, b) {
        if (0 > b) throw Error("Tried to read a negative byte length: " + b);
        var c = a.h, d = c + b;
        if (d > a.j) throw Za(b, a.j - c);
        a.h = d;
        return c;
      }
      var pb = [];
      function qb() {
        this.h = [];
      }
      qb.prototype.length = function() {
        return this.h.length;
      };
      qb.prototype.end = function() {
        var a = this.h;
        this.h = [];
        return a;
      };
      function rb(a, b, c) {
        for (; 0 < c || 127 < b; ) a.h.push(b & 127 | 128), b = (b >>> 7 | c << 25) >>> 0, c >>>= 7;
        a.h.push(b);
      }
      function N2(a, b) {
        for (; 127 < b; ) a.h.push(b & 127 | 128), b >>>= 7;
        a.h.push(b);
      }
      ;
      function sb(a, b) {
        if (pb.length) {
          var c = pb.pop();
          mb(c, a, b);
          a = c;
        } else a = new lb(a, b);
        this.h = a;
        this.j = this.h.h;
        this.i = this.l = -1;
        this.setOptions(b);
      }
      sb.prototype.setOptions = function(a) {
        a = void 0 === a ? {} : a;
        this.ca = void 0 === a.ca ? false : a.ca;
      };
      sb.prototype.reset = function() {
        this.h.reset();
        this.j = this.h.h;
        this.i = this.l = -1;
      };
      function tb(a) {
        var b = a.h;
        if (b.h == b.j) return false;
        a.j = a.h.h;
        var c = nb(a.h) >>> 0;
        b = c >>> 3;
        c &= 7;
        if (!(0 <= c && 5 >= c)) throw Xa(c, a.j);
        if (1 > b) throw Error("Invalid field number: " + b + " (at position " + a.j + ")");
        a.l = b;
        a.i = c;
        return true;
      }
      function ub(a) {
        switch (a.i) {
          case 0:
            if (0 != a.i) ub(a);
            else a: {
              a = a.h;
              for (var b = a.h, c = b + 10, d = a.i; b < c; ) if (0 === (d[b++] & 128)) {
                M(a, b);
                break a;
              }
              throw Ya();
            }
            break;
          case 1:
            a = a.h;
            M(a, a.h + 8);
            break;
          case 2:
            2 != a.i ? ub(a) : (b = nb(a.h) >>> 0, a = a.h, M(a, a.h + b));
            break;
          case 5:
            a = a.h;
            M(a, a.h + 4);
            break;
          case 3:
            b = a.l;
            do {
              if (!tb(a)) throw Error("Unmatched start-group tag: stream EOF");
              if (4 == a.i) {
                if (a.l != b) throw Error("Unmatched end-group tag");
                break;
              }
              ub(a);
            } while (1);
            break;
          default:
            throw Xa(a.i, a.j);
        }
      }
      var vb = [];
      function wb() {
        this.j = [];
        this.i = 0;
        this.h = new qb();
      }
      function O(a, b) {
        0 !== b.length && (a.j.push(b), a.i += b.length);
      }
      function xb(a, b) {
        if (b = b.R) {
          O(a, a.h.end());
          for (var c = 0; c < b.length; c++) O(a, jb(b[c]) || Oa());
        }
      }
      ;
      var P = "function" === typeof Symbol && "symbol" === typeof Symbol() ? Symbol() : void 0;
      function Q2(a, b) {
        if (P) return a[P] |= b;
        if (void 0 !== a.A) return a.A |= b;
        Object.defineProperties(a, { A: { value: b, configurable: true, writable: true, enumerable: false } });
        return b;
      }
      function yb(a, b) {
        P ? a[P] && (a[P] &= ~b) : void 0 !== a.A && (a.A &= ~b);
      }
      function R(a) {
        var b;
        P ? b = a[P] : b = a.A;
        return null == b ? 0 : b;
      }
      function S(a, b) {
        P ? a[P] = b : void 0 !== a.A ? a.A = b : Object.defineProperties(a, { A: { value: b, configurable: true, writable: true, enumerable: false } });
      }
      function zb(a) {
        Q2(a, 1);
        return a;
      }
      function Ab(a, b) {
        S(b, (a | 0) & -51);
      }
      function Bb(a, b) {
        S(b, (a | 18) & -41);
      }
      ;
      var Cb = {};
      function Db(a) {
        return null !== a && "object" === typeof a && !Array.isArray(a) && a.constructor === Object;
      }
      var Eb, Fb = [];
      S(Fb, 23);
      Eb = Object.freeze(Fb);
      function Gb(a) {
        if (R(a.o) & 2) throw Error("Cannot mutate an immutable Message");
      }
      function Hb(a) {
        var b = a.length;
        (b = b ? a[b - 1] : void 0) && Db(b) ? b.g = 1 : (b = {}, a.push((b.g = 1, b)));
      }
      ;
      function Ib(a) {
        var b = a.i + a.G;
        return a.B || (a.B = a.o[b] = {});
      }
      function T(a, b) {
        return -1 === b ? null : b >= a.i ? a.B ? a.B[b] : void 0 : a.o[b + a.G];
      }
      function V2(a, b, c, d) {
        Gb(a);
        Jb(a, b, c, d);
      }
      function Jb(a, b, c, d) {
        a.j && (a.j = void 0);
        b >= a.i || d ? Ib(a)[b] = c : (a.o[b + a.G] = c, (a = a.B) && b in a && delete a[b]);
      }
      function Kb(a, b, c, d) {
        var e = T(a, b);
        Array.isArray(e) || (e = Eb);
        var g = R(e);
        g & 1 || zb(e);
        if (d) g & 2 || Q2(e, 2), c & 1 || Object.freeze(e);
        else {
          d = !(c & 2);
          var f = g & 2;
          c & 1 || !f ? d && g & 16 && !f && yb(e, 16) : (e = zb(Array.prototype.slice.call(e)), Jb(a, b, e));
        }
        return e;
      }
      function Lb(a, b) {
        var c = T(a, b);
        var d = null == c ? c : "number" === typeof c || "NaN" === c || "Infinity" === c || "-Infinity" === c ? Number(c) : void 0;
        null != d && d !== c && Jb(a, b, d);
        return d;
      }
      function Mb(a, b, c, d, e) {
        a.h || (a.h = {});
        var g = a.h[c], f = Kb(a, c, 3, e);
        if (!g) {
          var h = f;
          g = [];
          var k = !!(R(a.o) & 16);
          f = !!(R(h) & 2);
          var l = h;
          !e && f && (h = Array.prototype.slice.call(h));
          for (var m = f, r = 0; r < h.length; r++) {
            var p = h[r];
            var n = b, q2 = false;
            q2 = void 0 === q2 ? false : q2;
            p = Array.isArray(p) ? new n(p) : q2 ? new n() : void 0;
            if (void 0 !== p) {
              n = p.o;
              var t2 = q2 = R(n);
              f && (t2 |= 2);
              k && (t2 |= 16);
              t2 != q2 && S(n, t2);
              n = t2;
              m = m || !!(2 & n);
              g.push(p);
            }
          }
          a.h[c] = g;
          k = R(h);
          b = k | 33;
          b = m ? b & -9 : b | 8;
          k != b && (m = h, Object.isFrozen(m) && (m = Array.prototype.slice.call(m)), S(m, b), h = m);
          l !== h && Jb(
            a,
            c,
            h
          );
          (e || d && f) && Q2(g, 2);
          d && Object.freeze(g);
          return g;
        }
        e || (e = Object.isFrozen(g), d && !e ? Object.freeze(g) : !d && e && (g = Array.prototype.slice.call(g), a.h[c] = g));
        return g;
      }
      function Nb(a, b, c) {
        var d = !!(R(a.o) & 2);
        b = Mb(a, b, c, d, d);
        a = Kb(a, c, 3, d);
        if (!(d || R(a) & 8)) {
          for (d = 0; d < b.length; d++) {
            c = b[d];
            if (R(c.o) & 2) {
              var e = Ob(c, false);
              e.j = c;
            } else e = c;
            c !== e && (b[d] = e, a[d] = e.o);
          }
          Q2(a, 8);
        }
        return b;
      }
      function W2(a, b, c) {
        if (null != c && "number" !== typeof c) throw Error("Value of float/double field must be a number|null|undefined, found " + typeof c + ": " + c);
        V2(a, b, c);
      }
      function Pb(a, b, c, d, e) {
        Gb(a);
        var g = Mb(a, c, b, false, false);
        c = null != d ? d : new c();
        a = Kb(a, b, 2, false);
        void 0 != e ? (g.splice(e, 0, c), a.splice(e, 0, c.o)) : (g.push(c), a.push(c.o));
        c.C() && yb(a, 8);
        return c;
      }
      function Qb(a, b) {
        return null == a ? b : a;
      }
      function X2(a, b, c) {
        c = void 0 === c ? 0 : c;
        return Qb(Lb(a, b), c);
      }
      ;
      var Rb;
      function Sb(a) {
        switch (typeof a) {
          case "number":
            return isFinite(a) ? a : String(a);
          case "object":
            if (a) if (Array.isArray(a)) {
              if (0 !== (R(a) & 128)) return a = Array.prototype.slice.call(a), Hb(a), a;
            } else {
              if (Ha && null != a && a instanceof Uint8Array) return Ja(a);
              if (a instanceof hb) {
                var b = a.V;
                return null == b ? "" : "string" === typeof b ? b : a.V = Ja(b);
              }
            }
        }
        return a;
      }
      ;
      function Tb(a, b, c, d) {
        if (null != a) {
          if (Array.isArray(a)) a = Ub(a, b, c, void 0 !== d);
          else if (Db(a)) {
            var e = {}, g;
            for (g in a) e[g] = Tb(a[g], b, c, d);
            a = e;
          } else a = b(a, d);
          return a;
        }
      }
      function Ub(a, b, c, d) {
        var e = R(a);
        d = d ? !!(e & 16) : void 0;
        a = Array.prototype.slice.call(a);
        for (var g = 0; g < a.length; g++) a[g] = Tb(a[g], b, c, d);
        c(e, a);
        return a;
      }
      function Vb(a) {
        return a.ja === Cb ? a.toJSON() : Sb(a);
      }
      function Wb(a, b) {
        a & 128 && Hb(b);
      }
      ;
      function Xb(a, b, c) {
        c = void 0 === c ? Bb : c;
        if (null != a) {
          if (Ha && a instanceof Uint8Array) return a.length ? new hb(new Uint8Array(a), Pa) : ib();
          if (Array.isArray(a)) {
            var d = R(a);
            if (d & 2) return a;
            if (b && !(d & 32) && (d & 16 || 0 === d)) return S(a, d | 2), a;
            a = Ub(a, Xb, d & 4 ? Bb : c, true);
            b = R(a);
            b & 4 && b & 2 && Object.freeze(a);
            return a;
          }
          return a.ja === Cb ? Yb(a) : a;
        }
      }
      function Zb(a, b, c, d, e, g, f) {
        if (a = a.h && a.h[c]) {
          d = R(a);
          d & 2 ? d = a : (g = Ba(a, Yb), Bb(d, g), Object.freeze(g), d = g);
          Gb(b);
          f = null == d ? Eb : zb([]);
          if (null != d) {
            g = !!d.length;
            for (a = 0; a < d.length; a++) {
              var h = d[a];
              g = g && !(R(h.o) & 2);
              f[a] = h.o;
            }
            g = (g ? 8 : 0) | 1;
            a = R(f);
            (a & g) !== g && (Object.isFrozen(f) && (f = Array.prototype.slice.call(f)), S(f, a | g));
            b.h || (b.h = {});
            b.h[c] = d;
          } else b.h && (b.h[c] = void 0);
          Jb(b, c, f, e);
        } else V2(b, c, Xb(d, g, f), e);
      }
      function Yb(a) {
        if (R(a.o) & 2) return a;
        a = Ob(a, true);
        Q2(a.o, 2);
        return a;
      }
      function Ob(a, b) {
        var c = a.o, d = [];
        Q2(d, 16);
        var e = a.constructor.h;
        e && d.push(e);
        e = a.B;
        if (e) {
          d.length = c.length;
          d.fill(void 0, d.length, c.length);
          var g = {};
          d[d.length - 1] = g;
        }
        0 !== (R(c) & 128) && Hb(d);
        b = b || a.C() ? Bb : Ab;
        g = a.constructor;
        Rb = d;
        d = new g(d);
        Rb = void 0;
        a.R && (d.R = a.R.slice());
        g = !!(R(c) & 16);
        for (var f = e ? c.length - 1 : c.length, h = 0; h < f; h++) Zb(a, d, h - a.G, c[h], false, g, b);
        if (e) for (var k in e) Zb(a, d, +k, e[k], true, g, b);
        return d;
      }
      ;
      function Y2(a, b, c) {
        null == a && (a = Rb);
        Rb = void 0;
        var d = this.constructor.i || 0, e = 0 < d, g = this.constructor.h, f = false;
        if (null == a) {
          a = g ? [g] : [];
          var h = 48;
          var k = true;
          e && (d = 0, h |= 128);
          S(a, h);
        } else {
          if (!Array.isArray(a)) throw Error();
          if (g && g !== a[0]) throw Error();
          var l = h = Q2(a, 0);
          if (k = 0 !== (16 & l)) (f = 0 !== (32 & l)) || (l |= 32);
          if (e) if (128 & l) d = 0;
          else {
            if (0 < a.length) {
              var m = a[a.length - 1];
              if (Db(m) && "g" in m) {
                d = 0;
                l |= 128;
                delete m.g;
                var r = true, p;
                for (p in m) {
                  r = false;
                  break;
                }
                r && a.pop();
              }
            }
          }
          else if (128 & l) throw Error();
          h !== l && S(a, l);
        }
        this.G = (g ? 0 : -1) - d;
        this.h = void 0;
        this.o = a;
        a: {
          g = this.o.length;
          d = g - 1;
          if (g && (g = this.o[d], Db(g))) {
            this.B = g;
            this.i = d - this.G;
            break a;
          }
          void 0 !== b && -1 < b ? (this.i = Math.max(b, d + 1 - this.G), this.B = void 0) : this.i = Number.MAX_VALUE;
        }
        if (!e && this.B && "g" in this.B) throw Error('Unexpected "g" flag in sparse object of message that is not a group type.');
        if (c) {
          b = k && !f && true;
          e = this.i;
          var n;
          for (k = 0; k < c.length; k++) f = c[k], f < e ? (f += this.G, (d = a[f]) ? $b(d, b) : a[f] = Eb) : (n || (n = Ib(this)), (d = n[f]) ? $b(d, b) : n[f] = Eb);
        }
      }
      Y2.prototype.toJSON = function() {
        return Ub(this.o, Vb, Wb);
      };
      Y2.prototype.C = function() {
        return !!(R(this.o) & 2);
      };
      function $b(a, b) {
        if (Array.isArray(a)) {
          var c = R(a), d = 1;
          !b || c & 2 || (d |= 16);
          (c & d) !== d && S(a, c | d);
        }
      }
      Y2.prototype.ja = Cb;
      Y2.prototype.toString = function() {
        return this.o.toString();
      };
      function ac(a, b, c) {
        if (c) {
          var d = {}, e;
          for (e in c) {
            var g = c[e], f = g.ra;
            f || (d.J = g.xa || g.oa.W, g.ia ? (d.aa = bc(g.ia), f = /* @__PURE__ */ function(h) {
              return function(k, l, m) {
                return h.J(k, l, m, h.aa);
              };
            }(d)) : g.ka ? (d.Z = cc(g.da.P, g.ka), f = /* @__PURE__ */ function(h) {
              return function(k, l, m) {
                return h.J(k, l, m, h.Z);
              };
            }(d)) : f = d.J, g.ra = f);
            f(b, a, g.da);
            d = { J: d.J, aa: d.aa, Z: d.Z };
          }
        }
        xb(b, a);
      }
      var dc = Symbol();
      function ec(a, b, c) {
        return a[dc] || (a[dc] = function(d, e) {
          return b(d, e, c);
        });
      }
      function fc(a) {
        var b = a[dc];
        if (!b) {
          var c = gc(a);
          b = function(d, e) {
            return hc(d, e, c);
          };
          a[dc] = b;
        }
        return b;
      }
      function ic(a) {
        var b = a.ia;
        if (b) return fc(b);
        if (b = a.wa) return ec(a.da.P, b, a.ka);
      }
      function jc(a) {
        var b = ic(a), c = a.da, d = a.oa.U;
        return b ? function(e, g) {
          return d(e, g, c, b);
        } : function(e, g) {
          return d(e, g, c);
        };
      }
      function kc(a, b) {
        var c = a[b];
        "function" == typeof c && 0 === c.length && (c = c(), a[b] = c);
        return Array.isArray(c) && (lc in c || mc in c || 0 < c.length && "function" == typeof c[0]) ? c : void 0;
      }
      function nc(a, b, c, d, e, g) {
        b.P = a[0];
        var f = 1;
        if (a.length > f && "number" !== typeof a[f]) {
          var h = a[f++];
          c(b, h);
        }
        for (; f < a.length; ) {
          c = a[f++];
          for (var k = f + 1; k < a.length && "number" !== typeof a[k]; ) k++;
          h = a[f++];
          k -= f;
          switch (k) {
            case 0:
              d(b, c, h);
              break;
            case 1:
              (k = kc(a, f)) ? (f++, e(b, c, h, k)) : d(b, c, h, a[f++]);
              break;
            case 2:
              k = f++;
              k = kc(a, k);
              e(b, c, h, k, a[f++]);
              break;
            case 3:
              g(b, c, h, a[f++], a[f++], a[f++]);
              break;
            case 4:
              g(b, c, h, a[f++], a[f++], a[f++], a[f++]);
              break;
            default:
              throw Error("unexpected number of binary field arguments: " + k);
          }
        }
        return b;
      }
      var oc = Symbol();
      function bc(a) {
        var b = a[oc];
        if (!b) {
          var c = pc(a);
          b = function(d, e) {
            return qc(d, e, c);
          };
          a[oc] = b;
        }
        return b;
      }
      function cc(a, b) {
        var c = a[oc];
        c || (c = function(d, e) {
          return ac(d, e, b);
        }, a[oc] = c);
        return c;
      }
      var mc = Symbol();
      function rc(a, b) {
        a.push(b);
      }
      function sc(a, b, c) {
        a.push(b, c.W);
      }
      function tc(a, b, c, d) {
        var e = bc(d), g = pc(d).P, f = c.W;
        a.push(b, function(h, k, l) {
          return f(h, k, l, g, e);
        });
      }
      function uc(a, b, c, d, e, g) {
        var f = cc(d, g), h = c.W;
        a.push(b, function(k, l, m) {
          return h(k, l, m, d, f);
        });
      }
      function pc(a) {
        var b = a[mc];
        if (b) return b;
        b = nc(a, a[mc] = [], rc, sc, tc, uc);
        lc in a && mc in a && (a.length = 0);
        return b;
      }
      var lc = Symbol();
      function vc(a, b) {
        a[0] = b;
      }
      function wc(a, b, c, d) {
        var e = c.U;
        a[b] = d ? function(g, f, h) {
          return e(g, f, h, d);
        } : e;
      }
      function xc(a, b, c, d, e) {
        var g = c.U, f = fc(d), h = gc(d).P;
        a[b] = function(k, l, m) {
          return g(k, l, m, h, f, e);
        };
      }
      function yc(a, b, c, d, e, g, f) {
        var h = c.U, k = ec(d, e, g);
        a[b] = function(l, m, r) {
          return h(l, m, r, d, k, f);
        };
      }
      function gc(a) {
        var b = a[lc];
        if (b) return b;
        b = nc(a, a[lc] = {}, vc, wc, xc, yc);
        lc in a && mc in a && (a.length = 0);
        return b;
      }
      function hc(a, b, c) {
        for (; tb(b) && 4 != b.i; ) {
          var d = b.l, e = c[d];
          if (!e) {
            var g = c[0];
            g && (g = g[d]) && (e = c[d] = jc(g));
          }
          if (!e || !e(b, a, d)) {
            e = b;
            d = a;
            g = e.j;
            ub(e);
            var f = e;
            if (!f.ca) {
              e = f.h.h - g;
              f.h.h = g;
              f = f.h;
              if (0 == e) e = ib();
              else {
                g = ob(f, e);
                if (f.S && f.m) e = f.i.subarray(g, g + e);
                else {
                  f = f.i;
                  var h = g;
                  e = g + e;
                  e = h === e ? Oa() : Qa ? f.slice(h, e) : new Uint8Array(f.subarray(h, e));
                }
                e = 0 == e.length ? ib() : new hb(e, Pa);
              }
              (g = d.R) ? g.push(e) : d.R = [e];
            }
          }
        }
        return a;
      }
      function qc(a, b, c) {
        for (var d = c.length, e = 1 == d % 2, g = e ? 1 : 0; g < d; g += 2) (0, c[g + 1])(b, a, c[g]);
        ac(a, b, e ? c[0] : void 0);
      }
      function zc(a, b) {
        return { U: a, W: b };
      }
      var Z2 = zc(function(a, b, c) {
        if (5 !== a.i) return false;
        a = a.h;
        var d = a.i, e = a.h, g = d[e];
        var f = d[e + 1];
        var h = d[e + 2];
        d = d[e + 3];
        M(a, a.h + 4);
        f = (g << 0 | f << 8 | h << 16 | d << 24) >>> 0;
        a = 2 * (f >> 31) + 1;
        g = f >>> 23 & 255;
        f &= 8388607;
        V2(b, c, 255 == g ? f ? NaN : Infinity * a : 0 == g ? a * Math.pow(2, -149) * f : a * Math.pow(2, g - 150) * (f + Math.pow(2, 23)));
        return true;
      }, function(a, b, c) {
        b = Lb(b, c);
        if (null != b) {
          N2(a.h, 8 * c + 5);
          a = a.h;
          var d = +b;
          0 === d ? 0 < 1 / d ? H2 = K2 = 0 : (K2 = 0, H2 = 2147483648) : isNaN(d) ? (K2 = 0, H2 = 2147483647) : (d = (c = 0 > d ? -2147483648 : 0) ? -d : d, 34028234663852886e22 < d ? (K2 = 0, H2 = (c | 2139095040) >>> 0) : 11754943508222875e-54 > d ? (d = Math.round(d / Math.pow(2, -149)), K2 = 0, H2 = (c | d) >>> 0) : (b = Math.floor(Math.log(d) / Math.LN2), d *= Math.pow(2, -b), d = Math.round(8388608 * d), 16777216 <= d && ++b, K2 = 0, H2 = (c | b + 127 << 23 | d & 8388607) >>> 0));
          c = H2;
          a.h.push(c >>> 0 & 255);
          a.h.push(c >>> 8 & 255);
          a.h.push(c >>> 16 & 255);
          a.h.push(c >>> 24 & 255);
        }
      }), Ac = zc(function(a, b, c) {
        if (0 !== a.i) return false;
        var d = a.h, e = 0, g = a = 0, f = d.i, h = d.h;
        do {
          var k = f[h++];
          e |= (k & 127) << g;
          g += 7;
        } while (32 > g && k & 128);
        32 < g && (a |= (k & 127) >> 4);
        for (g = 3; 32 > g && k & 128; g += 7) k = f[h++], a |= (k & 127) << g;
        M(
          d,
          h
        );
        if (128 > k) {
          d = e >>> 0;
          k = a >>> 0;
          if (a = k & 2147483648) d = ~d + 1 >>> 0, k = ~k >>> 0, 0 == d && (k = k + 1 >>> 0);
          d = 4294967296 * k + (d >>> 0);
        } else throw Ya();
        V2(b, c, a ? -d : d);
        return true;
      }, function(a, b, c) {
        b = T(b, c);
        null != b && ("string" === typeof b && Va(b), null != b && (N2(a.h, 8 * c), "number" === typeof b ? (a = a.h, Ra(b), rb(a, H2, K2)) : (c = Va(b), rb(a.h, c.i, c.h))));
      }), Bc = zc(function(a, b, c) {
        if (0 !== a.i) return false;
        V2(b, c, nb(a.h));
        return true;
      }, function(a, b, c) {
        b = T(b, c);
        if (null != b && null != b) if (N2(a.h, 8 * c), a = a.h, c = b, 0 <= c) N2(a, c);
        else {
          for (b = 0; 9 > b; b++) a.h.push(c & 127 | 128), c >>= 7;
          a.h.push(1);
        }
      }), Cc = zc(function(a, b, c) {
        if (2 !== a.i) return false;
        var d = nb(a.h) >>> 0;
        a = a.h;
        var e = ob(a, d);
        a = a.i;
        if (cb) {
          var g = a, f;
          (f = bb) || (f = bb = new TextDecoder("utf-8", { fatal: true }));
          a = e + d;
          g = 0 === e && a === g.length ? g : g.subarray(e, a);
          try {
            var h = f.decode(g);
          } catch (r) {
            if (void 0 === ab) {
              try {
                f.decode(new Uint8Array([128]));
              } catch (p) {
              }
              try {
                f.decode(new Uint8Array([97])), ab = true;
              } catch (p) {
                ab = false;
              }
            }
            !ab && (bb = void 0);
            throw r;
          }
        } else {
          h = e;
          d = h + d;
          e = [];
          for (var k = null, l, m; h < d; ) l = a[h++], 128 > l ? e.push(l) : 224 > l ? h >= d ? L2() : (m = a[h++], 194 > l || 128 !== (m & 192) ? (h--, L2()) : e.push((l & 31) << 6 | m & 63)) : 240 > l ? h >= d - 1 ? L2() : (m = a[h++], 128 !== (m & 192) || 224 === l && 160 > m || 237 === l && 160 <= m || 128 !== ((g = a[h++]) & 192) ? (h--, L2()) : e.push((l & 15) << 12 | (m & 63) << 6 | g & 63)) : 244 >= l ? h >= d - 2 ? L2() : (m = a[h++], 128 !== (m & 192) || 0 !== (l << 28) + (m - 144) >> 30 || 128 !== ((g = a[h++]) & 192) || 128 !== ((f = a[h++]) & 192) ? (h--, L2()) : (l = (l & 7) << 18 | (m & 63) << 12 | (g & 63) << 6 | f & 63, l -= 65536, e.push((l >> 10 & 1023) + 55296, (l & 1023) + 56320))) : L2(), 8192 <= e.length && (k = $a(k, e), e.length = 0);
          h = $a(k, e);
        }
        V2(b, c, h);
        return true;
      }, function(a, b, c) {
        b = T(b, c);
        if (null != b) {
          var d = false;
          d = void 0 === d ? false : d;
          if (eb) {
            if (d && /(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(b)) throw Error("Found an unpaired surrogate");
            b = (db || (db = new TextEncoder())).encode(b);
          } else {
            for (var e = 0, g = new Uint8Array(3 * b.length), f = 0; f < b.length; f++) {
              var h = b.charCodeAt(f);
              if (128 > h) g[e++] = h;
              else {
                if (2048 > h) g[e++] = h >> 6 | 192;
                else {
                  if (55296 <= h && 57343 >= h) {
                    if (56319 >= h && f < b.length) {
                      var k = b.charCodeAt(++f);
                      if (56320 <= k && 57343 >= k) {
                        h = 1024 * (h - 55296) + k - 56320 + 65536;
                        g[e++] = h >> 18 | 240;
                        g[e++] = h >> 12 & 63 | 128;
                        g[e++] = h >> 6 & 63 | 128;
                        g[e++] = h & 63 | 128;
                        continue;
                      } else f--;
                    }
                    if (d) throw Error("Found an unpaired surrogate");
                    h = 65533;
                  }
                  g[e++] = h >> 12 | 224;
                  g[e++] = h >> 6 & 63 | 128;
                }
                g[e++] = h & 63 | 128;
              }
            }
            b = e === g.length ? g : g.subarray(0, e);
          }
          N2(a.h, 8 * c + 2);
          N2(a.h, b.length);
          O(a, a.h.end());
          O(a, b);
        }
      }), Dc = zc(function(a, b, c, d, e) {
        if (2 !== a.i) return false;
        b = Pb(b, c, d);
        c = a.h.j;
        d = nb(a.h) >>> 0;
        var g = a.h.h + d, f = g - c;
        0 >= f && (a.h.j = g, e(b, a, void 0, void 0, void 0), f = g - a.h.h);
        if (f) throw Error("Message parsing ended unexpectedly. Expected to read " + (d + " bytes, instead read " + (d - f) + " bytes, either the data ended unexpectedly or the message misreported its own length"));
        a.h.h = g;
        a.h.j = c;
        return true;
      }, function(a, b, c, d, e) {
        b = Nb(b, d, c);
        if (null != b) for (d = 0; d < b.length; d++) {
          var g = a;
          N2(g.h, 8 * c + 2);
          var f = g.h.end();
          O(g, f);
          f.push(g.i);
          g = f;
          e(b[d], a);
          f = a;
          var h = g.pop();
          for (h = f.i + f.h.length() - h; 127 < h; ) g.push(h & 127 | 128), h >>>= 7, f.i++;
          g.push(h);
          f.i++;
        }
      });
      function Ec(a) {
        return function(b, c) {
          a: {
            if (vb.length) {
              var d = vb.pop();
              d.setOptions(c);
              mb(d.h, b, c);
              b = d;
            } else b = new sb(b, c);
            try {
              var e = gc(a);
              var g = hc(new e.P(), b, e);
              break a;
            } finally {
              e = b.h, e.i = null, e.m = false, e.l = 0, e.j = 0, e.h = 0, e.S = false, b.l = -1, b.i = -1, 100 > vb.length && vb.push(b);
            }
            g = void 0;
          }
          return g;
        };
      }
      function Fc(a) {
        return function() {
          var b = new wb();
          qc(this, b, pc(a));
          O(b, b.h.end());
          for (var c = new Uint8Array(b.i), d = b.j, e = d.length, g = 0, f = 0; f < e; f++) {
            var h = d[f];
            c.set(h, g);
            g += h.length;
          }
          b.j = [c];
          return c;
        };
      }
      ;
      function Gc(a) {
        Y2.call(this, a);
      }
      na(Gc, Y2);
      var Hc = [Gc, 1, Bc, 2, Z2, 3, Cc, 4, Cc];
      Gc.prototype.l = Fc(Hc);
      function Ic(a) {
        Y2.call(this, a, -1, Jc);
      }
      na(Ic, Y2);
      Ic.prototype.addClassification = function(a, b) {
        Pb(this, 1, Gc, a, b);
        return this;
      };
      var Jc = [1], Kc = Ec([Ic, 1, Dc, Hc]);
      function Lc(a) {
        Y2.call(this, a);
      }
      na(Lc, Y2);
      var Mc = [Lc, 1, Z2, 2, Z2, 3, Z2, 4, Z2, 5, Z2];
      Lc.prototype.l = Fc(Mc);
      function Nc(a) {
        Y2.call(this, a, -1, Oc);
      }
      na(Nc, Y2);
      var Oc = [1], Pc = Ec([Nc, 1, Dc, Mc]);
      function Qc(a) {
        Y2.call(this, a);
      }
      na(Qc, Y2);
      var Rc = [Qc, 1, Z2, 2, Z2, 3, Z2, 4, Z2, 5, Z2, 6, Ac], Sc = Ec(Rc);
      Qc.prototype.l = Fc(Rc);
      function Tc(a, b, c) {
        c = a.createShader(0 === c ? a.VERTEX_SHADER : a.FRAGMENT_SHADER);
        a.shaderSource(c, b);
        a.compileShader(c);
        if (!a.getShaderParameter(c, a.COMPILE_STATUS)) throw Error("Could not compile WebGL shader.\n\n" + a.getShaderInfoLog(c));
        return c;
      }
      ;
      function Uc(a) {
        return Nb(a, Gc, 1).map(function(b) {
          var c = T(b, 1);
          return { index: null == c ? 0 : c, qa: X2(b, 2), label: null != T(b, 3) ? Qb(T(b, 3), "") : void 0, displayName: null != T(b, 4) ? Qb(T(b, 4), "") : void 0 };
        });
      }
      ;
      function Vc(a) {
        return { x: X2(a, 1), y: X2(a, 2), z: X2(a, 3), visibility: null != Lb(a, 4) ? X2(a, 4) : void 0 };
      }
      function Wc(a) {
        return Nb(Pc(a), Lc, 1).map(Vc);
      }
      ;
      function Xc(a, b) {
        this.i = a;
        this.h = b;
        this.m = 0;
      }
      function Yc(a, b, c) {
        Zc(a, b);
        if ("function" === typeof a.h.canvas.transferToImageBitmap) return Promise.resolve(a.h.canvas.transferToImageBitmap());
        if (c) return Promise.resolve(a.h.canvas);
        if ("function" === typeof createImageBitmap) return createImageBitmap(a.h.canvas);
        void 0 === a.j && (a.j = document.createElement("canvas"));
        return new Promise(function(d) {
          a.j.height = a.h.canvas.height;
          a.j.width = a.h.canvas.width;
          a.j.getContext("2d", {}).drawImage(a.h.canvas, 0, 0, a.h.canvas.width, a.h.canvas.height);
          d(a.j);
        });
      }
      function Zc(a, b) {
        var c = a.h;
        if (void 0 === a.s) {
          var d = Tc(c, "\n  attribute vec2 aVertex;\n  attribute vec2 aTex;\n  varying vec2 vTex;\n  void main(void) {\n    gl_Position = vec4(aVertex, 0.0, 1.0);\n    vTex = aTex;\n  }", 0), e = Tc(c, "\n  precision mediump float;\n  varying vec2 vTex;\n  uniform sampler2D sampler0;\n  void main(){\n    gl_FragColor = texture2D(sampler0, vTex);\n  }", 1), g = c.createProgram();
          c.attachShader(g, d);
          c.attachShader(g, e);
          c.linkProgram(g);
          if (!c.getProgramParameter(g, c.LINK_STATUS)) throw Error("Could not compile WebGL program.\n\n" + c.getProgramInfoLog(g));
          d = a.s = g;
          c.useProgram(d);
          e = c.getUniformLocation(d, "sampler0");
          a.l = { O: c.getAttribLocation(d, "aVertex"), N: c.getAttribLocation(d, "aTex"), ya: e };
          a.v = c.createBuffer();
          c.bindBuffer(c.ARRAY_BUFFER, a.v);
          c.enableVertexAttribArray(a.l.O);
          c.vertexAttribPointer(a.l.O, 2, c.FLOAT, false, 0, 0);
          c.bufferData(c.ARRAY_BUFFER, new Float32Array([-1, -1, -1, 1, 1, 1, 1, -1]), c.STATIC_DRAW);
          c.bindBuffer(c.ARRAY_BUFFER, null);
          a.u = c.createBuffer();
          c.bindBuffer(c.ARRAY_BUFFER, a.u);
          c.enableVertexAttribArray(a.l.N);
          c.vertexAttribPointer(
            a.l.N,
            2,
            c.FLOAT,
            false,
            0,
            0
          );
          c.bufferData(c.ARRAY_BUFFER, new Float32Array([0, 1, 0, 0, 1, 0, 1, 1]), c.STATIC_DRAW);
          c.bindBuffer(c.ARRAY_BUFFER, null);
          c.uniform1i(e, 0);
        }
        d = a.l;
        c.useProgram(a.s);
        c.canvas.width = b.width;
        c.canvas.height = b.height;
        c.viewport(0, 0, b.width, b.height);
        c.activeTexture(c.TEXTURE0);
        a.i.bindTexture2d(b.glName);
        c.enableVertexAttribArray(d.O);
        c.bindBuffer(c.ARRAY_BUFFER, a.v);
        c.vertexAttribPointer(d.O, 2, c.FLOAT, false, 0, 0);
        c.enableVertexAttribArray(d.N);
        c.bindBuffer(c.ARRAY_BUFFER, a.u);
        c.vertexAttribPointer(
          d.N,
          2,
          c.FLOAT,
          false,
          0,
          0
        );
        c.bindFramebuffer(c.DRAW_FRAMEBUFFER ? c.DRAW_FRAMEBUFFER : c.FRAMEBUFFER, null);
        c.clearColor(0, 0, 0, 0);
        c.clear(c.COLOR_BUFFER_BIT);
        c.colorMask(true, true, true, true);
        c.drawArrays(c.TRIANGLE_FAN, 0, 4);
        c.disableVertexAttribArray(d.O);
        c.disableVertexAttribArray(d.N);
        c.bindBuffer(c.ARRAY_BUFFER, null);
        a.i.bindTexture2d(0);
      }
      function $c(a) {
        this.h = a;
      }
      ;
      var ad = new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 9, 1, 7, 0, 65, 0, 253, 15, 26, 11]);
      function bd(a, b) {
        return b + a;
      }
      function cd(a, b) {
        window[a] = b;
      }
      function dd(a) {
        var b = document.createElement("script");
        b.setAttribute("src", a);
        b.setAttribute("crossorigin", "anonymous");
        return new Promise(function(c) {
          b.addEventListener("load", function() {
            c();
          }, false);
          b.addEventListener("error", function() {
            c();
          }, false);
          document.body.appendChild(b);
        });
      }
      function ed() {
        return E(function(a) {
          switch (a.h) {
            case 1:
              return a.s = 2, D2(a, WebAssembly.instantiate(ad), 4);
            case 4:
              a.h = 3;
              a.s = 0;
              break;
            case 2:
              return a.s = 0, a.l = null, a.return(false);
            case 3:
              return a.return(true);
          }
        });
      }
      function fd(a) {
        this.h = a;
        this.listeners = {};
        this.l = {};
        this.L = {};
        this.s = {};
        this.v = {};
        this.M = this.u = this.ga = true;
        this.I = Promise.resolve();
        this.fa = "";
        this.D = {};
        this.locateFile = a && a.locateFile || bd;
        if ("object" === typeof window) var b = window.location.pathname.toString().substring(0, window.location.pathname.toString().lastIndexOf("/")) + "/";
        else if ("undefined" !== typeof location) b = location.pathname.toString().substring(0, location.pathname.toString().lastIndexOf("/")) + "/";
        else throw Error("solutions can only be loaded on a web page or in a web worker");
        this.ha = b;
        if (a.options) {
          b = A(Object.keys(a.options));
          for (var c = b.next(); !c.done; c = b.next()) {
            c = c.value;
            var d = a.options[c].default;
            void 0 !== d && (this.l[c] = "function" === typeof d ? d() : d);
          }
        }
      }
      x = fd.prototype;
      x.close = function() {
        this.j && this.j.delete();
        return Promise.resolve();
      };
      function gd(a) {
        var b, c, d, e, g, f, h, k, l, m, r;
        return E(function(p) {
          switch (p.h) {
            case 1:
              if (!a.ga) return p.return();
              b = void 0 === a.h.files ? [] : "function" === typeof a.h.files ? a.h.files(a.l) : a.h.files;
              return D2(p, ed(), 2);
            case 2:
              c = p.i;
              if ("object" === typeof window) return cd("createMediapipeSolutionsWasm", { locateFile: a.locateFile }), cd("createMediapipeSolutionsPackedAssets", { locateFile: a.locateFile }), f = b.filter(function(n) {
                return void 0 !== n.data;
              }), h = b.filter(function(n) {
                return void 0 === n.data;
              }), k = Promise.all(f.map(function(n) {
                var q2 = hd(a, n.url);
                if (void 0 !== n.path) {
                  var t2 = n.path;
                  q2 = q2.then(function(w) {
                    a.overrideFile(t2, w);
                    return Promise.resolve(w);
                  });
                }
                return q2;
              })), l = Promise.all(h.map(function(n) {
                return void 0 === n.simd || n.simd && c || !n.simd && !c ? dd(a.locateFile(n.url, a.ha)) : Promise.resolve();
              })).then(function() {
                var n, q2, t2;
                return E(function(w) {
                  if (1 == w.h) return n = window.createMediapipeSolutionsWasm, q2 = window.createMediapipeSolutionsPackedAssets, t2 = a, D2(w, n(q2), 2);
                  t2.i = w.i;
                  w.h = 0;
                });
              }), m = function() {
                return E(function(n) {
                  a.h.graph && a.h.graph.url ? n = D2(
                    n,
                    hd(a, a.h.graph.url),
                    0
                  ) : (n.h = 0, n = void 0);
                  return n;
                });
              }(), D2(p, Promise.all([l, k, m]), 7);
              if ("function" !== typeof importScripts) throw Error("solutions can only be loaded on a web page or in a web worker");
              d = b.filter(function(n) {
                return void 0 === n.simd || n.simd && c || !n.simd && !c;
              }).map(function(n) {
                return a.locateFile(n.url, a.ha);
              });
              importScripts.apply(null, ea(d));
              e = a;
              return D2(p, createMediapipeSolutionsWasm(Module), 6);
            case 6:
              e.i = p.i;
              a.m = new OffscreenCanvas(1, 1);
              a.i.canvas = a.m;
              g = a.i.GL.createContext(a.m, {
                antialias: false,
                alpha: false,
                va: "undefined" !== typeof WebGL2RenderingContext ? 2 : 1
              });
              a.i.GL.makeContextCurrent(g);
              p.h = 4;
              break;
            case 7:
              a.m = document.createElement("canvas");
              r = a.m.getContext("webgl2", {});
              if (!r && (r = a.m.getContext("webgl", {}), !r)) return alert("Failed to create WebGL canvas context when passing video frame."), p.return();
              a.K = r;
              a.i.canvas = a.m;
              a.i.createContext(a.m, true, true, {});
            case 4:
              a.j = new a.i.SolutionWasm(), a.ga = false, p.h = 0;
          }
        });
      }
      function id(a) {
        var b, c, d, e, g, f, h, k;
        return E(function(l) {
          if (1 == l.h) {
            if (a.h.graph && a.h.graph.url && a.fa === a.h.graph.url) return l.return();
            a.u = true;
            if (!a.h.graph || !a.h.graph.url) {
              l.h = 2;
              return;
            }
            a.fa = a.h.graph.url;
            return D2(l, hd(a, a.h.graph.url), 3);
          }
          2 != l.h && (b = l.i, a.j.loadGraph(b));
          c = A(Object.keys(a.D));
          for (d = c.next(); !d.done; d = c.next()) e = d.value, a.j.overrideFile(e, a.D[e]);
          a.D = {};
          if (a.h.listeners) for (g = A(a.h.listeners), f = g.next(); !f.done; f = g.next()) h = f.value, jd(a, h);
          k = a.l;
          a.l = {};
          a.setOptions(k);
          l.h = 0;
        });
      }
      x.reset = function() {
        var a = this;
        return E(function(b) {
          a.j && (a.j.reset(), a.s = {}, a.v = {});
          b.h = 0;
        });
      };
      x.setOptions = function(a, b) {
        var c = this;
        if (b = b || this.h.options) {
          for (var d = [], e = [], g = {}, f = A(Object.keys(a)), h = f.next(); !h.done; g = { X: g.X, Y: g.Y }, h = f.next()) if (h = h.value, !(h in this.l && this.l[h] === a[h])) {
            this.l[h] = a[h];
            var k = b[h];
            void 0 !== k && (k.onChange && (g.X = k.onChange, g.Y = a[h], d.push(/* @__PURE__ */ function(l) {
              return function() {
                var m;
                return E(function(r) {
                  if (1 == r.h) return D2(r, l.X(l.Y), 2);
                  m = r.i;
                  true === m && (c.u = true);
                  r.h = 0;
                });
              };
            }(g))), k.graphOptionXref && (h = Object.assign(
              {},
              { calculatorName: "", calculatorIndex: 0 },
              k.graphOptionXref,
              { valueNumber: 1 === k.type ? a[h] : 0, valueBoolean: 0 === k.type ? a[h] : false, valueString: 2 === k.type ? a[h] : "" }
            ), e.push(h)));
          }
          if (0 !== d.length || 0 !== e.length) this.u = true, this.H = (void 0 === this.H ? [] : this.H).concat(e), this.F = (void 0 === this.F ? [] : this.F).concat(d);
        }
      };
      function kd(a) {
        var b, c, d, e, g, f, h;
        return E(function(k) {
          switch (k.h) {
            case 1:
              if (!a.u) return k.return();
              if (!a.F) {
                k.h = 2;
                break;
              }
              b = A(a.F);
              c = b.next();
            case 3:
              if (c.done) {
                k.h = 5;
                break;
              }
              d = c.value;
              return D2(k, d(), 4);
            case 4:
              c = b.next();
              k.h = 3;
              break;
            case 5:
              a.F = void 0;
            case 2:
              if (a.H) {
                e = new a.i.GraphOptionChangeRequestList();
                g = A(a.H);
                for (f = g.next(); !f.done; f = g.next()) h = f.value, e.push_back(h);
                a.j.changeOptions(e);
                e.delete();
                a.H = void 0;
              }
              a.u = false;
              k.h = 0;
          }
        });
      }
      x.initialize = function() {
        var a = this;
        return E(function(b) {
          return 1 == b.h ? D2(b, gd(a), 2) : 3 != b.h ? D2(b, id(a), 3) : D2(b, kd(a), 0);
        });
      };
      function hd(a, b) {
        var c, d;
        return E(function(e) {
          if (b in a.L) return e.return(a.L[b]);
          c = a.locateFile(b, "");
          d = fetch(c).then(function(g) {
            return g.arrayBuffer();
          });
          a.L[b] = d;
          return e.return(d);
        });
      }
      x.overrideFile = function(a, b) {
        this.j ? this.j.overrideFile(a, b) : this.D[a] = b;
      };
      x.clearOverriddenFiles = function() {
        this.D = {};
        this.j && this.j.clearOverriddenFiles();
      };
      x.send = function(a, b) {
        var c = this, d, e, g, f, h, k, l, m, r;
        return E(function(p) {
          switch (p.h) {
            case 1:
              if (!c.h.inputs) return p.return();
              d = 1e3 * (void 0 === b || null === b ? performance.now() : b);
              return D2(p, c.I, 2);
            case 2:
              return D2(p, c.initialize(), 3);
            case 3:
              e = new c.i.PacketDataList();
              g = A(Object.keys(a));
              for (f = g.next(); !f.done; f = g.next()) if (h = f.value, k = c.h.inputs[h]) {
                a: {
                  var n = a[h];
                  switch (k.type) {
                    case "video":
                      var q2 = c.s[k.stream];
                      q2 || (q2 = new Xc(c.i, c.K), c.s[k.stream] = q2);
                      0 === q2.m && (q2.m = q2.i.createTexture());
                      if ("undefined" !== typeof HTMLVideoElement && n instanceof HTMLVideoElement) {
                        var t2 = n.videoWidth;
                        var w = n.videoHeight;
                      } else "undefined" !== typeof HTMLImageElement && n instanceof HTMLImageElement ? (t2 = n.naturalWidth, w = n.naturalHeight) : (t2 = n.width, w = n.height);
                      w = { glName: q2.m, width: t2, height: w };
                      t2 = q2.h;
                      t2.canvas.width = w.width;
                      t2.canvas.height = w.height;
                      t2.activeTexture(t2.TEXTURE0);
                      q2.i.bindTexture2d(q2.m);
                      t2.texImage2D(t2.TEXTURE_2D, 0, t2.RGBA, t2.RGBA, t2.UNSIGNED_BYTE, n);
                      q2.i.bindTexture2d(0);
                      q2 = w;
                      break a;
                    case "detections":
                      q2 = c.s[k.stream];
                      q2 || (q2 = new $c(c.i), c.s[k.stream] = q2);
                      q2.data || (q2.data = new q2.h.DetectionListData());
                      q2.data.reset(n.length);
                      for (w = 0; w < n.length; ++w) {
                        t2 = n[w];
                        var v = q2.data, B2 = v.setBoundingBox, J2 = w;
                        var I = t2.la;
                        var u = new Qc();
                        W2(u, 1, I.sa);
                        W2(u, 2, I.ta);
                        W2(u, 3, I.height);
                        W2(u, 4, I.width);
                        W2(u, 5, I.rotation);
                        V2(u, 6, I.pa);
                        I = u.l();
                        B2.call(v, J2, I);
                        if (t2.ea) for (v = 0; v < t2.ea.length; ++v) {
                          u = t2.ea[v];
                          B2 = q2.data;
                          J2 = B2.addNormalizedLandmark;
                          I = w;
                          u = Object.assign({}, u, { visibility: u.visibility ? u.visibility : 0 });
                          var C = new Lc();
                          W2(C, 1, u.x);
                          W2(C, 2, u.y);
                          W2(C, 3, u.z);
                          u.visibility && W2(C, 4, u.visibility);
                          u = C.l();
                          J2.call(
                            B2,
                            I,
                            u
                          );
                        }
                        if (t2.ba) for (v = 0; v < t2.ba.length; ++v) B2 = q2.data, J2 = B2.addClassification, I = w, u = t2.ba[v], C = new Gc(), W2(C, 2, u.qa), u.index && V2(C, 1, u.index), u.label && V2(C, 3, u.label), u.displayName && V2(C, 4, u.displayName), u = C.l(), J2.call(B2, I, u);
                      }
                      q2 = q2.data;
                      break a;
                    default:
                      q2 = {};
                  }
                }
                l = q2;
                m = k.stream;
                switch (k.type) {
                  case "video":
                    e.pushTexture2d(Object.assign({}, l, { stream: m, timestamp: d }));
                    break;
                  case "detections":
                    r = l;
                    r.stream = m;
                    r.timestamp = d;
                    e.pushDetectionList(r);
                    break;
                  default:
                    throw Error("Unknown input config type: '" + k.type + "'");
                }
              }
              c.j.send(e);
              return D2(p, c.I, 4);
            case 4:
              e.delete(), p.h = 0;
          }
        });
      };
      function ld(a, b, c) {
        var d, e, g, f, h, k, l, m, r, p, n, q2, t2, w;
        return E(function(v) {
          switch (v.h) {
            case 1:
              if (!c) return v.return(b);
              d = {};
              e = 0;
              g = A(Object.keys(c));
              for (f = g.next(); !f.done; f = g.next()) h = f.value, k = c[h], "string" !== typeof k && "texture" === k.type && void 0 !== b[k.stream] && ++e;
              1 < e && (a.M = false);
              l = A(Object.keys(c));
              f = l.next();
            case 2:
              if (f.done) {
                v.h = 4;
                break;
              }
              m = f.value;
              r = c[m];
              if ("string" === typeof r) return t2 = d, w = m, D2(v, md(a, m, b[r]), 14);
              p = b[r.stream];
              if ("detection_list" === r.type) {
                if (p) {
                  var B2 = p.getRectList();
                  for (var J2 = p.getLandmarksList(), I = p.getClassificationsList(), u = [], C = 0; C < B2.size(); ++C) {
                    var U2 = Sc(B2.get(C)), pd = X2(U2, 1), qd = X2(U2, 2), rd = X2(U2, 3), sd = X2(U2, 4), td = X2(U2, 5, 0), za = void 0;
                    za = void 0 === za ? 0 : za;
                    U2 = { la: { sa: pd, ta: qd, height: rd, width: sd, rotation: td, pa: Qb(T(U2, 6), za) }, ea: Wc(J2.get(C)), ba: Uc(Kc(I.get(C))) };
                    u.push(U2);
                  }
                  B2 = u;
                } else B2 = [];
                d[m] = B2;
                v.h = 7;
                break;
              }
              if ("proto_list" === r.type) {
                if (p) {
                  B2 = Array(p.size());
                  for (J2 = 0; J2 < p.size(); J2++) B2[J2] = p.get(J2);
                  p.delete();
                } else B2 = [];
                d[m] = B2;
                v.h = 7;
                break;
              }
              if (void 0 === p) {
                v.h = 3;
                break;
              }
              if ("float_list" === r.type) {
                d[m] = p;
                v.h = 7;
                break;
              }
              if ("proto" === r.type) {
                d[m] = p;
                v.h = 7;
                break;
              }
              if ("texture" !== r.type) throw Error("Unknown output config type: '" + r.type + "'");
              n = a.v[m];
              n || (n = new Xc(a.i, a.K), a.v[m] = n);
              return D2(v, Yc(n, p, a.M), 13);
            case 13:
              q2 = v.i, d[m] = q2;
            case 7:
              r.transform && d[m] && (d[m] = r.transform(d[m]));
              v.h = 3;
              break;
            case 14:
              t2[w] = v.i;
            case 3:
              f = l.next();
              v.h = 2;
              break;
            case 4:
              return v.return(d);
          }
        });
      }
      function md(a, b, c) {
        var d;
        return E(function(e) {
          return "number" === typeof c || c instanceof Uint8Array || c instanceof a.i.Uint8BlobList ? e.return(c) : c instanceof a.i.Texture2dDataOut ? (d = a.v[b], d || (d = new Xc(a.i, a.K), a.v[b] = d), e.return(Yc(d, c, a.M))) : e.return(void 0);
        });
      }
      function jd(a, b) {
        for (var c = b.name || "$", d = [].concat(ea(b.wants)), e = new a.i.StringList(), g = A(b.wants), f = g.next(); !f.done; f = g.next()) e.push_back(f.value);
        g = a.i.PacketListener.implement({ onResults: function(h) {
          for (var k = {}, l = 0; l < b.wants.length; ++l) k[d[l]] = h.get(l);
          var m = a.listeners[c];
          m && (a.I = ld(a, k, b.outs).then(function(r) {
            r = m(r);
            for (var p = 0; p < b.wants.length; ++p) {
              var n = k[d[p]];
              "object" === typeof n && n.hasOwnProperty && n.hasOwnProperty("delete") && n.delete();
            }
            r && (a.I = r);
          }));
        } });
        a.j.attachMultiListener(e, g);
        e.delete();
      }
      x.onResults = function(a, b) {
        this.listeners[b || "$"] = a;
      };
      G2("Solution", fd);
      G2("OptionType", { BOOL: 0, NUMBER: 1, ua: 2, 0: "BOOL", 1: "NUMBER", 2: "STRING" });
      function nd(a) {
        void 0 === a && (a = 0);
        switch (a) {
          case 1:
            return "pose_landmark_full.tflite";
          case 2:
            return "pose_landmark_heavy.tflite";
          default:
            return "pose_landmark_lite.tflite";
        }
      }
      function od(a) {
        var b = this;
        a = a || {};
        this.h = new fd({ locateFile: a.locateFile, files: function(c) {
          return [{ url: "pose_solution_packed_assets_loader.js" }, { simd: false, url: "pose_solution_wasm_bin.js" }, { simd: true, url: "pose_solution_simd_wasm_bin.js" }, { data: true, url: nd(c.modelComplexity) }];
        }, graph: { url: "pose_web.binarypb" }, listeners: [{ wants: ["pose_landmarks", "world_landmarks", "segmentation_mask", "image_transformed"], outs: { image: { type: "texture", stream: "image_transformed" }, poseLandmarks: {
          type: "proto",
          stream: "pose_landmarks",
          transform: Wc
        }, poseWorldLandmarks: { type: "proto", stream: "world_landmarks", transform: Wc }, segmentationMask: { type: "texture", stream: "segmentation_mask" } } }], inputs: { image: { type: "video", stream: "input_frames_gpu" } }, options: {
          useCpuInference: { type: 0, graphOptionXref: { calculatorType: "InferenceCalculator", fieldName: "use_cpu_inference" }, default: "object" !== typeof window || void 0 === window.navigator ? false : "iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform) || navigator.userAgent.includes("Mac") && "ontouchend" in document },
          selfieMode: { type: 0, graphOptionXref: { calculatorType: "GlScalerCalculator", calculatorIndex: 1, fieldName: "flip_horizontal" } },
          modelComplexity: { type: 1, graphOptionXref: { calculatorType: "ConstantSidePacketCalculator", calculatorName: "ConstantSidePacketCalculatorModelComplexity", fieldName: "int_value" }, onChange: function(c) {
            var d, e, g;
            return E(function(f) {
              if (1 == f.h) return d = nd(c), e = "third_party/mediapipe/modules/pose_landmark/" + d, D2(f, hd(b.h, d), 2);
              g = f.i;
              b.h.overrideFile(e, g);
              return f.return(true);
            });
          } },
          smoothLandmarks: { type: 0, graphOptionXref: { calculatorType: "ConstantSidePacketCalculator", calculatorName: "ConstantSidePacketCalculatorSmoothLandmarks", fieldName: "bool_value" } },
          enableSegmentation: { type: 0, graphOptionXref: { calculatorType: "ConstantSidePacketCalculator", calculatorName: "ConstantSidePacketCalculatorEnableSegmentation", fieldName: "bool_value" } },
          smoothSegmentation: { type: 0, graphOptionXref: {
            calculatorType: "ConstantSidePacketCalculator",
            calculatorName: "ConstantSidePacketCalculatorSmoothSegmentation",
            fieldName: "bool_value"
          } },
          minDetectionConfidence: { type: 1, graphOptionXref: { calculatorType: "TensorsToDetectionsCalculator", calculatorName: "poselandmarkgpu__posedetectiongpu__TensorsToDetectionsCalculator", fieldName: "min_score_thresh" } },
          minTrackingConfidence: { type: 1, graphOptionXref: { calculatorType: "ThresholdingCalculator", calculatorName: "poselandmarkgpu__poselandmarkbyroigpu__tensorstoposelandmarksandsegmentation__ThresholdingCalculator", fieldName: "threshold" } }
        } });
      }
      x = od.prototype;
      x.reset = function() {
        this.h.reset();
      };
      x.close = function() {
        this.h.close();
        return Promise.resolve();
      };
      x.onResults = function(a) {
        this.h.onResults(a);
      };
      x.initialize = function() {
        var a = this;
        return E(function(b) {
          return D2(b, a.h.initialize(), 0);
        });
      };
      x.send = function(a, b) {
        var c = this;
        return E(function(d) {
          return D2(d, c.h.send(a, b), 0);
        });
      };
      x.setOptions = function(a) {
        this.h.setOptions(a);
      };
      G2("Pose", od);
      G2("POSE_CONNECTIONS", [[0, 1], [1, 2], [2, 3], [3, 7], [0, 4], [4, 5], [5, 6], [6, 8], [9, 10], [11, 12], [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], [17, 19], [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], [18, 20], [11, 23], [12, 24], [23, 24], [23, 25], [24, 26], [25, 27], [26, 28], [27, 29], [28, 30], [29, 31], [30, 32], [27, 31], [28, 32]]);
      G2("POSE_LANDMARKS", { NOSE: 0, LEFT_EYE_INNER: 1, LEFT_EYE: 2, LEFT_EYE_OUTER: 3, RIGHT_EYE_INNER: 4, RIGHT_EYE: 5, RIGHT_EYE_OUTER: 6, LEFT_EAR: 7, RIGHT_EAR: 8, LEFT_RIGHT: 9, RIGHT_LEFT: 10, LEFT_SHOULDER: 11, RIGHT_SHOULDER: 12, LEFT_ELBOW: 13, RIGHT_ELBOW: 14, LEFT_WRIST: 15, RIGHT_WRIST: 16, LEFT_PINKY: 17, RIGHT_PINKY: 18, LEFT_INDEX: 19, RIGHT_INDEX: 20, LEFT_THUMB: 21, RIGHT_THUMB: 22, LEFT_HIP: 23, RIGHT_HIP: 24, LEFT_KNEE: 25, RIGHT_KNEE: 26, LEFT_ANKLE: 27, RIGHT_ANKLE: 28, LEFT_HEEL: 29, RIGHT_HEEL: 30, LEFT_FOOT_INDEX: 31, RIGHT_FOOT_INDEX: 32 });
      G2("POSE_LANDMARKS_LEFT", { LEFT_EYE_INNER: 1, LEFT_EYE: 2, LEFT_EYE_OUTER: 3, LEFT_EAR: 7, LEFT_RIGHT: 9, LEFT_SHOULDER: 11, LEFT_ELBOW: 13, LEFT_WRIST: 15, LEFT_PINKY: 17, LEFT_INDEX: 19, LEFT_THUMB: 21, LEFT_HIP: 23, LEFT_KNEE: 25, LEFT_ANKLE: 27, LEFT_HEEL: 29, LEFT_FOOT_INDEX: 31 });
      G2("POSE_LANDMARKS_RIGHT", { RIGHT_EYE_INNER: 4, RIGHT_EYE: 5, RIGHT_EYE_OUTER: 6, RIGHT_EAR: 8, RIGHT_LEFT: 10, RIGHT_SHOULDER: 12, RIGHT_ELBOW: 14, RIGHT_WRIST: 16, RIGHT_PINKY: 18, RIGHT_INDEX: 20, RIGHT_THUMB: 22, RIGHT_HIP: 24, RIGHT_KNEE: 26, RIGHT_ANKLE: 28, RIGHT_HEEL: 30, RIGHT_FOOT_INDEX: 32 });
      G2("POSE_LANDMARKS_NEUTRAL", { NOSE: 0 });
      G2("VERSION", "0.5.1675469404");
    }).call(exports);
  }
});

// node_modules/@tensorflow-models/pose-detection/dist/pose-detection.esm.js
var import_pose = __toESM(require_pose());
var L = function(t2, e) {
  return (L = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(t3, e2) {
    t3.__proto__ = e2;
  } || function(t3, e2) {
    for (var n in e2) Object.prototype.hasOwnProperty.call(e2, n) && (t3[n] = e2[n]);
  })(t2, e);
};
function V(t2, e) {
  if ("function" != typeof e && null !== e) throw new TypeError("Class extends value " + String(e) + " is not a constructor or null");
  function n() {
    this.constructor = t2;
  }
  L(t2, e), t2.prototype = null === e ? Object.create(e) : (n.prototype = e.prototype, new n());
}
var B = function() {
  return (B = Object.assign || function(t2) {
    for (var e, n = 1, i = arguments.length; n < i; n++) for (var r in e = arguments[n]) Object.prototype.hasOwnProperty.call(e, r) && (t2[r] = e[r]);
    return t2;
  }).apply(this, arguments);
};
function N(t2, e, n, i) {
  return new (n || (n = Promise))(function(r, o) {
    function a(t3) {
      try {
        u(i.next(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function s(t3) {
      try {
        u(i.throw(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function u(t3) {
      var e2;
      t3.done ? r(t3.value) : (e2 = t3.value, e2 instanceof n ? e2 : new n(function(t4) {
        t4(e2);
      })).then(a, s);
    }
    u((i = i.apply(t2, e || [])).next());
  });
}
function D(t2, e) {
  var n, i, r, o, a = { label: 0, sent: function() {
    if (1 & r[0]) throw r[1];
    return r[1];
  }, trys: [], ops: [] };
  return o = { next: s(0), throw: s(1), return: s(2) }, "function" == typeof Symbol && (o[Symbol.iterator] = function() {
    return this;
  }), o;
  function s(o2) {
    return function(s2) {
      return function(o3) {
        if (n) throw new TypeError("Generator is already executing.");
        for (; a; ) try {
          if (n = 1, i && (r = 2 & o3[0] ? i.return : o3[0] ? i.throw || ((r = i.return) && r.call(i), 0) : i.next) && !(r = r.call(i, o3[1])).done) return r;
          switch (i = 0, r && (o3 = [2 & o3[0], r.value]), o3[0]) {
            case 0:
            case 1:
              r = o3;
              break;
            case 4:
              return a.label++, { value: o3[1], done: false };
            case 5:
              a.label++, i = o3[1], o3 = [0];
              continue;
            case 7:
              o3 = a.ops.pop(), a.trys.pop();
              continue;
            default:
              if (!(r = a.trys, (r = r.length > 0 && r[r.length - 1]) || 6 !== o3[0] && 2 !== o3[0])) {
                a = 0;
                continue;
              }
              if (3 === o3[0] && (!r || o3[1] > r[0] && o3[1] < r[3])) {
                a.label = o3[1];
                break;
              }
              if (6 === o3[0] && a.label < r[1]) {
                a.label = r[1], r = o3;
                break;
              }
              if (r && a.label < r[2]) {
                a.label = r[2], a.ops.push(o3);
                break;
              }
              r[2] && a.ops.pop(), a.trys.pop();
              continue;
          }
          o3 = e.call(t2, a);
        } catch (t3) {
          o3 = [6, t3], i = 0;
        } finally {
          n = r = 0;
        }
        if (5 & o3[0]) throw o3[1];
        return { value: o3[0] ? o3[1] : void 0, done: true };
      }([o2, s2]);
    };
  }
}
function K(t2, e, n) {
  if (n || 2 === arguments.length) for (var i, r = 0, o = e.length; r < o; r++) !i && r in e || (i || (i = Array.prototype.slice.call(e, 0, r)), i[r] = e[r]);
  return t2.concat(i || Array.prototype.slice.call(e));
}
var U = ["nose", "left_eye", "right_eye", "left_ear", "right_ear", "left_shoulder", "right_shoulder", "left_elbow", "right_elbow", "left_wrist", "right_wrist", "left_hip", "right_hip", "left_knee", "right_knee", "left_ankle", "right_ankle"];
var j = ["nose", "left_eye_inner", "left_eye", "left_eye_outer", "right_eye_inner", "right_eye", "right_eye_outer", "left_ear", "right_ear", "mouth_left", "mouth_right", "left_shoulder", "right_shoulder", "left_elbow", "right_elbow", "left_wrist", "right_wrist", "left_pinky", "right_pinky", "left_index", "right_index", "left_thumb", "right_thumb", "left_hip", "right_hip", "left_knee", "right_knee", "left_ankle", "right_ankle", "left_heel", "right_heel", "left_foot_index", "right_foot_index"];
var H = { left: [1, 2, 3, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31], right: [4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32], middle: [0] };
var q = { left: [1, 3, 5, 7, 9, 11, 13, 15], right: [2, 4, 6, 8, 10, 12, 14, 16], middle: [0] };
var X = [[0, 1], [0, 2], [1, 3], [2, 4], [5, 6], [5, 7], [5, 11], [6, 8], [6, 12], [7, 9], [8, 10], [11, 12], [11, 13], [12, 14], [13, 15], [14, 16]];
var Y = [[0, 1], [0, 4], [1, 2], [2, 3], [3, 7], [4, 5], [5, 6], [6, 8], [9, 10], [11, 12], [11, 13], [11, 23], [12, 14], [14, 16], [12, 24], [13, 15], [15, 17], [16, 18], [16, 20], [15, 17], [15, 19], [15, 21], [16, 22], [17, 19], [18, 20], [23, 25], [23, 24], [24, 26], [25, 27], [26, 28], [27, 29], [28, 30], [27, 31], [28, 32], [29, 31], [30, 32]];
function W(t2) {
  return t2 instanceof SVGAnimatedLength ? t2.baseVal.value : t2;
}
function G(t2) {
  return N(this, void 0, void 0, function() {
    var i, r;
    return D(this, function(o) {
      switch (o.label) {
        case 0:
          return i = document.createElement("canvas"), t2 instanceof Tensor ? [4, browser_exports.toPixels(t2, i)] : [3, 2];
        case 1:
          return o.sent(), [3, 3];
        case 2:
          i.width = W(t2.width), i.height = W(t2.height), r = i.getContext("2d"), t2 instanceof ImageData ? r.putImageData(t2, 0, 0) : r.drawImage(t2, 0, 0), o.label = 3;
        case 3:
          return [2, i];
      }
    });
  });
}
function Q(t2) {
  return N(this, void 0, void 0, function() {
    var i, r, o, a, s, u;
    return D(this, function(h) {
      switch (h.label) {
        case 0:
          return t2 instanceof Tensor ? (i = t2.shape.slice(0, 2), r = i[0], o = i[1], a = ImageData.bind, [4, browser_exports.toPixels(t2)]) : [3, 2];
        case 1:
          return [2, new (a.apply(ImageData, [void 0, h.sent(), o, r]))()];
        case 2:
          return s = document.createElement("canvas"), u = s.getContext("2d"), s.width = W(t2.width), s.height = W(t2.height), u.drawImage(t2, 0, 0), [2, u.getImageData(0, 0, s.width, s.height)];
      }
    });
  });
}
function Z(t2) {
  return N(this, void 0, void 0, function() {
    var e, i;
    return D(this, function(r) {
      switch (r.label) {
        case 0:
          return t2 instanceof SVGImageElement || t2 instanceof OffscreenCanvas ? [4, G(t2)] : [3, 2];
        case 1:
          return i = r.sent(), [3, 3];
        case 2:
          i = t2, r.label = 3;
        case 3:
          return e = i, [2, browser_exports.fromPixels(e, 4)];
      }
    });
  });
}
function $(t2) {
  if (t2 < 0 || t2 >= 256) throw new Error("Mask value must be in range [0, 255] but got ".concat(t2));
  if (!Number.isInteger(t2)) throw new Error("Mask value must be an integer but got ".concat(t2));
}
var J = { runtime: "mediapipe", enableSmoothing: true, enableSegmentation: false, smoothSegmentation: true, modelType: "full" };
var tt = function() {
  function t2(t3) {
    this.mask = t3;
  }
  return t2.prototype.toCanvasImageSource = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, this.mask];
      });
    });
  }, t2.prototype.toImageData = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, Q(this.mask)];
      });
    });
  }, t2.prototype.toTensor = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, Z(this.mask)];
      });
    });
  }, t2.prototype.getUnderlyingType = function() {
    return "canvasimagesource";
  }, t2;
}();
function et(t2) {
  return $(t2), "person";
}
var nt = function() {
  function i(e) {
    var n, i2 = this;
    switch (this.width = 0, this.height = 0, this.selfieMode = false, this.poseSolution = new import_pose.Pose({ locateFile: function(t2, n2) {
      if (e.solutionPath) {
        var i3 = e.solutionPath.replace(/\/+$/, "");
        return "".concat(i3, "/").concat(t2);
      }
      return "".concat(n2, "/").concat(t2);
    } }), e.modelType) {
      case "lite":
        n = 0;
        break;
      case "heavy":
        n = 2;
        break;
      case "full":
      default:
        n = 1;
    }
    this.poseSolution.setOptions({ modelComplexity: n, smoothLandmarks: e.enableSmoothing, enableSegmentation: e.enableSegmentation, smoothSegmentation: e.smoothSegmentation, selfieMode: this.selfieMode }), this.poseSolution.onResults(function(t2) {
      if (i2.height = t2.image.height, i2.width = t2.image.width, null == t2.poseLandmarks) i2.poses = [];
      else {
        var e2 = i2.translateOutput(t2.poseLandmarks, t2.poseWorldLandmarks);
        t2.segmentationMask && (e2.segmentation = { maskValueToLabel: et, mask: new tt(t2.segmentationMask) }), i2.poses = [e2];
      }
    });
  }
  return i.prototype.translateOutput = function(t2, e) {
    var n = this, i2 = { keypoints: t2.map(function(t3, e2) {
      return { x: t3.x * n.width, y: t3.y * n.height, z: t3.z, score: t3.visibility, name: j[e2] };
    }) };
    return null != e && (i2.keypoints3D = e.map(function(t3, e2) {
      return { x: t3.x, y: t3.y, z: t3.z, score: t3.visibility, name: j[e2] };
    })), i2;
  }, i.prototype.estimatePoses = function(t2, i2, r) {
    return N(this, void 0, void 0, function() {
      var o, a;
      return D(this, function(s) {
        switch (s.label) {
          case 0:
            return i2 && i2.flipHorizontal && i2.flipHorizontal !== this.selfieMode && (this.selfieMode = i2.flipHorizontal, this.poseSolution.setOptions({ selfieMode: this.selfieMode })), t2 instanceof Tensor ? (a = ImageData.bind, [4, browser_exports.toPixels(t2)]) : [3, 2];
          case 1:
            return o = new (a.apply(ImageData, [void 0, s.sent(), t2.shape[1], t2.shape[0]]))(), [3, 3];
          case 2:
            o = t2, s.label = 3;
          case 3:
            return t2 = o, [4, this.poseSolution.send({ image: t2 }, r)];
          case 4:
            return s.sent(), [2, this.poses];
        }
      });
    });
  }, i.prototype.dispose = function() {
    this.poseSolution.close();
  }, i.prototype.reset = function() {
    this.poseSolution.reset();
  }, i.prototype.initialize = function() {
    return this.poseSolution.initialize();
  }, i;
}();
function it(t2) {
  return N(this, void 0, void 0, function() {
    var e, n;
    return D(this, function(i) {
      switch (i.label) {
        case 0:
          return e = function(t3) {
            if (null == t3) return B({}, J);
            var e2 = B({}, t3);
            return e2.runtime = "mediapipe", null == e2.enableSegmentation && (e2.enableSegmentation = J.enableSegmentation), null == e2.enableSmoothing && (e2.enableSmoothing = J.enableSmoothing), null == e2.smoothSegmentation && (e2.smoothSegmentation = J.smoothSegmentation), null == e2.modelType && (e2.modelType = J.modelType), e2;
          }(t2), [4, (n = new nt(e)).initialize()];
        case 1:
          return i.sent(), [2, n];
      }
    });
  });
}
function rt(t2) {
  return t2 instanceof Tensor ? { height: t2.shape[0], width: t2.shape[1] } : { height: t2.height, width: t2.width };
}
function ot(t2) {
  return t2 - 2 * Math.PI * Math.floor((t2 + Math.PI) / (2 * Math.PI));
}
function at(t2) {
  return t2 instanceof Tensor ? t2 : browser_exports.fromPixels(t2);
}
function st(t2, e, n) {
  return ut(n, "inputResolution"), [1 / n.width * t2[0][0] * e.width, 1 / n.height * t2[0][1] * e.width, t2[0][3] * e.width, 1 / n.width * t2[1][0] * e.height, 1 / n.height * t2[1][1] * e.height, t2[1][3] * e.height, 0, 0];
}
function ut(t2, e) {
  util_exports.assert(0 !== t2.width, function() {
    return "".concat(e, " width cannot be 0.");
  }), util_exports.assert(0 !== t2.height, function() {
    return "".concat(e, " height cannot be 0.");
  });
}
function ht(t2, e, n) {
  var i = n.rotationVectorStartKeypointIndex, r = n.rotationVectorEndKeypointIndex, o = t2.locationData, a = o.relativeKeypoints[i].x * e.width, s = o.relativeKeypoints[i].y * e.height, u = o.relativeKeypoints[r].x * e.width, h = o.relativeKeypoints[r].y * e.height, l = 2 * Math.sqrt((u - a) * (u - a) + (h - s) * (h - s)), c = function(t3, e2, n2) {
    var i2, r2 = t3.locationData, o2 = n2.rotationVectorStartKeypointIndex, a2 = n2.rotationVectorEndKeypointIndex;
    i2 = n2.rotationVectorTargetAngle ? n2.rotationVectorTargetAngle : Math.PI * n2.rotationVectorTargetAngleDegree / 180;
    var s2 = r2.relativeKeypoints[o2].x * e2.width, u2 = r2.relativeKeypoints[o2].y * e2.height, h2 = r2.relativeKeypoints[a2].x * e2.width, l2 = r2.relativeKeypoints[a2].y * e2.height;
    return ot(i2 - Math.atan2(-(l2 - u2), h2 - s2));
  }(t2, e, n);
  return { xCenter: a / e.width, yCenter: s / e.height, width: l / e.width, height: l / e.height, rotation: c };
}
function lt(t2) {
  if (16 !== t2.length) throw new Error("Array length must be 16 but got ".concat(t2.length));
  return [[t2[0], t2[1], t2[2], t2[3]], [t2[4], t2[5], t2[6], t2[7]], [t2[8], t2[9], t2[10], t2[11]], [t2[12], t2[13], t2[14], t2[15]]];
}
function ct(t2, e, n, i, r, o, a) {
  return t2[e][r] * (t2[n][o] * t2[i][a] - t2[n][a] * t2[i][o]);
}
function pt(t2, e, n) {
  var i = (e + 1) % 4, r = (e + 2) % 4, o = (e + 3) % 4, a = (n + 1) % 4, s = (n + 2) % 4, u = (n + 3) % 4;
  return ct(t2, i, r, o, a, s, u) + ct(t2, r, o, i, a, s, u) + ct(t2, o, i, r, a, s, u);
}
function ft(t2, e, n) {
  void 0 === n && (n = { ignoreRotation: false });
  for (var i = [], r = 0, o = t2; r < o.length; r++) {
    var a = o[r], s = a.x - 0.5, u = a.y - 0.5, h = n.ignoreRotation ? 0 : e.rotation, l = Math.cos(h) * s - Math.sin(h) * u, c = Math.sin(h) * s + Math.cos(h) * u;
    l = l * e.width + e.xCenter, c = c * e.height + e.yCenter;
    var p = a.z * e.width, f = B({}, a);
    f.x = l, f.y = c, f.z = p, i.push(f);
  }
  return i;
}
function dt(t2, e) {
  var n = function(t3, e2, n2, i) {
    var r = e2 - t3, o = i - n2;
    if (0 === r) throw new Error("Original min and max are both ".concat(t3, ", range cannot be 0."));
    var a = o / r;
    return { scale: a, offset: n2 - t3 * a };
  }(0, 255, e[0], e[1]);
  return tidy(function() {
    return add(mul(t2, n.scale), n.offset);
  });
}
function mt(t2, e, n) {
  var i, o, a, c, p, f, d, m, g, y, v, x, w, k, b = e.outputTensorSize, M = e.keepAspectRatio, S = e.borderMode, T = e.outputTensorFloatRange, P = rt(t2), F = function(t3, e2) {
    return e2 ? { xCenter: e2.xCenter * t3.width, yCenter: e2.yCenter * t3.height, width: e2.width * t3.width, height: e2.height * t3.height, rotation: e2.rotation } : { xCenter: 0.5 * t3.width, yCenter: 0.5 * t3.height, width: t3.width, height: t3.height, rotation: 0 };
  }(P, n), _ = function(t3, e2, n2) {
    if (void 0 === n2 && (n2 = false), !n2) return { top: 0, left: 0, right: 0, bottom: 0 };
    var i2 = e2.height, r = e2.width;
    ut(e2, "targetSize"), ut(t3, "roi");
    var o2, a2, s = i2 / r, u = t3.height / t3.width, h = 0, l = 0;
    return s > u ? (o2 = t3.width, a2 = t3.width * s, l = (1 - u / s) / 2) : (o2 = t3.height / s, a2 = t3.height, h = (1 - s / u) / 2), t3.width = o2, t3.height = a2, { top: l, left: h, right: h, bottom: l };
  }(F, b, M), O = (i = F, o = P.width, a = P.height, c = false, p = i.width, f = i.height, d = c ? -1 : 1, m = Math.cos(i.rotation), g = Math.sin(i.rotation), y = i.xCenter, v = i.yCenter, x = 1 / o, w = 1 / a, (k = new Array(16))[0] = p * m * d * x, k[1] = -f * g * x, k[2] = 0, k[3] = (-0.5 * p * m * d + 0.5 * f * g + y) * x, k[4] = p * g * d * w, k[5] = f * m * w, k[6] = 0, k[7] = (-0.5 * f * m - 0.5 * p * g * d + v) * w, k[8] = 0, k[9] = 0, k[10] = p * x, k[11] = 0, k[12] = 0, k[13] = 0, k[14] = 0, k[15] = 1, lt(k));
  return { imageTensor: tidy(function() {
    var e2 = at(t2), n2 = tensor2d(st(O, P, b), [1, 8]), i2 = "zero" === S ? "constant" : "nearest", r = image.transform(expandDims(cast(e2, "float32")), n2, "bilinear", i2, 0, [b.height, b.width]);
    return null != T ? dt(r, T) : r;
  }), padding: _, transformationMatrix: O };
}
function gt(t2, e, n, i) {
  return 1 === i ? 0.5 * (t2 + e) : t2 + (e - t2) * n / (i - 1);
}
function yt(t2) {
  return tidy(function() {
    var e = function(t3) {
      return tidy(function() {
        return [slice(t3, [0, 0, 0], [1, -1, 1]), slice(t3, [0, 0, 1], [1, -1, -1])];
      });
    }(t2), n = e[0], i = e[1];
    return { boxes: squeeze(i), logits: squeeze(n) };
  });
}
function vt(t2) {
  return null != t2 && null != t2.currentTime;
}
function xt(t2) {
  for (var e = { locationData: { relativeKeypoints: [] } }, n = Number.MAX_SAFE_INTEGER, i = Number.MIN_SAFE_INTEGER, r = Number.MAX_SAFE_INTEGER, o = Number.MIN_SAFE_INTEGER, a = 0; a < t2.length; ++a) {
    var s = t2[a];
    n = Math.min(n, s.x), i = Math.max(i, s.x), r = Math.min(r, s.y), o = Math.max(o, s.y), e.locationData.relativeKeypoints.push({ x: s.x, y: s.y });
  }
  return e.locationData.relativeBoundingBox = { xMin: n, yMin: r, xMax: i, yMax: o, width: i - n, height: o - r }, e;
}
function wt(t2, e, n, i) {
  return N(this, void 0, void 0, function() {
    var i2, r, o, a, h;
    return D(this, function(l) {
      switch (l.label) {
        case 0:
          return t2.sort(function(t3, e2) {
            return Math.max.apply(Math, e2.score) - Math.max.apply(Math, t3.score);
          }), i2 = tensor2d(t2.map(function(t3) {
            return [t3.locationData.relativeBoundingBox.yMin, t3.locationData.relativeBoundingBox.xMin, t3.locationData.relativeBoundingBox.yMax, t3.locationData.relativeBoundingBox.xMax];
          })), r = tensor1d(t2.map(function(t3) {
            return t3.score[0];
          })), [4, image.nonMaxSuppressionAsync(i2, r, e, n)];
        case 1:
          return [4, (o = l.sent()).array()];
        case 2:
          return a = l.sent(), h = t2.filter(function(t3, e2) {
            return a.indexOf(e2) > -1;
          }), dispose([i2, r, o]), [2, h];
      }
    });
  });
}
function kt(t2, e) {
  return t2.map(function(t3) {
    var n = B(B({}, t3), { x: t3.x * e.width, y: t3.y * e.height });
    return null != t3.z && (n.z = t3.z * e.width), n;
  });
}
function bt(t2, e, n) {
  return N(this, void 0, void 0, function() {
    var i, r, o, a, s, u, h, l, c, f, d, m, g, y, v, x, w, k, b, M, S, T, P, F;
    return D(this, function(_) {
      switch (_.label) {
        case 0:
          if (i = squeeze(e, [0]), r = i.shape, o = r[0], a = r[1], s = r[2], t2.length !== s) throw new Error("Expected heatmap to have same number of channels as the number of landmarks. But got landmarks length: " + "".concat(t2.length, ", heatmap length: ").concat(s));
          return u = [], [4, i.buffer()];
        case 1:
          for (h = _.sent(), l = 0; l < t2.length; l++) if (c = t2[l], f = B({}, c), u.push(f), d = Math.trunc(f.x * a), m = Math.trunc(f.y * o), !(d < 0 || d >= a || m < 0 || d >= o)) {
            for (g = Math.trunc((n.kernelSize - 1) / 2), y = Math.max(0, d - g), v = Math.min(a, d + g + 1), x = Math.max(0, m - g), w = Math.min(o, m + g + 1), k = 0, b = 0, M = 0, S = 0, T = x; T < w; ++T) for (P = y; P < v; ++P) F = h.get(T, P, l), k += F, S = Math.max(S, F), b += P * F, M += T * F;
            S >= n.minConfidenceToRefine && k > 0 && (f.x = b / a / k, f.y = M / o / k);
          }
          return i.dispose(), [2, u];
      }
    });
  });
}
function Mt(t2, e) {
  var n = e.left, i = e.top, r = e.left + e.right, o = e.top + e.bottom;
  return t2.map(function(t3) {
    return B(B({}, t3), { x: (t3.x - n) / (1 - r), y: (t3.y - i) / (1 - o), z: t3.z / (1 - r) });
  });
}
function St(t2, e, n) {
  return "webgl" === getBackend() ? function(t3, e2, n2) {
    var i = n2.combineWithPreviousRatio.toFixed(2), o = { variableNames: ["prevMask", "newMask"], outputShape: t3.shape, userCode: "\n  void main() {\n      ivec2 coords = getOutputCoords();\n      int height = coords[0];\n      int width = coords[1];\n\n      float prevMaskValue = getPrevMask(height, width);\n      float newMaskValue = getNewMask(height, width);\n\n      /*\n      * Assume p := newMaskValue\n      * H(p) := 1 + (p * log(p) + (1-p) * log(1-p)) / log(2)\n      * uncertainty alpha(p) =\n      *   Clamp(1 - (1 - H(p)) * (1 - H(p)), 0, 1) [squaring the\n      * uncertainty]\n      *\n      * The following polynomial approximates uncertainty alpha as a\n      * function of (p + 0.5):\n      */\n      const float c1 = 5.68842;\n      const float c2 = -0.748699;\n      const float c3 = -57.8051;\n      const float c4 = 291.309;\n      const float c5 = -624.717;\n      float t = newMaskValue - 0.5;\n      float x = t * t;\n\n      float uncertainty =\n        1.0 - min(1.0, x * (c1 + x * (c2 + x * (c3 + x * (c4 + x * c5)))));\n\n      float outputValue = newMaskValue + (prevMaskValue - newMaskValue) *\n                             (uncertainty * ".concat(i, ");\n\n      setOutput(outputValue);\n    }\n") }, a = backend();
    return tidy(function() {
      var n3 = a.compileAndRun(o, [t3, e2]);
      return engine().makeTensorFromDataId(n3.dataId, n3.shape, n3.dtype);
    });
  }(t2, e, n) : tidy(function() {
    var i = sub(e, 0.5), r = square(i), s = sub(1, minimum(1, mul(r, add(5.68842, mul(r, add(-0.748699, mul(r, add(-57.8051, mul(r, add(291.309, mul(r, -624.717)))))))))));
    return add(e, mul(sub(t2, e), mul(s, n.combineWithPreviousRatio)));
  });
}
function Tt(t2, e, n) {
  return N(this, void 0, void 0, function() {
    var i, s, u, h, l;
    return D(this, function(d) {
      switch (d.label) {
        case 0:
          return i = t2[0], s = t2[1], u = function(t3, e2, n2) {
            return tidy(function() {
              var i2, r, s2, u2;
              n2.reverseOutputOrder ? (r = squeeze(slice(t3, [0, n2.boxCoordOffset + 0], [-1, 1])), i2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 1], [-1, 1])), u2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 2], [-1, 1])), s2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 3], [-1, 1]))) : (i2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 0], [-1, 1])), r = squeeze(slice(t3, [0, n2.boxCoordOffset + 1], [-1, 1])), s2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 2], [-1, 1])), u2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 3], [-1, 1]))), r = add(mul(div(r, n2.xScale), e2.w), e2.x), i2 = add(mul(div(i2, n2.yScale), e2.h), e2.y), n2.applyExponentialOnBoxSize ? (s2 = mul(exp(div(s2, n2.hScale)), e2.h), u2 = mul(exp(div(u2, n2.wScale)), e2.w)) : (s2 = mul(div(s2, n2.hScale), e2.h), u2 = mul(div(u2, n2.wScale), e2.h));
              var h2 = sub(i2, div(s2, 2)), l2 = sub(r, div(u2, 2)), f = add(i2, div(s2, 2)), d2 = add(r, div(u2, 2)), m = concat([reshape(h2, [n2.numBoxes, 1]), reshape(l2, [n2.numBoxes, 1]), reshape(f, [n2.numBoxes, 1]), reshape(d2, [n2.numBoxes, 1])], 1);
              if (n2.numKeypoints) for (var g = 0; g < n2.numKeypoints; ++g) {
                var v = n2.keypointCoordOffset + g * n2.numValuesPerKeypoint, x = void 0, w = void 0;
                n2.reverseOutputOrder ? (x = squeeze(slice(t3, [0, v], [-1, 1])), w = squeeze(slice(t3, [0, v + 1], [-1, 1]))) : (w = squeeze(slice(t3, [0, v], [-1, 1])), x = squeeze(slice(t3, [0, v + 1], [-1, 1])));
                var T = add(mul(div(x, n2.xScale), e2.w), e2.x), P = add(mul(div(w, n2.yScale), e2.h), e2.y);
                m = concat([m, reshape(T, [n2.numBoxes, 1]), reshape(P, [n2.numBoxes, 1])], 1);
              }
              return m;
            });
          }(s, e, n), h = tidy(function() {
            var t3 = i;
            return n.sigmoidScore ? (null != n.scoreClippingThresh && (t3 = clipByValue(i, -n.scoreClippingThresh, n.scoreClippingThresh)), t3 = sigmoid(t3)) : t3;
          }), [4, Pt(u, h, n)];
        case 1:
          return l = d.sent(), dispose([u, h]), [2, l];
      }
    });
  });
}
function Pt(t2, e, n) {
  return N(this, void 0, void 0, function() {
    var i, r, o, a, s, u, h, l, c, p, f, d;
    return D(this, function(m) {
      switch (m.label) {
        case 0:
          return i = [], [4, t2.data()];
        case 1:
          return r = m.sent(), [4, e.data()];
        case 2:
          for (o = m.sent(), a = 0; a < n.numBoxes; ++a) if (!(null != n.minScoreThresh && o[a] < n.minScoreThresh || (s = a * n.numCoords, u = Ft(r[s + 0], r[s + 1], r[s + 2], r[s + 3], o[a], n.flipVertically, a), (h = u.locationData.relativeBoundingBox).width < 0 || h.height < 0))) {
            if (n.numKeypoints > 0) for ((l = u.locationData).relativeKeypoints = [], c = n.numKeypoints * n.numValuesPerKeypoint, p = 0; p < c; p += n.numValuesPerKeypoint) f = s + n.keypointCoordOffset + p, d = { x: r[f + 0], y: n.flipVertically ? 1 - r[f + 1] : r[f + 1] }, l.relativeKeypoints.push(d);
            i.push(u);
          }
          return [2, i];
      }
    });
  });
}
function Ft(t2, e, n, i, r, o, a) {
  return { score: [r], ind: a, locationData: { relativeBoundingBox: { xMin: e, yMin: o ? 1 - n : t2, xMax: i, yMax: o ? 1 - t2 : n, width: i - e, height: n - t2 } } };
}
function _t(t2, e) {
  return "none" === t2 ? e : function(t3) {
    return 1 / (1 + Math.exp(-t3));
  }(e);
}
function Ot(t2, e, n, i) {
  return N(this, void 0, void 0, function() {
    var r, o, a, s, u, h, l, c;
    return D(this, function(p) {
      switch (p.label) {
        case 0:
          return n = n || e.flipHorizontally || false, i = i || e.flipVertically || false, r = t2.size, o = r / e.numLandmarks, [4, t2.data()];
        case 1:
          for (a = p.sent(), s = [], u = 0; u < e.numLandmarks; ++u) h = u * o, (c = { x: 0, y: 0 }).x = n ? e.inputImageWidth - a[h] : a[h], o > 1 && (c.y = i ? e.inputImageHeight - a[h + 1] : a[h + 1]), o > 2 && (c.z = a[h + 2]), o > 3 && (c.score = _t(e.visibilityActivation, a[h + 3])), s.push(c);
          for (l = 0; l < s.length; ++l) (c = s[l]).x = c.x / e.inputImageWidth, c.y = c.y / e.inputImageHeight, c.z = c.z / e.inputImageWidth / (e.normalizeZ || 1);
          return [2, s];
      }
    });
  });
}
function It(t2, e, n) {
  var i = t2.width, r = t2.height, o = t2.rotation;
  if (null == n.rotation && null == n.rotationDegree || (o = function(t3, e2) {
    null != e2.rotation ? t3 += e2.rotation : null != e2.rotationDegree && (t3 += Math.PI * e2.rotationDegree / 180);
    return ot(t3);
  }(o, n)), 0 === o) t2.xCenter = t2.xCenter + i * n.shiftX, t2.yCenter = t2.yCenter + r * n.shiftY;
  else {
    var a = (e.width * i * n.shiftX * Math.cos(o) - e.height * r * n.shiftY * Math.sin(o)) / e.width, s = (e.width * i * n.shiftX * Math.sin(o) + e.height * r * n.shiftY * Math.cos(o)) / e.height;
    t2.xCenter = t2.xCenter + a, t2.yCenter = t2.yCenter + s;
  }
  if (n.squareLong) {
    var u = Math.max(i * e.width, r * e.height);
    i = u / e.width, r = u / e.height;
  } else if (n.squareShort) {
    var h = Math.min(i * e.width, r * e.height);
    i = h / e.width, r = h / e.height;
  }
  return t2.width = i * n.scaleX, t2.height = r * n.scaleY, t2;
}
function At(t2, e) {
  return t2.map(function(t3) {
    var n = B(B({}, t3), { x: t3.x / e.width, y: t3.y / e.height });
    return null != t3.z && (t3.z = t3.z / e.width), n;
  });
}
var zt = function() {
  function t2(t3) {
    this.alpha = t3, this.initialized = false;
  }
  return t2.prototype.apply = function(t3, e) {
    var n;
    return this.initialized ? n = null == e ? this.storedValue + this.alpha * (t3 - this.storedValue) : this.storedValue + this.alpha * e * Math.asinh((t3 - this.storedValue) / e) : (n = t3, this.initialized = true), this.rawValue = t3, this.storedValue = n, n;
  }, t2.prototype.applyWithAlpha = function(t3, e, n) {
    return this.alpha = e, this.apply(t3, n);
  }, t2.prototype.hasLastRawValue = function() {
    return this.initialized;
  }, t2.prototype.lastRawValue = function() {
    return this.rawValue;
  }, t2.prototype.reset = function() {
    this.initialized = false;
  }, t2;
}();
var Ct = function() {
  function t2(t3) {
    this.frequency = t3.frequency, this.minCutOff = t3.minCutOff, this.beta = t3.beta, this.thresholdCutOff = t3.thresholdCutOff, this.thresholdBeta = t3.thresholdBeta, this.derivateCutOff = t3.derivateCutOff, this.x = new zt(this.getAlpha(this.minCutOff)), this.dx = new zt(this.getAlpha(this.derivateCutOff)), this.lastTimestamp = 0;
  }
  return t2.prototype.apply = function(t3, e, n) {
    if (null == t3) return t3;
    var i = Math.trunc(e);
    if (this.lastTimestamp >= i) return t3;
    0 !== this.lastTimestamp && 0 !== i && (this.frequency = 1 / (1e-6 * (i - this.lastTimestamp))), this.lastTimestamp = i;
    var r = this.x.hasLastRawValue() ? (t3 - this.x.lastRawValue()) * n * this.frequency : 0, o = this.dx.applyWithAlpha(r, this.getAlpha(this.derivateCutOff)), a = this.minCutOff + this.beta * Math.abs(o), s = null != this.thresholdCutOff ? this.thresholdCutOff + this.thresholdBeta * Math.abs(o) : null;
    return this.x.applyWithAlpha(t3, this.getAlpha(a), s);
  }, t2.prototype.getAlpha = function(t3) {
    return 1 / (1 + this.frequency / (2 * Math.PI * t3));
  }, t2;
}();
var Et = function() {
  function t2(t3) {
    this.config = t3;
  }
  return t2.prototype.apply = function(t3, e, n) {
    var i = this;
    if (null == t3) return this.reset(), null;
    this.initializeFiltersIfEmpty(t3);
    var r = 1;
    if (!this.config.disableValueScaling) {
      if (n < this.config.minAllowedObjectScale) return K([], t3, true);
      r = 1 / n;
    }
    return t3.map(function(t4, n2) {
      var o = B(B({}, t4), { x: i.xFilters[n2].apply(t4.x, e, r), y: i.yFilters[n2].apply(t4.y, e, r) });
      return null != t4.z && (o.z = i.zFilters[n2].apply(t4.z, e, r)), o;
    });
  }, t2.prototype.reset = function() {
    this.xFilters = null, this.yFilters = null, this.zFilters = null;
  }, t2.prototype.initializeFiltersIfEmpty = function(t3) {
    var e = this;
    null != this.xFilters && this.xFilters.length === t3.length || (this.xFilters = t3.map(function(t4) {
      return new Ct(e.config);
    }), this.yFilters = t3.map(function(t4) {
      return new Ct(e.config);
    }), this.zFilters = t3.map(function(t4) {
      return new Ct(e.config);
    }));
  }, t2;
}();
var Rt = function() {
  function t2(t3) {
    this.config = t3, this.window = [], this.lowPassFilter = new zt(1), this.lastValue = 0, this.lastValueScale = 1, this.lastTimestamp = -1;
  }
  return t2.prototype.apply = function(t3, e, n) {
    if (null == t3) return t3;
    var i, r = Math.trunc(e);
    if (this.lastTimestamp >= r) return t3;
    if (-1 === this.lastTimestamp) i = 1;
    else {
      for (var o = t3 * n - this.lastValue * this.lastValueScale, a = r - this.lastTimestamp, s = o, u = a, h = (1 + this.window.length) * (1e6 / 30), l = 0, c = this.window; l < c.length; l++) {
        var p = c[l];
        if (u + p.duration > h) break;
        s += p.distance, u += p.duration;
      }
      var f = s / (1e-6 * u);
      i = 1 - 1 / (1 + this.config.velocityScale * Math.abs(f)), this.window.unshift({ distance: o, duration: a }), this.window.length > this.config.windowSize && this.window.pop();
    }
    return this.lastValue = t3, this.lastValueScale = n, this.lastTimestamp = r, this.lowPassFilter.applyWithAlpha(t3, i);
  }, t2;
}();
var Lt = function() {
  function t2(t3) {
    this.config = t3;
  }
  return t2.prototype.apply = function(t3, e, n) {
    var i = this;
    if (null == t3) return this.reset(), null;
    var r = 1;
    if (!this.config.disableValueScaling) {
      if (n < this.config.minAllowedObjectScale) return K([], t3, true);
      r = 1 / n;
    }
    return this.initializeFiltersIfEmpty(t3), t3.map(function(t4, n2) {
      var o = B(B({}, t4), { x: i.xFilters[n2].apply(t4.x, e, r), y: i.yFilters[n2].apply(t4.y, e, r) });
      return null != t4.z && (o.z = i.zFilters[n2].apply(t4.z, e, r)), o;
    });
  }, t2.prototype.reset = function() {
    this.xFilters = null, this.yFilters = null, this.zFilters = null;
  }, t2.prototype.initializeFiltersIfEmpty = function(t3) {
    var e = this;
    null != this.xFilters && this.xFilters.length === t3.length || (this.xFilters = t3.map(function(t4) {
      return new Rt(e.config);
    }), this.yFilters = t3.map(function(t4) {
      return new Rt(e.config);
    }), this.zFilters = t3.map(function(t4) {
      return new Rt(e.config);
    }));
  }, t2;
}();
var Vt = function() {
  function t2(t3) {
    if (null != t3.velocityFilter) this.keypointsFilter = new Lt(t3.velocityFilter);
    else {
      if (null == t3.oneEuroFilter) throw new Error("Either configure velocityFilter or oneEuroFilter, but got " + "".concat(t3, "."));
      this.keypointsFilter = new Et(t3.oneEuroFilter);
    }
  }
  return t2.prototype.apply = function(t3, e, n, i, r) {
    if (void 0 === i && (i = false), null == t3) return this.keypointsFilter.reset(), null;
    var o = null != r ? function(t4, e2) {
      return (t4.width * e2.width + t4.height * e2.height) / 2;
    }(r, n) : 1, a = i ? kt(t3, n) : t3, s = this.keypointsFilter.apply(a, e, o);
    return i ? At(s, n) : s;
  }, t2;
}();
var Bt = function() {
  function t2(t3) {
    this.alpha = t3.alpha;
  }
  return t2.prototype.apply = function(t3) {
    var e = this;
    if (null == t3) return this.visibilityFilters = null, null;
    null != this.visibilityFilters && this.visibilityFilters.length === t3.length || (this.visibilityFilters = t3.map(function(t4) {
      return new zt(e.alpha);
    }));
    for (var n = [], i = 0; i < t3.length; ++i) {
      var r = t3[i], o = B({}, r);
      o.score = this.visibilityFilters[i].apply(r.score), n.push(o);
    }
    return n;
  }, t2;
}();
var Nt = { reduceBoxesInLowestlayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: [], featureMapWidth: [], numLayers: 5, minScale: 0.1484375, maxScale: 0.75, inputSizeHeight: 224, inputSizeWidth: 224, anchorOffsetX: 0.5, anchorOffsetY: 0.5, strides: [8, 16, 32, 32, 32], aspectRatios: [1], fixedAnchorSize: true };
var Dt = { runtime: "tfjs", modelType: "full", enableSmoothing: true, enableSegmentation: false, smoothSegmentation: true, detectorModelUrl: "https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/detector/1", landmarkModelUrl: "https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/full/2" };
var Kt = { maxPoses: 1, flipHorizontal: false };
var Ut = { applyExponentialOnBoxSize: false, flipVertically: false, ignoreClasses: [], numClasses: 1, numBoxes: 2254, numCoords: 12, boxCoordOffset: 0, keypointCoordOffset: 4, numKeypoints: 4, numValuesPerKeypoint: 2, sigmoidScore: true, scoreClippingThresh: 100, reverseOutputOrder: true, xScale: 224, yScale: 224, hScale: 224, wScale: 224, minScoreThresh: 0.5 };
var jt = 0.3;
var Ht = { shiftX: 0, shiftY: 0, scaleX: 1.25, scaleY: 1.25, squareLong: true };
var qt = { outputTensorSize: { width: 224, height: 224 }, keepAspectRatio: true, outputTensorFloatRange: [-1, 1], borderMode: "zero" };
var Xt = { outputTensorSize: { width: 256, height: 256 }, keepAspectRatio: true, outputTensorFloatRange: [0, 1], borderMode: "zero" };
var Yt = { numLandmarks: 39, inputImageWidth: 256, inputImageHeight: 256, visibilityActivation: "sigmoid", flipHorizontally: false, flipVertically: false };
var Wt = { numLandmarks: 39, inputImageWidth: 1, inputImageHeight: 1, visibilityActivation: "sigmoid", flipHorizontally: false, flipVertically: false };
var Gt = { kernelSize: 7, minConfidenceToRefine: 0.5 };
var Qt = { alpha: 0.1 };
var Zt = { oneEuroFilter: { frequency: 30, minCutOff: 0.05, beta: 80, derivateCutOff: 1, minAllowedObjectScale: 1e-6 } };
var $t = { oneEuroFilter: { frequency: 30, minCutOff: 0.01, beta: 10, derivateCutOff: 1, minAllowedObjectScale: 1e-6 } };
var Jt = { oneEuroFilter: { frequency: 30, minCutOff: 0.1, beta: 40, derivateCutOff: 1, minAllowedObjectScale: 1e-6, disableValueScaling: true } };
var te = { activation: "none" };
var ee = { combineWithPreviousRatio: 0.7 };
var ne = function() {
  function t2(t3) {
    this.mask = t3;
  }
  return t2.prototype.toCanvasImageSource = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, G(this.mask)];
      });
    });
  }, t2.prototype.toImageData = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, Q(this.mask)];
      });
    });
  }, t2.prototype.toTensor = function() {
    return N(this, void 0, void 0, function() {
      return D(this, function(t3) {
        return [2, this.mask];
      });
    });
  }, t2.prototype.getUnderlyingType = function() {
    return "tensor";
  }, t2;
}();
function ie(t2) {
  return $(t2), "person";
}
var re = function() {
  function t2(t3, e, n, i, r, o) {
    this.detectorModel = t3, this.landmarkModel = e, this.enableSmoothing = n, this.enableSegmentation = i, this.smoothSegmentation = r, this.modelType = o, this.regionOfInterest = null, this.prevFilteredSegmentationMask = null, this.anchors = function(t4) {
      null == t4.reduceBoxesInLowestLayer && (t4.reduceBoxesInLowestLayer = false), null == t4.interpolatedScaleAspectRatio && (t4.interpolatedScaleAspectRatio = 1), null == t4.fixedAnchorSize && (t4.fixedAnchorSize = false);
      for (var e2 = [], n2 = 0; n2 < t4.numLayers; ) {
        for (var i2 = [], r2 = [], o2 = [], a2 = [], s = n2; s < t4.strides.length && t4.strides[s] === t4.strides[n2]; ) {
          var u2 = gt(t4.minScale, t4.maxScale, s, t4.strides.length);
          if (0 === s && t4.reduceBoxesInLowestLayer) o2.push(1), o2.push(2), o2.push(0.5), a2.push(0.1), a2.push(u2), a2.push(u2);
          else {
            for (var h2 = 0; h2 < t4.aspectRatios.length; ++h2) o2.push(t4.aspectRatios[h2]), a2.push(u2);
            if (t4.interpolatedScaleAspectRatio > 0) {
              var l2 = s === t4.strides.length - 1 ? 1 : gt(t4.minScale, t4.maxScale, s + 1, t4.strides.length);
              a2.push(Math.sqrt(u2 * l2)), o2.push(t4.interpolatedScaleAspectRatio);
            }
          }
          s++;
        }
        for (var c = 0; c < o2.length; ++c) {
          var p = Math.sqrt(o2[c]);
          i2.push(a2[c] / p), r2.push(a2[c] * p);
        }
        var f = 0, d = 0;
        if (t4.featureMapHeight.length > 0) f = t4.featureMapHeight[n2], d = t4.featureMapWidth[n2];
        else {
          var m = t4.strides[n2];
          f = Math.ceil(t4.inputSizeHeight / m), d = Math.ceil(t4.inputSizeWidth / m);
        }
        for (var g = 0; g < f; ++g) for (var y = 0; y < d; ++y) for (var v = 0; v < i2.length; ++v) {
          var x = { xCenter: (y + t4.anchorOffsetX) / d, yCenter: (g + t4.anchorOffsetY) / f, width: 0, height: 0 };
          t4.fixedAnchorSize ? (x.width = 1, x.height = 1) : (x.width = r2[v], x.height = i2[v]), e2.push(x);
        }
        n2 = s;
      }
      return e2;
    }(Nt);
    var a = tensor1d(this.anchors.map(function(t4) {
      return t4.width;
    })), u = tensor1d(this.anchors.map(function(t4) {
      return t4.height;
    })), h = tensor1d(this.anchors.map(function(t4) {
      return t4.xCenter;
    })), l = tensor1d(this.anchors.map(function(t4) {
      return t4.yCenter;
    }));
    this.anchorTensor = { x: h, y: l, w: a, h: u }, this.prevFilteredSegmentationMask = this.enableSegmentation ? tensor2d([], [0, 0]) : null;
  }
  return t2.prototype.estimatePoses = function(t3, e, n) {
    return N(this, void 0, void 0, function() {
      var i, o, a, s, u, c, p, d, m, g, y, v, x, w, k, b, M, S, T, P, O, I, A;
      return D(this, function(z) {
        switch (z.label) {
          case 0:
            return i = function(t4) {
              var e2;
              if (null == (e2 = null == t4 ? Kt : B({}, t4)).maxPoses && (e2.maxPoses = 1), e2.maxPoses <= 0) throw new Error("Invalid maxPoses ".concat(e2.maxPoses, ". Should be > 0."));
              if (e2.maxPoses > 1) throw new Error("Multi-pose detection is not implemented yet. Please set maxPoses to 1.");
              return e2;
            }(e), null == t3 ? (this.reset(), [2, []]) : (this.maxPoses = i.maxPoses, this.timestamp = null != n ? 1e3 * n : vt(t3) ? 1e6 * t3.currentTime : null, o = rt(t3), a = tidy(function() {
              return cast(at(t3), "float32");
            }), null != (s = this.regionOfInterest) ? [3, 2] : [4, this.detectPose(a)]);
          case 1:
            if (0 === (u = z.sent()).length) return this.reset(), a.dispose(), [2, []];
            c = u[0], s = this.poseDetectionToRoi(c, o), z.label = 2;
          case 2:
            return [4, this.poseLandmarksByRoi(s, a)];
          case 3:
            return p = z.sent(), a.dispose(), null == p ? (this.reset(), [2, []]) : (d = p.landmarks, m = p.auxiliaryLandmarks, g = p.poseScore, y = p.worldLandmarks, v = p.segmentationMask, x = this.poseLandmarkFiltering(d, m, y, o), w = x.actualLandmarksFiltered, k = x.auxiliaryLandmarksFiltered, b = x.actualWorldLandmarksFiltered, M = this.poseLandmarksToRoi(k, o), this.regionOfInterest = M, S = this.smoothSegmentation && null != v ? this.poseSegmentationFiltering(v) : v, null != (T = null != w ? kt(w, o) : null) && T.forEach(function(t4, e2) {
              t4.name = j[e2];
            }), null != (P = b) && P.forEach(function(t4, e2) {
              t4.name = j[e2];
            }), O = { score: g, keypoints: T, keypoints3D: P }, null !== S && (I = tidy(function() {
              var t4 = expandDims(S, 2), e2 = pad(t4, [[0, 0], [0, 0], [0, 1]]);
              return mirrorPad(e2, [[0, 0], [0, 0], [0, 2]], "symmetric");
            }), this.smoothSegmentation || dispose(S), A = { maskValueToLabel: ie, mask: new ne(I) }, O.segmentation = A), [2, [O]]);
        }
      });
    });
  }, t2.prototype.poseSegmentationFiltering = function(t3) {
    var e = this.prevFilteredSegmentationMask;
    return 0 === e.size ? this.prevFilteredSegmentationMask = t3 : (this.prevFilteredSegmentationMask = St(e, t3, ee), dispose(t3)), dispose(e), this.prevFilteredSegmentationMask;
  }, t2.prototype.dispose = function() {
    this.detectorModel.dispose(), this.landmarkModel.dispose(), dispose([this.anchorTensor.x, this.anchorTensor.y, this.anchorTensor.w, this.anchorTensor.h, this.prevFilteredSegmentationMask]);
  }, t2.prototype.reset = function() {
    this.regionOfInterest = null, this.enableSegmentation && (dispose(this.prevFilteredSegmentationMask), this.prevFilteredSegmentationMask = tensor2d([], [0, 0])), this.visibilitySmoothingFilterActual = null, this.visibilitySmoothingFilterAuxiliary = null, this.landmarksSmoothingFilterActual = null, this.landmarksSmoothingFilterAuxiliary = null;
  }, t2.prototype.detectPose = function(t3) {
    return N(this, void 0, void 0, function() {
      var e, n, i, r, o, a, s, u, h, l;
      return D(this, function(c) {
        switch (c.label) {
          case 0:
            return e = mt(t3, qt), n = e.imageTensor, i = e.padding, r = this.detectorModel.predict(n), o = yt(r), a = o.boxes, [4, Tt([s = o.logits, a], this.anchorTensor, Ut)];
          case 1:
            return 0 === (u = c.sent()).length ? (dispose([n, r, s, a]), [2, u]) : [4, wt(u, this.maxPoses, jt)];
          case 2:
            return h = c.sent(), l = function(t4, e2) {
              void 0 === t4 && (t4 = []);
              for (var n2 = e2.left, i2 = e2.top, r2 = e2.left + e2.right, o2 = e2.top + e2.bottom, a2 = 0; a2 < t4.length; a2++) {
                var s2 = t4[a2], u2 = s2.locationData.relativeBoundingBox, h2 = (u2.xMin - n2) / (1 - r2), l2 = (u2.yMin - i2) / (1 - o2), c2 = u2.width / (1 - r2), p = u2.height / (1 - o2);
                u2.xMin = h2, u2.yMin = l2, u2.width = c2, u2.height = p, u2.xMax = h2 + c2, u2.yMax = l2 + p;
                var f = s2.locationData.relativeKeypoints;
                f && f.forEach(function(t5) {
                  var e3 = (t5.x - n2) / (1 - r2), a3 = (t5.y - i2) / (1 - o2);
                  t5.x = e3, t5.y = a3;
                });
              }
              return t4;
            }(h, i), dispose([n, r, s, a]), [2, l];
        }
      });
    });
  }, t2.prototype.poseDetectionToRoi = function(t3, e) {
    return 0, 1, It(ht(t3, e, { rotationVectorEndKeypointIndex: 1, rotationVectorStartKeypointIndex: 0, rotationVectorTargetAngleDegree: 90 }), e, Ht);
  }, t2.prototype.poseLandmarksByRoi = function(t3, e) {
    return N(this, void 0, void 0, function() {
      var n, i, r, o, a, s, u, h, l, c, p, d, m, g;
      return D(this, function(y) {
        switch (y.label) {
          case 0:
            if (n = rt(e), i = mt(e, Xt, t3), r = i.imageTensor, o = i.padding, a = i.transformationMatrix, "lite" !== this.modelType && "full" !== this.modelType && "heavy" !== this.modelType) throw new Error("Model type must be one of lite, full or heavy," + "but got ".concat(this.modelType));
            return s = ["ld_3d", "output_poseflag", "activation_heatmap", "world_3d"], this.enableSegmentation && s.push("activation_segmentation"), u = this.landmarkModel.execute(r, s), [4, this.tensorsToPoseLandmarksAndSegmentation(u)];
          case 1:
            return null == (h = y.sent()) ? (dispose(u), dispose(r), [2, null]) : (l = h.landmarks, c = h.auxiliaryLandmarks, p = h.poseScore, d = h.worldLandmarks, m = h.segmentationMask, [4, this.poseLandmarksAndSegmentationInverseProjection(n, t3, o, a, l, c, d, m)]);
          case 2:
            return g = y.sent(), dispose(u), dispose(r), [2, B({ poseScore: p }, g)];
        }
      });
    });
  }, t2.prototype.poseLandmarksAndSegmentationInverseProjection = function(t3, e, n, i, o, a, h, l) {
    return N(this, void 0, void 0, function() {
      var c, d, m, g, y, v;
      return D(this, function(x) {
        return c = Mt(o, n), d = Mt(a, n), m = ft(c, e), g = ft(d, e), y = function(t4, e2) {
          for (var n2 = [], i2 = 0, r = t4; i2 < r.length; i2++) {
            var o2 = r[i2], a2 = o2.x, s = o2.y, u = e2.rotation, h2 = Math.cos(u) * a2 - Math.sin(u) * s, l2 = Math.sin(u) * a2 + Math.cos(u) * s, c2 = B({}, o2);
            c2.x = h2, c2.y = l2, n2.push(c2);
          }
          return n2;
        }(h, e), v = null, this.enableSegmentation && (v = tidy(function() {
          var e2 = l.shape, n2 = e2[0], r = e2[1], o2 = function(t4) {
            var e3 = lt(new Array(16).fill(0));
            e3[0][0] = pt(t4, 0, 0), e3[1][0] = -pt(t4, 0, 1), e3[2][0] = pt(t4, 0, 2), e3[3][0] = -pt(t4, 0, 3), e3[0][2] = pt(t4, 2, 0), e3[1][2] = -pt(t4, 2, 1), e3[2][2] = pt(t4, 2, 2), e3[3][2] = -pt(t4, 2, 3), e3[0][1] = -pt(t4, 1, 0), e3[1][1] = pt(t4, 1, 1), e3[2][1] = -pt(t4, 1, 2), e3[3][1] = pt(t4, 1, 3), e3[0][3] = -pt(t4, 3, 0), e3[1][3] = pt(t4, 3, 1), e3[2][3] = -pt(t4, 3, 2), e3[3][3] = pt(t4, 3, 3);
            for (var n3 = t4[0][0] * e3[0][0] + t4[1][0] * e3[0][1] + t4[2][0] * e3[0][2] + t4[3][0] * e3[0][3], i2 = 0; i2 < e3.length; i2++) for (var r2 = 0; r2 < e3.length; r2++) e3[i2][r2] /= n3;
            return e3;
          }(i), a2 = tensor2d(st(o2, { width: r, height: n2 }, t3), [1, 8]), h2 = [1, n2, r, 1];
          return squeeze(image.transform(reshape(l, h2), a2, "bilinear", "constant", 0, [t3.height, t3.width]), [0, 3]);
        }), dispose(l)), [2, { landmarks: m, auxiliaryLandmarks: g, worldLandmarks: y, segmentationMask: v }];
      });
    });
  }, t2.prototype.tensorsToPoseLandmarksAndSegmentation = function(t3) {
    return N(this, void 0, void 0, function() {
      var e, n, i, o, a, s, h, l, c, f, d, m, g;
      return D(this, function(y) {
        switch (y.label) {
          case 0:
            return e = t3[0], n = t3[1], i = t3[2], o = t3[3], a = this.enableSegmentation ? t3[4] : null, [4, n.data()];
          case 1:
            return (s = y.sent()[0]) < 0.5 ? [2, null] : [4, Ot(e, Yt)];
          case 2:
            return [4, bt(y.sent(), i, Gt)];
          case 3:
            return h = y.sent(), l = h.slice(0, 33), c = h.slice(33, 35), [4, Ot(o, Wt)];
          case 4:
            return f = y.sent(), d = f.slice(0, 33), m = function(t4, e2, n2) {
              void 0 === n2 && (n2 = true);
              for (var i2 = [], r = 0; r < t4.length; r++) {
                var o2 = B({}, e2[r]);
                n2 && (o2.score = t4[r].score), i2.push(o2);
              }
              return i2;
            }(l, d, true), g = this.enableSegmentation ? function(t4, e2, n2) {
              return tidy(function() {
                var i2 = squeeze(t4, [0]), r = i2.shape[2];
                if (1 === r) {
                  var o2 = i2;
                  switch (e2.activation) {
                    case "none":
                      break;
                    case "sigmoid":
                      o2 = sigmoid(o2);
                      break;
                    case "softmax":
                      throw new Error("Softmax activation requires two channels.");
                    default:
                      throw new Error("Activation not supported (".concat(e2.activation, ")"));
                  }
                  var a2 = n2 ? image.resizeBilinear(o2, [n2.height, n2.width]) : o2;
                  return squeeze(a2, [2]);
                }
                throw new Error("Unsupported number of tensor channels ".concat(r));
              });
            }(a, te) : null, [2, { landmarks: l, auxiliaryLandmarks: c, poseScore: s, worldLandmarks: m, segmentationMask: g }];
        }
      });
    });
  }, t2.prototype.poseLandmarksToRoi = function(t3, e) {
    return It(ht(xt(t3), e, { rotationVectorStartKeypointIndex: 0, rotationVectorEndKeypointIndex: 1, rotationVectorTargetAngleDegree: 90 }), e, Ht);
  }, t2.prototype.poseLandmarkFiltering = function(t3, e, n, i) {
    var r, o, a;
    if (null != this.timestamp && this.enableSmoothing) {
      var s = ht(xt(e), i, { rotationVectorEndKeypointIndex: 0, rotationVectorStartKeypointIndex: 1, rotationVectorTargetAngleDegree: 90 });
      null == this.visibilitySmoothingFilterActual && (this.visibilitySmoothingFilterActual = new Bt(Qt)), r = this.visibilitySmoothingFilterActual.apply(t3), null == this.visibilitySmoothingFilterAuxiliary && (this.visibilitySmoothingFilterAuxiliary = new Bt(Qt)), o = this.visibilitySmoothingFilterAuxiliary.apply(e), a = this.visibilitySmoothingFilterActual.apply(n), null == this.landmarksSmoothingFilterActual && (this.landmarksSmoothingFilterActual = new Vt(Zt)), r = this.landmarksSmoothingFilterActual.apply(r, this.timestamp, i, true, s), null == this.landmarksSmoothingFilterAuxiliary && (this.landmarksSmoothingFilterAuxiliary = new Vt($t)), o = this.landmarksSmoothingFilterAuxiliary.apply(o, this.timestamp, i, true, s), null == this.worldLandmarksSmoothingFilterActual && (this.worldLandmarksSmoothingFilterActual = new Vt(Jt)), a = this.worldLandmarksSmoothingFilterActual.apply(n, this.timestamp);
    } else r = t3, o = e, a = n;
    return { actualLandmarksFiltered: r, auxiliaryLandmarksFiltered: o, actualWorldLandmarksFiltered: a };
  }, t2;
}();
function oe(t2) {
  return N(this, void 0, void 0, function() {
    var e, n, i, r, o, a;
    return D(this, function(s) {
      switch (s.label) {
        case 0:
          return e = function(t3) {
            var e2 = B({}, null == t3 ? Dt : t3);
            if (null == e2.enableSmoothing && (e2.enableSmoothing = Dt.enableSmoothing), null == e2.enableSegmentation && (e2.enableSegmentation = Dt.enableSegmentation), null == e2.smoothSegmentation && (e2.smoothSegmentation = Dt.smoothSegmentation), null == e2.modelType && (e2.modelType = Dt.modelType), null == e2.detectorModelUrl && (e2.detectorModelUrl = Dt.detectorModelUrl), null == e2.landmarkModelUrl) switch (e2.modelType) {
              case "lite":
                e2.landmarkModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/lite/2";
                break;
              case "heavy":
                e2.landmarkModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/heavy/2";
                break;
              case "full":
              default:
                e2.landmarkModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/blazepose_3d/landmark/full/2";
            }
            return e2;
          }(t2), n = "string" == typeof e.detectorModelUrl && e.detectorModelUrl.indexOf("https://tfhub.dev") > -1, i = "string" == typeof e.landmarkModelUrl && e.landmarkModelUrl.indexOf("https://tfhub.dev") > -1, [4, Promise.all([loadGraphModel(e.detectorModelUrl, { fromTFHub: n }), loadGraphModel(e.landmarkModelUrl, { fromTFHub: i })])];
        case 1:
          return r = s.sent(), o = r[0], a = r[1], [2, new re(o, a, e.enableSmoothing, e.enableSegmentation, e.smoothSegmentation, e.modelType)];
      }
    });
  });
}
var ae;
var se;
var ue = function() {
  function t2(t3) {
    !function(t4) {
      if (t4.maxTracks < 1) throw new Error("Must specify 'maxTracks' to be at least 1, but " + "encountered ".concat(t4.maxTracks));
      if (t4.maxAge <= 0) throw new Error("Must specify 'maxAge' to be positive, but " + "encountered ".concat(t4.maxAge));
      if (void 0 !== t4.keypointTrackerParams) {
        if (t4.keypointTrackerParams.keypointConfidenceThreshold < 0 || t4.keypointTrackerParams.keypointConfidenceThreshold > 1) throw new Error("Must specify 'keypointConfidenceThreshold' to be in the range [0, 1], but encountered " + "".concat(t4.keypointTrackerParams.keypointConfidenceThreshold));
        if (t4.keypointTrackerParams.minNumberOfKeypoints < 1) throw new Error("Must specify 'minNumberOfKeypoints' to be at least 1, but " + "encountered ".concat(t4.keypointTrackerParams.minNumberOfKeypoints));
        for (var e = 0, n = t4.keypointTrackerParams.keypointFalloff; e < n.length; e++) {
          var i = n[e];
          if (i <= 0) throw new Error("Must specify each keypoint falloff parameterto be positive " + "but encountered ".concat(i));
        }
      }
    }(t3), this.tracks = [], this.maxTracks = t3.maxTracks, this.maxAge = 1e3 * t3.maxAge, this.minSimilarity = t3.minSimilarity, this.nextID = 1;
  }
  return t2.prototype.apply = function(t3, e) {
    this.filterOldTracks(e);
    var n = this.computeSimilarity(t3);
    return this.assignTracks(t3, n, e), this.updateTracks(e), t3;
  }, t2.prototype.getTracks = function() {
    return this.tracks.slice();
  }, t2.prototype.getTrackIDs = function() {
    return new Set(this.tracks.map(function(t3) {
      return t3.id;
    }));
  }, t2.prototype.filterOldTracks = function(t3) {
    var e = this;
    this.tracks = this.tracks.filter(function(n) {
      return t3 - n.lastTimestamp <= e.maxAge;
    });
  }, t2.prototype.assignTracks = function(t3, e, n) {
    for (var i = Array.from(Array(e[0].length).keys()), r = [], o = 0, a = Array.from(Array(t3.length).keys()); o < a.length; o++) {
      var s = a[o];
      if (0 !== i.length) {
        for (var u = -1, h = -1, l = 0, c = i; l < c.length; l++) {
          var p = c[l], f = e[s][p];
          f >= this.minSimilarity && f > h && (u = p, h = f);
        }
        if (u >= 0) {
          var d = this.tracks[u];
          d = Object.assign(d, this.createTrack(t3[s], n, d.id)), t3[s].id = d.id;
          var m = i.indexOf(u);
          i.splice(m, 1);
        } else r.push(s);
      } else r.push(s);
    }
    for (var g = 0, y = r; g < y.length; g++) {
      s = y[g];
      var v = this.createTrack(t3[s], n);
      this.tracks.push(v), t3[s].id = v.id;
    }
  }, t2.prototype.updateTracks = function(t3) {
    this.tracks.sort(function(t4, e) {
      return e.lastTimestamp - t4.lastTimestamp;
    }), this.tracks = this.tracks.slice(0, this.maxTracks);
  }, t2.prototype.createTrack = function(t3, e, n) {
    var i = { id: n || this.nextTrackID(), lastTimestamp: e, keypoints: K([], t3.keypoints, true).map(function(t4) {
      return B({}, t4);
    }) };
    return void 0 !== t3.box && (i.box = B({}, t3.box)), i;
  }, t2.prototype.nextTrackID = function() {
    var t3 = this.nextID;
    return this.nextID += 1, t3;
  }, t2.prototype.remove = function() {
    for (var t3 = [], e = 0; e < arguments.length; e++) t3[e] = arguments[e];
    this.tracks = this.tracks.filter(function(e2) {
      return !t3.includes(e2.id);
    });
  }, t2.prototype.reset = function() {
    this.tracks = [];
  }, t2;
}();
var he = function(t2) {
  function e(e2) {
    return t2.call(this, e2) || this;
  }
  return V(e, t2), e.prototype.computeSimilarity = function(t3) {
    var e2 = this;
    return 0 === t3.length || 0 === this.tracks.length ? [[]] : t3.map(function(t4) {
      return e2.tracks.map(function(n) {
        return e2.iou(t4, n);
      });
    });
  }, e.prototype.iou = function(t3, e2) {
    var n = Math.max(t3.box.xMin, e2.box.xMin), i = Math.max(t3.box.yMin, e2.box.yMin), r = Math.min(t3.box.xMax, e2.box.xMax), o = Math.min(t3.box.yMax, e2.box.yMax);
    if (n >= r || i >= o) return 0;
    var a = (r - n) * (o - i);
    return a / (t3.box.width * t3.box.height + e2.box.width * e2.box.height - a);
  }, e;
}(ue);
var le = function(t2) {
  function e(e2) {
    var n = t2.call(this, e2) || this;
    return n.keypointThreshold = e2.keypointTrackerParams.keypointConfidenceThreshold, n.keypointFalloff = e2.keypointTrackerParams.keypointFalloff, n.minNumKeyoints = e2.keypointTrackerParams.minNumberOfKeypoints, n;
  }
  return V(e, t2), e.prototype.computeSimilarity = function(t3) {
    if (0 === t3.length || 0 === this.tracks.length) return [[]];
    for (var e2 = [], n = 0, i = t3; n < i.length; n++) {
      for (var r = i[n], o = [], a = 0, s = this.tracks; a < s.length; a++) {
        var u = s[a];
        o.push(this.oks(r, u));
      }
      e2.push(o);
    }
    return e2;
  }, e.prototype.oks = function(t3, e2) {
    for (var n = this.area(e2.keypoints) + 1e-6, i = 0, r = 0, o = 0; o < t3.keypoints.length; ++o) {
      var a = t3.keypoints[o], s = e2.keypoints[o];
      if (!(a.score < this.keypointThreshold || s.score < this.keypointThreshold)) {
        r += 1;
        var u = Math.pow(a.x - s.x, 2) + Math.pow(a.y - s.y, 2), h = 2 * this.keypointFalloff[o];
        i += Math.exp(-1 * u / (2 * n * Math.pow(h, 2)));
      }
    }
    return r < this.minNumKeyoints ? 0 : i / r;
  }, e.prototype.area = function(t3) {
    var e2 = this, n = t3.filter(function(t4) {
      return t4.score > e2.keypointThreshold;
    }), i = Math.min.apply(Math, K([1], n.map(function(t4) {
      return t4.x;
    }), false)), r = Math.max.apply(Math, K([0], n.map(function(t4) {
      return t4.x;
    }), false)), o = Math.min.apply(Math, K([1], n.map(function(t4) {
      return t4.y;
    }), false));
    return (r - i) * (Math.max.apply(Math, K([0], n.map(function(t4) {
      return t4.y;
    }), false)) - o);
  }, e;
}(ue);
function ce(t2) {
  switch (t2) {
    case se.BlazePose:
      return j.reduce(function(t3, e, n) {
        return t3[e] = n, t3;
      }, {});
    case se.PoseNet:
    case se.MoveNet:
      return U.reduce(function(t3, e, n) {
        return t3[e] = n, t3;
      }, {});
    default:
      throw new Error("Model ".concat(t2, " is not supported."));
  }
}
!function(t2) {
  t2.Keypoint = "keypoint", t2.BoundingBox = "boundingBox";
}(ae || (ae = {})), function(t2) {
  t2.MoveNet = "MoveNet", t2.BlazePose = "BlazePose", t2.PoseNet = "PoseNet";
}(se || (se = {}));
var pe = Object.freeze({ __proto__: null, getKeypointIndexBySide: function(t2) {
  switch (t2) {
    case se.BlazePose:
      return H;
    case se.PoseNet:
    case se.MoveNet:
      return q;
    default:
      throw new Error("Model ".concat(t2, " is not supported."));
  }
}, getAdjacentPairs: function(t2) {
  switch (t2) {
    case se.BlazePose:
      return Y;
    case se.PoseNet:
    case se.MoveNet:
      return X;
    default:
      throw new Error("Model ".concat(t2, " is not supported."));
  }
}, getKeypointIndexByName: ce });
var fe = ["SinglePose.Lightning", "SinglePose.Thunder", "MultiPose.Lightning"];
var de = { modelType: "SinglePose.Lightning", enableSmoothing: true };
var me = {};
var ge = { frequency: 30, minCutOff: 2.5, beta: 300, derivateCutOff: 2.5, thresholdCutOff: 0.5, thresholdBeta: 5, disableValueScaling: true };
var ye = { maxTracks: 18, maxAge: 1e3, minSimilarity: 0.2, keypointTrackerParams: { keypointConfidenceThreshold: 0.3, keypointFalloff: [0.026, 0.025, 0.025, 0.035, 0.035, 0.079, 0.079, 0.072, 0.072, 0.062, 0.062, 0.107, 0.107, 0.087, 0.087, 0.089, 0.089], minNumberOfKeypoints: 4 } };
var ve = { maxTracks: 18, maxAge: 1e3, minSimilarity: 0.15, trackerParams: {} };
function xe(t2, e, n, i) {
  for (var r = {}, o = 0, a = U; o < a.length; o++) {
    var s = a[o];
    r[s] = [e[n[s]].y * i.height, e[n[s]].x * i.width];
  }
  if (function(t3, e2) {
    return (t3[e2.left_hip].score > 0.2 || t3[e2.right_hip].score > 0.2) && (t3[e2.left_shoulder].score > 0.2 || t3[e2.right_shoulder].score > 0.2);
  }(e, n)) {
    var u = (r.left_hip[0] + r.right_hip[0]) / 2, h = (r.left_hip[1] + r.right_hip[1]) / 2, l = function(t3, e2, n2, i2, r2) {
      for (var o2 = ["left_shoulder", "right_shoulder", "left_hip", "right_hip"], a2 = 0, s2 = 0, u2 = 0; u2 < o2.length; u2++) {
        (f2 = Math.abs(i2 - n2[o2[u2]][0])) > a2 && (a2 = f2), (d2 = Math.abs(r2 - n2[o2[u2]][1])) > s2 && (s2 = d2);
      }
      for (var h2 = 0, l2 = 0, c2 = 0, p2 = Object.keys(n2); c2 < p2.length; c2++) {
        var f2, d2, m2 = p2[c2];
        if (!(t3[e2[m2]].score < 0.2)) (f2 = Math.abs(i2 - n2[m2][0])) > h2 && (h2 = f2), (d2 = Math.abs(r2 - n2[m2][1])) > l2 && (l2 = d2);
      }
      return [a2, s2, h2, l2];
    }(e, n, r, u, h), c = l[0], p = l[1], f = l[2], d = l[3], m = Math.max(1.9 * p, 1.9 * c, 1.2 * f, 1.2 * d), g = [u - (m = Math.min(m, Math.max(h, i.width - h, u, i.height - u))), h - m];
    if (m > Math.max(i.width, i.height) / 2) return we(null == t2, i);
    var y = 2 * m;
    return { yMin: g[0] / i.height, xMin: g[1] / i.width, yMax: (g[0] + y) / i.height, xMax: (g[1] + y) / i.width, height: (g[0] + y) / i.height - g[0] / i.height, width: (g[1] + y) / i.width - g[1] / i.width };
  }
  return we(null == t2, i);
}
function we(t2, e) {
  var n, i, r, o;
  return t2 ? e.width > e.height ? (n = 1, i = e.height / e.width, r = 0, o = (e.width / 2 - e.height / 2) / e.width) : (n = e.width / e.height, i = 1, r = (e.height / 2 - e.width / 2) / e.height, o = 0) : e.width > e.height ? (n = e.width / e.height, i = 1, r = (e.height / 2 - e.width / 2) / e.height, o = 0) : (n = 1, i = e.height / e.width, r = 0, o = (e.width / 2 - e.height / 2) / e.width), { yMin: r, xMin: o, yMax: r + n, xMax: o + i, height: n, width: i };
}
function ke(t2) {
  var e, n = null == t2 ? de : B({}, t2);
  if (null == n.modelType) n.modelType = "SinglePose.Lightning";
  else if (fe.indexOf(n.modelType) < 0) throw new Error("Invalid architecture ".concat(n.modelType, ". ") + "Should be one of ".concat(fe));
  if (null == n.enableSmoothing && (n.enableSmoothing = true), null != n.minPoseScore && (n.minPoseScore < 0 || n.minPoseScore > 1)) throw new Error("minPoseScore should be between 0.0 and 1.0");
  if (null != n.multiPoseMaxDimension && (n.multiPoseMaxDimension % 32 != 0 || n.multiPoseMaxDimension < 32)) throw new Error("multiPoseMaxDimension must be a multiple of 32 and higher than 0");
  if ("MultiPose.Lightning" === n.modelType && null == n.enableTracking && (n.enableTracking = true), "MultiPose.Lightning" === n.modelType && true === n.enableTracking) if (null == n.trackerType && (n.trackerType = ae.BoundingBox), n.trackerType === ae.Keypoint) null != n.trackerConfig ? n.trackerConfig = function(t3) {
    var e2 = be(ye, t3);
    e2.keypointTrackerParams = B({}, ye.keypointTrackerParams), null != t3.keypointTrackerParams && (null != t3.keypointTrackerParams.keypointConfidenceThreshold && (e2.keypointTrackerParams.keypointConfidenceThreshold = t3.keypointTrackerParams.keypointConfidenceThreshold), null != t3.keypointTrackerParams.keypointFalloff && (e2.keypointTrackerParams.keypointFalloff = t3.keypointTrackerParams.keypointFalloff), null != t3.keypointTrackerParams.minNumberOfKeypoints && (e2.keypointTrackerParams.minNumberOfKeypoints = t3.keypointTrackerParams.minNumberOfKeypoints));
    return e2;
  }(n.trackerConfig) : n.trackerConfig = ye;
  else {
    if (n.trackerType !== ae.BoundingBox) throw new Error("Tracker type not supported by MoveNet");
    null != n.trackerConfig ? n.trackerConfig = (e = n.trackerConfig, be(ve, e)) : n.trackerConfig = ve;
  }
  return n;
}
function be(t2, e) {
  var n = { maxTracks: t2.maxTracks, maxAge: t2.maxAge, minSimilarity: t2.minSimilarity };
  return null != e.maxTracks && (n.maxTracks = e.maxTracks), null != e.maxAge && (n.maxAge = e.maxAge), null != e.minSimilarity && (n.minSimilarity = e.minSimilarity), n;
}
var Me = function() {
  function t2(t3, e) {
    this.moveNetModel = t3, this.modelInputResolution = { height: 0, width: 0 }, this.keypointIndexByName = ce(se.MoveNet), "SinglePose.Lightning" === e.modelType ? (this.modelInputResolution.width = 192, this.modelInputResolution.height = 192) : "SinglePose.Thunder" === e.modelType && (this.modelInputResolution.width = 256, this.modelInputResolution.height = 256), this.multiPoseModel = "MultiPose.Lightning" === e.modelType, this.multiPoseModel || (this.keypointFilter = new Et(ge), this.cropRegionFilterYMin = new zt(0.9), this.cropRegionFilterXMin = new zt(0.9), this.cropRegionFilterYMax = new zt(0.9), this.cropRegionFilterXMax = new zt(0.9)), this.enableSmoothing = e.enableSmoothing, e.minPoseScore ? this.minPoseScore = e.minPoseScore : this.minPoseScore = 0.25, e.multiPoseMaxDimension ? this.multiPoseMaxDimension = e.multiPoseMaxDimension : this.multiPoseMaxDimension = 256, this.enableTracking = e.enableTracking, this.multiPoseModel && this.enableTracking && (e.trackerType === ae.Keypoint ? this.tracker = new le(e.trackerConfig) : e.trackerType === ae.BoundingBox && (this.tracker = new he(e.trackerConfig)), this.enableSmoothing && (this.keypointFilterMap = /* @__PURE__ */ new Map()));
  }
  return t2.prototype.runSinglePersonPoseModel = function(t3) {
    return N(this, void 0, void 0, function() {
      var e, n, i, r, o;
      return D(this, function(a) {
        switch (a.label) {
          case 0:
            if (4 !== (e = this.moveNetModel.execute(t3)).shape.length || 1 !== e.shape[0] || 1 !== e.shape[1] || 17 !== e.shape[2] || 3 !== e.shape[3]) throw e.dispose(), new Error("Unexpected output shape from model: [".concat(e.shape, "]"));
            return "webgpu" === getBackend() ? [3, 1] : (n = e.dataSync(), [3, 3]);
          case 1:
            return [4, e.data()];
          case 2:
            n = a.sent(), a.label = 3;
          case 3:
            for (e.dispose(), i = { keypoints: [], score: 0 }, r = 0, o = 0; o < 17; ++o) i.keypoints[o] = { y: n[3 * o], x: n[3 * o + 1], score: n[3 * o + 2] }, i.keypoints[o].score > 0.2 && (++r, i.score += i.keypoints[o].score);
            return r > 0 && (i.score /= r), [2, i];
        }
      });
    });
  }, t2.prototype.runMultiPersonPoseModel = function(t3) {
    return N(this, void 0, void 0, function() {
      var e, n, i, r, o, a, s, u;
      return D(this, function(h) {
        switch (h.label) {
          case 0:
            if (3 !== (e = this.moveNetModel.execute(t3)).shape.length || 1 !== e.shape[0] || 56 !== e.shape[2]) throw e.dispose(), new Error("Unexpected output shape from model: [".concat(e.shape, "]"));
            return "webgpu" === getBackend() ? [3, 1] : (n = e.dataSync(), [3, 3]);
          case 1:
            return [4, e.data()];
          case 2:
            n = h.sent(), h.label = 3;
          case 3:
            for (e.dispose(), i = [], r = n.length / 56, o = 0; o < r; ++o) for (i[o] = { keypoints: [] }, a = 56 * o + 51, i[o].box = { yMin: n[a], xMin: n[a + 1], yMax: n[a + 2], xMax: n[a + 3], width: n[a + 3] - n[a + 1], height: n[a + 2] - n[a] }, s = 56 * o + 55, i[o].score = n[s], i[o].keypoints = [], u = 0; u < 17; ++u) i[o].keypoints[u] = { y: n[56 * o + 3 * u], x: n[56 * o + 3 * u + 1], score: n[56 * o + 3 * u + 2] };
            return [2, i];
        }
      });
    });
  }, t2.prototype.estimatePoses = function(t3, n, i) {
    return void 0 === n && (n = me), N(this, void 0, void 0, function() {
      var r, o, a, s, u, l;
      return D(this, function(c) {
        switch (c.label) {
          case 0:
            return n = function(t4) {
              return null == t4 ? me : B({}, t4);
            }(n), null == t3 ? (this.reset(), [2, []]) : (null == i ? vt(t3) && (i = 1e6 * t3.currentTime) : i *= 1e3, r = at(t3), o = rt(r), a = expandDims(r, 0), t3 instanceof Tensor || r.dispose(), s = [], this.multiPoseModel ? [3, 2] : [4, this.estimateSinglePose(a, o, i)]);
          case 1:
            return s = c.sent(), [3, 4];
          case 2:
            return [4, this.estimateMultiplePoses(a, o, i)];
          case 3:
            s = c.sent(), c.label = 4;
          case 4:
            for (u = 0; u < s.length; ++u) for (l = 0; l < s[u].keypoints.length; ++l) s[u].keypoints[l].name = U[l], s[u].keypoints[l].y *= o.height, s[u].keypoints[l].x *= o.width;
            return [2, s];
        }
      });
    });
  }, t2.prototype.estimateSinglePose = function(t3, e, n) {
    return N(this, void 0, void 0, function() {
      var i, o, a, h, c = this;
      return D(this, function(p) {
        switch (p.label) {
          case 0:
            return this.cropRegion || (this.cropRegion = we(null == this.cropRegion, e)), i = tidy(function() {
              var e2 = tensor2d([[c.cropRegion.yMin, c.cropRegion.xMin, c.cropRegion.yMax, c.cropRegion.xMax]]), n2 = zeros([1], "int32"), i2 = [c.modelInputResolution.height, c.modelInputResolution.width];
              return cast(image.cropAndResize(t3, e2, n2, i2, "bilinear", 0), "int32");
            }), t3.dispose(), [4, this.runSinglePersonPoseModel(i)];
          case 1:
            if (o = p.sent(), i.dispose(), o.score < this.minPoseScore) return this.reset(), [2, []];
            for (a = 0; a < o.keypoints.length; ++a) o.keypoints[a].y = this.cropRegion.yMin + o.keypoints[a].y * this.cropRegion.height, o.keypoints[a].x = this.cropRegion.xMin + o.keypoints[a].x * this.cropRegion.width;
            return null != n && this.enableSmoothing && (o.keypoints = this.keypointFilter.apply(o.keypoints, n, 1)), h = xe(this.cropRegion, o.keypoints, this.keypointIndexByName, e), this.cropRegion = this.filterCropRegion(h), [2, [o]];
        }
      });
    });
  }, t2.prototype.estimateMultiplePoses = function(t3, e, n) {
    return N(this, void 0, void 0, function() {
      var i, r, o, a, s, h, c, p, f, d, m, g = this;
      return D(this, function(y) {
        switch (y.label) {
          case 0:
            return 32, e.width > e.height ? (r = this.multiPoseMaxDimension, o = Math.round(this.multiPoseMaxDimension * e.height / e.width), i = image.resizeBilinear(t3, [o, r]), s = r, h = 32 * Math.ceil(o / 32), a = pad(i, [[0, 0], [0, h - o], [0, 0], [0, 0]])) : (r = Math.round(this.multiPoseMaxDimension * e.width / e.height), o = this.multiPoseMaxDimension, i = image.resizeBilinear(t3, [o, r]), s = 32 * Math.ceil(r / 32), h = o, a = pad(i, [[0, 0], [0, 0], [0, s - r], [0, 0]])), i.dispose(), t3.dispose(), c = cast(a, "int32"), a.dispose(), [4, this.runMultiPersonPoseModel(c)];
          case 1:
            for (p = y.sent(), c.dispose(), p = p.filter(function(t4) {
              return t4.score >= g.minPoseScore;
            }), d = 0; d < p.length; ++d) for (f = 0; f < p[d].keypoints.length; ++f) p[d].keypoints[f].y *= h / o, p[d].keypoints[f].x *= s / r;
            if (this.enableTracking && (this.tracker.apply(p, n), this.enableSmoothing)) {
              for (d = 0; d < p.length; ++d) this.keypointFilterMap.has(p[d].id) || this.keypointFilterMap.set(p[d].id, new Et(ge)), p[d].keypoints = this.keypointFilterMap.get(p[d].id).apply(p[d].keypoints, n, 1);
              m = this.tracker.getTrackIDs(), this.keypointFilterMap.forEach(function(t4, e2) {
                m.has(e2) || g.keypointFilterMap.delete(e2);
              });
            }
            return [2, p];
        }
      });
    });
  }, t2.prototype.filterCropRegion = function(t3) {
    if (t3) {
      var e = this.cropRegionFilterYMin.apply(t3.yMin), n = this.cropRegionFilterXMin.apply(t3.xMin), i = this.cropRegionFilterYMax.apply(t3.yMax), r = this.cropRegionFilterXMax.apply(t3.xMax);
      return { yMin: e, xMin: n, yMax: i, xMax: r, height: i - e, width: r - n };
    }
    return this.cropRegionFilterYMin.reset(), this.cropRegionFilterXMin.reset(), this.cropRegionFilterYMax.reset(), this.cropRegionFilterXMax.reset(), null;
  }, t2.prototype.dispose = function() {
    this.moveNetModel.dispose();
  }, t2.prototype.reset = function() {
    this.cropRegion = null, this.resetFilters();
  }, t2.prototype.resetFilters = function() {
    this.keypointFilter.reset(), this.cropRegionFilterYMin.reset(), this.cropRegionFilterXMin.reset(), this.cropRegionFilterYMax.reset(), this.cropRegionFilterXMax.reset();
  }, t2;
}();
function Se(t2) {
  return void 0 === t2 && (t2 = de), N(this, void 0, void 0, function() {
    var e, n, i, r;
    return D(this, function(o) {
      switch (o.label) {
        case 0:
          return e = ke(t2), i = true, e.modelUrl ? (i = "string" == typeof e.modelUrl && e.modelUrl.indexOf("https://tfhub.dev") > -1, [4, loadGraphModel(e.modelUrl, { fromTFHub: i })]) : [3, 2];
        case 1:
          return n = o.sent(), [3, 4];
        case 2:
          return r = void 0, "SinglePose.Lightning" === e.modelType ? r = "https://tfhub.dev/google/tfjs-model/movenet/singlepose/lightning/4" : "SinglePose.Thunder" === e.modelType ? r = "https://tfhub.dev/google/tfjs-model/movenet/singlepose/thunder/4" : "MultiPose.Lightning" === e.modelType && (r = "https://tfhub.dev/google/tfjs-model/movenet/multipose/lightning/1"), [4, loadGraphModel(r, { fromTFHub: i })];
        case 3:
          n = o.sent(), o.label = 4;
        case 4:
          return "webgl" === getBackend() && env().set("TOPK_LAST_DIM_CPU_HANDOFF_SIZE_THRESHOLD", 0), [2, new Me(n, e)];
      }
    });
  });
}
var Te = { architecture: "MobileNetV1", outputStride: 16, multiplier: 0.75, inputResolution: { height: 257, width: 257 } };
var Pe = ["MobileNetV1", "ResNet50"];
var Fe = { MobileNetV1: [8, 16], ResNet50: [16] };
var _e = [8, 16, 32];
var Oe = { MobileNetV1: [0.5, 0.75, 1], ResNet50: [1] };
var Ie = [1, 2, 4];
var Ae = { maxPoses: 1, flipHorizontal: false };
var ze = { maxPoses: 5, flipHorizontal: false, scoreThreshold: 0.5, nmsRadius: 20 };
var Ce = [-123.15, -115.9, -103.06];
function Ee(t2) {
  return Math.floor(t2 / 2);
}
var Re = function() {
  function t2(t3, e) {
    this.priorityQueue = new Array(t3), this.numberOfElements = -1, this.getElementValue = e;
  }
  return t2.prototype.enqueue = function(t3) {
    this.priorityQueue[++this.numberOfElements] = t3, this.swim(this.numberOfElements);
  }, t2.prototype.dequeue = function() {
    var t3 = this.priorityQueue[0];
    return this.exchange(0, this.numberOfElements--), this.sink(0), this.priorityQueue[this.numberOfElements + 1] = null, t3;
  }, t2.prototype.empty = function() {
    return -1 === this.numberOfElements;
  }, t2.prototype.size = function() {
    return this.numberOfElements + 1;
  }, t2.prototype.all = function() {
    return this.priorityQueue.slice(0, this.numberOfElements + 1);
  }, t2.prototype.max = function() {
    return this.priorityQueue[0];
  }, t2.prototype.swim = function(t3) {
    for (; t3 > 0 && this.less(Ee(t3), t3); ) this.exchange(t3, Ee(t3)), t3 = Ee(t3);
  }, t2.prototype.sink = function(t3) {
    for (; 2 * t3 <= this.numberOfElements; ) {
      var e = 2 * t3;
      if (e < this.numberOfElements && this.less(e, e + 1) && e++, !this.less(t3, e)) break;
      this.exchange(t3, e), t3 = e;
    }
  }, t2.prototype.getValueAt = function(t3) {
    return this.getElementValue(this.priorityQueue[t3]);
  }, t2.prototype.less = function(t3, e) {
    return this.getValueAt(t3) < this.getValueAt(e);
  }, t2.prototype.exchange = function(t3, e) {
    var n = this.priorityQueue[t3];
    this.priorityQueue[t3] = this.priorityQueue[e], this.priorityQueue[e] = n;
  }, t2;
}();
function Le(t2, e, n, i, r, o) {
  for (var a = o.shape, s = a[0], u = a[1], h = true, l = Math.max(n - r, 0), c = Math.min(n + r + 1, s), p = l; p < c; ++p) {
    for (var f = Math.max(i - r, 0), d = Math.min(i + r + 1, u), m = f; m < d; ++m) if (o.get(p, m, t2) > e) {
      h = false;
      break;
    }
    if (!h) break;
  }
  return h;
}
function Ve(t2) {
  return N(this, void 0, void 0, function() {
    return D(this, function(e) {
      return [2, Promise.all(t2.map(function(t3) {
        return t3.buffer();
      }))];
    });
  });
}
function Be(t2, e, n, i) {
  return { y: i.get(t2, e, n), x: i.get(t2, e, n + 17) };
}
function Ne(t2, e, n) {
  var i = Be(t2.heatmapY, t2.heatmapX, t2.id, n), r = i.y, o = i.x;
  return { x: t2.heatmapX * e + o, y: t2.heatmapY * e + r };
}
function De(t2, e, n, i) {
  var r = n.x, o = n.y;
  return t2.some(function(t3) {
    var n2, a, s, u, h, l, c = t3.keypoints;
    return n2 = o, a = r, s = c[i].y, u = c[i].x, (h = s - n2) * h + (l = u - a) * l <= e;
  });
}
var Ke = U.reduce(function(t2, e, n) {
  return t2[e] = n, t2;
}, {});
var Ue = [["nose", "left_eye"], ["left_eye", "left_ear"], ["nose", "right_eye"], ["right_eye", "right_ear"], ["nose", "left_shoulder"], ["left_shoulder", "left_elbow"], ["left_elbow", "left_wrist"], ["left_shoulder", "left_hip"], ["left_hip", "left_knee"], ["left_knee", "left_ankle"], ["nose", "right_shoulder"], ["right_shoulder", "right_elbow"], ["right_elbow", "right_wrist"], ["right_shoulder", "right_hip"], ["right_hip", "right_knee"], ["right_knee", "right_ankle"]].map(function(t2) {
  var e = t2[0], n = t2[1];
  return [Ke[e], Ke[n]];
});
var je = Ue.map(function(t2) {
  return t2[1];
});
var He = Ue.map(function(t2) {
  return t2[0];
});
function qe(t2, e, n) {
  return t2 < e ? e : t2 > n ? n : t2;
}
function Xe(t2, e, n, i) {
  return { y: qe(Math.round(t2.y / e), 0, n - 1), x: qe(Math.round(t2.x / e), 0, i - 1) };
}
function Ye(t2, e) {
  return { x: t2.x + e.x, y: t2.y + e.y };
}
function We(t2, e, n, i, r, o, a, s) {
  void 0 === s && (s = 2);
  for (var u = i.shape, h = u[0], l = u[1], c = { y: e.y, x: e.x }, p = Ye(c, function(t3, e2, n2) {
    var i2 = n2.shape[2] / 2;
    return { y: n2.get(e2.y, e2.x, t3), x: n2.get(e2.y, e2.x, i2 + t3) };
  }(t2, Xe(c, o, h, l), a)), f = 0; f < s; f++) {
    var d = Xe(p, o, h, l), m = Be(d.y, d.x, n, r);
    p = Ye({ x: d.x * o, y: d.y * o }, { x: m.x, y: m.y });
  }
  var g = Xe(p, o, h, l), y = i.get(g.y, g.x, n);
  return { y: p.y, x: p.x, name: U[n], score: y };
}
function Ge(t2, e, n, i, r, o) {
  var a = e.shape[2], s = je.length, u = new Array(a), h = t2.part, l = t2.score, c = Ne(h, i, n);
  u[h.id] = { score: l, name: U[h.id], y: c.y, x: c.x };
  for (var p = s - 1; p >= 0; --p) {
    var f = je[p], d = He[p];
    u[f] && !u[d] && (u[d] = We(p, u[f], d, e, n, i, o));
  }
  for (p = 0; p < s; ++p) {
    f = He[p], d = je[p];
    u[f] && !u[d] && (u[d] = We(p, u[f], d, e, n, i, r));
  }
  return u;
}
function Qe(t2, e, n) {
  return n.reduce(function(n2, i, r) {
    var o = i.y, a = i.x, s = i.score;
    return De(t2, e, { y: o, x: a }, r) || (n2 += s), n2;
  }, 0) / n.length;
}
function Ze(t2, e, n, i, r, o, a, s) {
  return void 0 === a && (a = 0.5), void 0 === s && (s = 20), N(this, void 0, void 0, function() {
    var u, h, l, c, p, f, d, m, g, y, v, x;
    return D(this, function(w) {
      switch (w.label) {
        case 0:
          return [4, Ve([t2, e, n, i])];
        case 1:
          for (u = w.sent(), h = u[0], l = u[1], c = u[2], p = u[3], f = [], d = function(t3, e2, n2) {
            for (var i2 = n2.shape, r2 = i2[0], o2 = i2[1], a2 = i2[2], s2 = new Re(r2 * o2 * a2, function(t4) {
              return t4.score;
            }), u2 = 0; u2 < r2; ++u2) for (var h2 = 0; h2 < o2; ++h2) for (var l2 = 0; l2 < a2; ++l2) {
              var c2 = n2.get(u2, h2, l2);
              c2 < t3 || Le(l2, c2, u2, h2, e2, n2) && s2.enqueue({ score: c2, part: { heatmapY: u2, heatmapX: h2, id: l2 } });
            }
            return s2;
          }(a, 1, h), m = s * s; f.length < o && !d.empty(); ) g = d.dequeue(), y = Ne(g.part, r, l), De(f, m, y, g.part.id) || (v = Ge(g, h, l, r, c, p), x = Qe(f, m, v), f.push({ keypoints: v, score: x }));
          return [2, f];
      }
    });
  });
}
function $e() {
  for (var t2, e = [], n = 0; n < arguments.length; n++) e[n] = arguments[n];
  switch (e.length) {
    case 0:
      t2 = "fn main() ";
      break;
    case 1:
      t2 = "fn main(".concat(e[0], " : i32)");
      break;
    default:
      throw Error("Unreachable");
  }
  return t2;
}
var Je = function() {
  function t2(t3) {
    this.variableNames = ["A", "B"], this.size = true;
    this.workgroupSize = [32, 1, 1], this.outputShape = [t3[0], 1], this.dispatchLayout = webgpu_util_exports.flatDispatchLayout(this.outputShape), this.dispatch = webgpu_util_exports.computeDispatch(this.dispatchLayout, this.outputShape, this.workgroupSize), this.shaderKey = "getpointsConfidenceOp";
  }
  return t2.prototype.getUserCode = function() {
    return "\n        ".concat($e("index"), " {\n          if (index < uniforms.size) {\n            let y = B[index * 2];\n            let x = B[index * 2 + 1];\n            let outIndex = y * uniforms.aShape.x * uniforms.aShape.z + x * uniforms.aShape.z + index;\n            result[index] = A[outIndex];\n          }\n        }\n        ");
  }, t2;
}();
function tn(t2, e) {
  if (backend() instanceof WebGPUBackend) return function(t3, e2) {
    var n = backend(), i = new Je(e2.shape), r = n.runWebGPUProgram(i, [t3, e2], "float32");
    return engine().makeTensorFromTensorInfo(r);
  }(t2, e);
  throw new Error("getPointsConfidenceWebGPU is not supported in this backend!");
}
var en = function() {
  function t2(t3) {
    if (this.variableNames = ["A", "B"], this.size = true, this.supportedLastDimension = 2, 2 !== t3.length || t3[1] !== this.supportedLastDimension) throw new Error("GetOffsetVectorsProgram only supports shape of [x, ".concat(this.supportedLastDimension, "], but current shape is ").concat(t3));
    this.workgroupSize = [32, 1, 1], this.outputShape = t3;
    var e = [t3[0], 1];
    this.dispatchLayout = webgpu_util_exports.flatDispatchLayout(e), this.dispatch = webgpu_util_exports.computeDispatch(this.dispatchLayout, e, this.workgroupSize), this.shaderKey = "GetOffsetVectors";
  }
  return t2.prototype.getUserCode = function() {
    return "\n    fn getOffsetPoint(y: i32, x: i32, index: i32) -> vec2<i32> {\n      let outIndexY = y * uniforms.bShape.x * uniforms.bShape.y + x * uniforms.bShape.y + index;\n      let outIndexX = outIndexY + uniforms.bShape.z;\n      let outY = i32(B[outIndexY]);\n      let outX = i32(B[outIndexX]);\n      return vec2<i32>(outY, outX);\n    }\n\n    ".concat($e("index"), " {\n      if (index < uniforms.size) {\n        let indexY = index * ").concat(this.supportedLastDimension, ";\n        let indexX = indexY + 1;\n        let heatmapY = A[indexY];\n        let heatmapX = A[indexX];\n        let out = getOffsetPoint(i32(heatmapY), i32(heatmapX), index);\n        result[indexY] = f32(out[0]);\n        result[indexX] = f32(out[1]);\n      }\n    }\n    ");
  }, t2;
}();
function nn(t2, e) {
  if (backend() instanceof WebGPUBackend) return function(t3, e2) {
    var n = backend(), i = new en(t3.shape), r = n.runWebGPUProgram(i, [t3, e2], "float32");
    return engine().makeTensorFromTensorInfo(r);
  }(t2, e);
  throw new Error("getOffsetVectorsGPU is not supported in this backend!");
}
function rn(t2) {
  var e = t2.shape, n = e[0], i = e[1], o = e[2];
  return tidy(function() {
    var e2, s, u = reshape(t2, [n * i, o]), l = argMax(u, 0), c = expandDims(div(l, scalar(i, "int32")), 1), p = expandDims((e2 = l, s = i, tidy(function() {
      var t3 = div(e2, scalar(s, "int32"));
      return sub(e2, mul(t3, scalar(s, "int32")));
    })), 1);
    return concat([c, p], 1);
  });
}
function on(t2, e, n) {
  return tidy(function() {
    var i = function(t3, e2) {
      for (var n2 = [], i2 = 0; i2 < U.length; i2++) {
        var r = t3.get(i2, 0).valueOf(), o = t3.get(i2, 1).valueOf(), a = an(r, o, i2, e2), u = a.x, h = a.y;
        n2.push(h), n2.push(u);
      }
      return tensor2d(n2, [U.length, 2]);
    }(t2, n);
    return add(cast(mul(t2.toTensor(), scalar(e, "int32")), "float32"), i);
  });
}
function an(t2, e, n, i) {
  return { y: i.get(t2, e, n), x: i.get(t2, e, n + U.length) };
}
function sn(t2, e, n) {
  return N(this, void 0, void 0, function() {
    var i, r, o, a, s, u, h, l, c, p;
    return D(this, function(f) {
      switch (f.label) {
        case 0:
          return i = 0, r = rn(t2), [4, Promise.all([t2.buffer(), e.buffer(), r.buffer()])];
        case 1:
          return o = f.sent(), a = o[0], s = o[1], u = o[2], [4, (h = on(u, n, s)).buffer()];
        case 2:
          return l = f.sent(), c = Array.from(function(t3, e2) {
            for (var n2 = e2.shape[0], i2 = new Float32Array(n2), r2 = 0; r2 < n2; r2++) {
              var o2 = e2.get(r2, 0), a2 = e2.get(r2, 1);
              i2[r2] = t3.get(o2, a2, r2);
            }
            return i2;
          }(a, u)), p = c.map(function(t3, e2) {
            return i += t3, { y: l.get(e2, 0), x: l.get(e2, 1), score: t3, name: U[e2] };
          }), r.dispose(), h.dispose(), [2, { keypoints: p, score: i / p.length }];
      }
    });
  });
}
function un(t2, e, n) {
  return N(this, void 0, void 0, function() {
    var i, s, u;
    return D(this, function(h) {
      return i = rn(t2), s = function(t3, e2, n2) {
        return tidy(function() {
          var i2 = nn(t3, n2);
          return add(cast(mul(t3, scalar(e2, "int32")), "float32"), i2);
        });
      }(i, n, e), u = tn(t2, i), [2, [s, u]];
    });
  });
}
function hn(t2, e) {
  return (t2 - 1) % e == 0;
}
var ln = "https://storage.googleapis.com/tfjs-models/savedmodel/posenet/mobilenet/";
var cn = "https://storage.googleapis.com/tfjs-models/savedmodel/posenet/resnet50/";
function pn(t2, e) {
  return function(t3, e2) {
    return (t3 - 1) % e2 == 0;
  }(t2, e) ? t2 : Math.floor(t2 / e) * e + 1;
}
var fn = function() {
  function t2(t3, e) {
    this.posenetModel = t3;
    var n = this.posenetModel.inputs[0].shape;
    util_exports.assert(-1 === n[1] && -1 === n[2], function() {
      return "Input shape [".concat(n[1], ", ").concat(n[2], "] ") + "must both be equal to or -1";
    });
    var r, o, a = (r = e.inputResolution, o = e.outputStride, { height: pn(r.height, o), width: pn(r.width, o) });
    !function(t4) {
      util_exports.assert(_e.indexOf(t4) >= 0, function() {
        return "outputStride of ".concat(t4, " is invalid. ") + "It must be either 8 or 16.";
      });
    }(e.outputStride), function(t4, e2) {
      util_exports.assert(hn(t4.height, e2), function() {
        return "height of ".concat(t4.height, " is invalid for output stride ") + "".concat(e2, ".");
      }), util_exports.assert(hn(t4.width, e2), function() {
        return "width of ".concat(t4.width, " is invalid for output stride ") + "".concat(e2, ".");
      });
    }(a, e.outputStride), this.inputResolution = a, this.outputStride = e.outputStride, this.architecture = e.architecture;
  }
  return t2.prototype.estimatePoses = function(t3, e) {
    return void 0 === e && (e = Ae), N(this, void 0, void 0, function() {
      return D(this, function(n) {
        return [2, this.estimatePosesGPU(t3, e, false)];
      });
    });
  }, t2.prototype.estimatePosesGPU = function(t3, e, n) {
    return void 0 === e && (e = Ae), void 0 === n && (n = false), N(this, void 0, void 0, function() {
      var i, r, a, s, u, h, l, c, d, m, g, y, v, x, w, k, b, M;
      return D(this, function(S) {
        switch (S.label) {
          case 0:
            return i = function(t4) {
              var e2 = t4;
              if (null == e2.maxPoses && (e2.maxPoses = 1), e2.maxPoses <= 0) throw new Error("Invalid maxPoses ".concat(e2.maxPoses, ". Should be > 0."));
              if (e2.maxPoses > 1) {
                if ((e2 = B(B({}, ze), e2)).scoreThreshold < 0 || e2.scoreThreshold > 1) throw new Error("Invalid scoreThreshold ".concat(e2.scoreThreshold, ". ") + "Should be in range [0.0, 1.0]");
                if (e2.nmsRadius <= 0) throw new Error("Invalid nmsRadius ".concat(e2.nmsRadius, "."));
              }
              return e2;
            }(e), null == t3 ? [2, n ? [[], []] : []] : (this.maxPoses = i.maxPoses, r = mt(t3, { outputTensorSize: this.inputResolution, keepAspectRatio: true, borderMode: "replicate" }), a = r.imageTensor, s = r.padding, u = "ResNet50" === this.architecture ? add(a, Ce) : dt(a, [-1, 1]), h = this.posenetModel.predict(u), "ResNet50" === this.architecture ? (l = squeeze(h[2], [0]), c = squeeze(h[3], [0]), d = squeeze(h[0], [0]), m = squeeze(h[1], [0])) : (l = squeeze(h[0], [0]), c = squeeze(h[1], [0]), d = squeeze(h[2], [0]), m = squeeze(h[3], [0])), g = sigmoid(c), 1 !== this.maxPoses ? [3, 5] : n ? [4, un(g, l, this.outputStride)] : [3, 2]);
          case 1:
            return v = S.sent(), w = v[0], x = v[1], y = [w, x], [3, 4];
          case 2:
            return [4, sn(g, l, this.outputStride)];
          case 3:
            w = S.sent(), y = [w], S.label = 4;
          case 4:
            return [3, 7];
          case 5:
            if (n) throw new Error("GPU renderer only supports single pose!");
            return [4, Ze(g, l, d, m, this.outputStride, this.maxPoses, i.scoreThreshold, i.nmsRadius)];
          case 6:
            y = S.sent(), S.label = 7;
          case 7:
            if (n) {
              if (true === i.flipHorizontal) throw new Error("flipHorizontal is not supported!");
              k = this.getCanvasInfo(rt(t3), this.inputResolution, s);
            } else M = rt(t3), b = function(t4, e2, n2, i2) {
              var r2 = e2.height, o = e2.width, a2 = r2 / (n2.height * (1 - i2.top - i2.bottom)), s2 = o / (n2.width * (1 - i2.left - i2.right)), u2 = -i2.top * n2.height, h2 = -i2.left * n2.width;
              if (1 === s2 && 1 === a2 && 0 === u2 && 0 === h2) return t4;
              for (var l2 = 0, c2 = t4; l2 < c2.length; l2++) for (var p = 0, f = c2[l2].keypoints; p < f.length; p++) {
                var d2 = f[p];
                d2.x = (d2.x + h2) * s2, d2.y = (d2.y + u2) * a2;
              }
              return t4;
            }(y, M, this.inputResolution, s), i.flipHorizontal && (b = function(t4, e2) {
              for (var n2 = 0, i2 = t4; n2 < i2.length; n2++) for (var r2 = 0, o = i2[n2].keypoints; r2 < o.length; r2++) {
                var a2 = o[r2];
                a2.x = e2.width - 1 - a2.x;
              }
              return t4;
            }(b, M));
            return a.dispose(), u.dispose(), dispose(h), l.dispose(), c.dispose(), d.dispose(), m.dispose(), g.dispose(), [2, n ? [y, k] : b];
        }
      });
    });
  }, t2.prototype.getCanvasInfo = function(t3, e, n) {
    var i = t3.height, r = t3.width, o = i / (e.height * (1 - n.top - n.bottom)), a = r / (e.width * (1 - n.left - n.right)), s = -n.top * e.height;
    return [-n.left * e.width, s, a, o, t3.width, t3.height];
  }, t2.prototype.dispose = function() {
    this.posenetModel.dispose();
  }, t2.prototype.reset = function() {
  }, t2;
}();
function dn(t2) {
  return void 0 === t2 && (t2 = Te), N(this, void 0, void 0, function() {
    var e, n, i, r, o;
    return D(this, function(a) {
      switch (a.label) {
        case 0:
          return "ResNet50" !== (e = function(t3) {
            var e2 = t3 || Te;
            if (null == e2.architecture && (e2.architecture = "MobileNetV1"), Pe.indexOf(e2.architecture) < 0) throw new Error("Invalid architecture ".concat(e2.architecture, ". ") + "Should be one of ".concat(Pe));
            if (null == e2.inputResolution && (e2.inputResolution = { height: 257, width: 257 }), null == e2.outputStride && (e2.outputStride = 16), Fe[e2.architecture].indexOf(e2.outputStride) < 0) throw new Error("Invalid outputStride ".concat(e2.outputStride, ". ") + "Should be one of ".concat(Fe[e2.architecture], " ") + "for architecture ".concat(e2.architecture, "."));
            if (null == e2.multiplier && (e2.multiplier = 1), Oe[e2.architecture].indexOf(e2.multiplier) < 0) throw new Error("Invalid multiplier ".concat(e2.multiplier, ". ") + "Should be one of ".concat(Oe[e2.architecture], " ") + "for architecture ".concat(e2.architecture, "."));
            if (null == e2.quantBytes && (e2.quantBytes = 4), Ie.indexOf(e2.quantBytes) < 0) throw new Error("Invalid quantBytes ".concat(e2.quantBytes, ". ") + "Should be one of ".concat(Ie, " ") + "for architecture ".concat(e2.architecture, "."));
            if ("MobileNetV1" === e2.architecture && 32 === e2.outputStride && 1 !== e2.multiplier) throw new Error("When using an output stride of 32, you must select 1 as the multiplier.");
            return e2;
          }(t2)).architecture ? [3, 2] : (s = e.outputStride, u = e.quantBytes, h = "model-stride".concat(s, ".json"), n = 4 === u ? cn + "float/" + h : cn + "quant".concat(u, "/") + h, [4, loadGraphModel(e.modelUrl || n)]);
        case 1:
          return i = a.sent(), [2, new fn(i, e)];
        case 2:
          return r = function(t3, e2, n2) {
            var i2 = { 1: "100", 0.75: "075", 0.5: "050" }, r2 = "model-stride".concat(t3, ".json");
            return 4 === n2 ? ln + "float/".concat(i2[e2], "/") + r2 : ln + "quant".concat(n2, "/").concat(i2[e2], "/") + r2;
          }(e.outputStride, e.multiplier, e.quantBytes), [4, loadGraphModel(e.modelUrl || r)];
        case 3:
          return o = a.sent(), [2, new fn(o, e)];
      }
      var s, u, h;
    });
  });
}
function mn(t2, e) {
  return N(this, void 0, void 0, function() {
    var n, i;
    return D(this, function(r) {
      switch (t2) {
        case se.PoseNet:
          return [2, dn(e)];
        case se.BlazePose:
          if (i = void 0, null != (n = e)) {
            if ("tfjs" === n.runtime) return [2, oe(e)];
            if ("mediapipe" === n.runtime) return [2, it(e)];
            i = n.runtime;
          }
          throw new Error("Expect modelConfig.runtime to be either 'tfjs' " + "or 'mediapipe', but got ".concat(i));
        case se.MoveNet:
          return [2, Se(e)];
        default:
          throw new Error("".concat(t2, " is not a supported model name."));
      }
    });
  });
}
var gn = { keypointsToNormalizedKeypoints: At };
var yn = { modelType: { SINGLEPOSE_LIGHTNING: "SinglePose.Lightning", SINGLEPOSE_THUNDER: "SinglePose.Thunder", MULTIPOSE_LIGHTNING: "MultiPose.Lightning" } };
export {
  se as SupportedModels,
  ae as TrackerType,
  gn as calculators,
  mn as createDetector,
  yn as movenet,
  pe as util
};
/*! Bundled license information:

@tensorflow-models/pose-detection/dist/pose-detection.esm.js:
  (**
      * @license
      * Copyright 2023 Google LLC. All Rights Reserved.
      * Licensed under the Apache License, Version 2.0 (the "License");
      * you may not use this file except in compliance with the License.
      * You may obtain a copy of the License at
      *
      * http://www.apache.org/licenses/LICENSE-2.0
      *
      * Unless required by applicable law or agreed to in writing, software
      * distributed under the License is distributed on an "AS IS" BASIS,
      * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      * See the License for the specific language governing permissions and
      * limitations under the License.
      * =============================================================================
      *)
*/
//# sourceMappingURL=@tensorflow-models_pose-detection.js.map
