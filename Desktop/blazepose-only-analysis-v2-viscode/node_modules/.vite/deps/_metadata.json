{"hash": "5a63bbfc", "configHash": "90994f4e", "lockfileHash": "571eb5b5", "browserHash": "b432580d", "optimized": {"@tensorflow/tfjs": {"src": "../../@tensorflow/tfjs/dist/index.js", "file": "@tensorflow_tfjs.js", "fileHash": "48f70260", "needsInterop": false}, "@tensorflow/tfjs-backend-webgpu": {"src": "../../@tensorflow/tfjs-backend-webgpu/dist/index.js", "file": "@tensorflow_tfjs-backend-webgpu.js", "fileHash": "038d17e1", "needsInterop": false}, "@tensorflow-models/pose-detection": {"src": "../../@tensorflow-models/pose-detection/dist/pose-detection.esm.js", "file": "@tensorflow-models_pose-detection.js", "fileHash": "88cdae0b", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f49c6727", "needsInterop": true}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "b171162a", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "911f6984", "needsInterop": false}, "@radix-ui/react-toggle": {"src": "../../@radix-ui/react-toggle/dist/index.mjs", "file": "@radix-ui_react-toggle.js", "fileHash": "e9d1e6c1", "needsInterop": false}, "@radix-ui/react-toggle-group": {"src": "../../@radix-ui/react-toggle-group/dist/index.mjs", "file": "@radix-ui_react-toggle-group.js", "fileHash": "687c09a8", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "9b14c22e", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "cdc81ef1", "needsInterop": false}, "@tensorflow/tfjs-converter": {"src": "../../@tensorflow/tfjs-converter/dist/index.js", "file": "@tensorflow_tfjs-converter.js", "fileHash": "7049f75c", "needsInterop": false}, "@tensorflow/tfjs-core": {"src": "../../@tensorflow/tfjs-core/dist/index.js", "file": "@tensorflow_tfjs-core.js", "fileHash": "a91dc84f", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "a6d76e88", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "8103d5db", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "33f084fa", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "b13343ac", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "293842cb", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "536e1dc5", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "1cfecd79", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c36cb7c6", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "1a7799a6", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "6be16d97", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-K276JTJK": {"file": "chunk-K276JTJK.js"}, "chunk-VBRHBFGJ": {"file": "chunk-VBRHBFGJ.js"}, "chunk-F63W4NW4": {"file": "chunk-F63W4NW4.js"}, "chunk-AFFYF5PH": {"file": "chunk-AFFYF5PH.js"}, "chunk-RZMLQGVZ": {"file": "chunk-RZMLQGVZ.js"}, "chunk-YZNGQWAF": {"file": "chunk-YZNGQWAF.js"}, "chunk-FWUY5KVM": {"file": "chunk-FWUY5KVM.js"}, "chunk-L62PXFT2": {"file": "chunk-L62PXFT2.js"}, "chunk-WIFN2VF7": {"file": "chunk-WIFN2VF7.js"}, "chunk-OSNC2VYY": {"file": "chunk-OSNC2VYY.js"}, "chunk-GV6UPHID": {"file": "chunk-GV6UPHID.js"}, "chunk-POAQ2H4K": {"file": "chunk-POAQ2H4K.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}