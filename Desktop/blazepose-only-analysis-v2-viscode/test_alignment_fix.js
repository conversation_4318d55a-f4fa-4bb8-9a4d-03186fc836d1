// Test script to verify the alignment points fix
// This simulates the detector detection (no keypoints) vs landmark detection (with keypoints) scenarios

const { calculateAlignmentPointsRects } = require('./src/shared/calculators/calculate_alignment_points_rects.ts');

// Mock image size
const imageSize = { width: 1080, height: 1920 };

// Mock config
const config = {
  rotationVectorStartKeypointIndex: 0,
  rotationVectorEndKeypointIndex: 1,
  rotationVectorTargetAngleDegree: 90
};

console.log('🧪 Testing Alignment Points Fix...\n');

// Test 1: Detector detection (no keypoints) - should use bounding box fallback
console.log('📋 Test 1: Detector Detection (No Keypoints)');
const detectorDetection = {
  score: 0.95,
  boundingBox: { xMin: 0.3, yMin: 0.2, xMax: 0.7, yMax: 0.8, width: 0.4, height: 0.6 },
  locationData: {
    format: 'RELATIVE_BOUNDING_BOX',
    relativeBoundingBox: { xMin: 0.3, yMin: 0.2, xMax: 0.7, yMax: 0.8, width: 0.4, height: 0.6 }
    // No relativeKeypoints - this is normal for detector detections
  }
};

try {
  const result1 = calculateAlignmentPointsRects(detectorDetection, imageSize, config);
  console.log('✅ Detector detection handled successfully:', result1);
} catch (error) {
  console.log('❌ Detector detection failed:', error.message);
}

console.log('\n📋 Test 2: Landmark Detection (With Keypoints)');
// Test 2: Landmark detection (with keypoints) - should use keypoint-based alignment
const landmarkDetection = {
  score: 0.98,
  boundingBox: { xMin: 0.35, yMin: 0.25, xMax: 0.65, yMax: 0.75, width: 0.3, height: 0.5 },
  locationData: {
    format: 'RELATIVE_BOUNDING_BOX',
    relativeBoundingBox: { xMin: 0.35, yMin: 0.25, xMax: 0.65, yMax: 0.75, width: 0.3, height: 0.5 },
    relativeKeypoints: [
      { x: 0.5, y: 0.3 }, // Keypoint 0 (start)
      { x: 0.5, y: 0.7 }  // Keypoint 1 (end)
    ]
  }
};

try {
  const result2 = calculateAlignmentPointsRects(landmarkDetection, imageSize, config);
  console.log('✅ Landmark detection handled successfully:', result2);
} catch (error) {
  console.log('❌ Landmark detection failed:', error.message);
}

console.log('\n🎯 Test Summary:');
console.log('- Detector detections (no keypoints) should use bounding box fallback');
console.log('- Landmark detections (with keypoints) should use precise keypoint alignment');
console.log('- Both should work without errors');
