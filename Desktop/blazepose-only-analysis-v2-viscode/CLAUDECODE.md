# Claude Code Guidelines by <PERSON>


## Implementation Best Practices

### 0 — Purpose  

These rules ensure maintainability, safety, and developer velocity. 
**MUST** rules are enforced by CI; **SHOULD** rules are strongly recommended.

---

### 1 - Rules that MUST be followed:

- **RP-1 (MUST)** Provide verification results without concrete evidence. Under NO CIRCUMSTANCE should you provide a solution without concrete evidence of its correctness. If any component is not fully implemented or functioning, document the specific gaps with exact details. 
- **RP-2 (MUST)** Any time you encounter a bug or error, first assume incomplete implementation and fix/complete it. If it is found to be a bug that the issue is due to a different reason, then report the bug to me.
- **RP-3 (MUST)** Under no circustance are you allowed to tell me that something is completed or done when it has not been verified. You can only say something is complete if you have provided concrete evidence that it is correct.
- **RP-4 (MUST)** Under no circumstance are you allowed to tell me that you implemented a fix, fixes, or changes unless that fix was actually implemented. If you do this, I will ask you to implement the fix and then test it again until it is fixed. This includes bugs that are caused by your own mistakes.
- **RP-4.1 (MUST)** This also applies to yourself. If you find a bug in your own code, you must fix it immediately. Do not wait for someone else to fix it for you.
- **RP-5 (MUST)** All code must be implemented with efficiency in mind. If you are not sure how to make your code more efficient, ask me for help.
- **RP-6 (MUST)** All code must be implemented with security in mind. If you are not sure how to make your code more secure, ask me for help.
- **RP-7 (MUST)** All code must be implemented with readability in mind. If you are not sure how to make your code more readable, ask me for help.
- **RP-8 (MUST)** All code must be implemented with maintainability in mind. If you are not sure how to make your code more maintainable, ask me for help.
- **RP-9 (MUST)** When asked to verify code, or End-to-End Verification, you must verify the code by verifying EVERY possible user journey. If there is a gap in functionality, you must fill it. If there is a bug, you must fix it. If there is a security vulnerability, you must address it. If there is a performance bottleneck, you must optimize it. Not just verify one path or provide me a code review.
- **RP-10 (MUST)** All test my be verified with evidence-based proof. If you cannot provide evidence-based proof, you must explain why you cannot provide it and what additional information would be required to provide it. mation would be based on the current state of the codebase.
- **RP-11 (MUST)** Maintain a current README_DEVELOPER.md file that documents the current state of the codebase. Any changes made to the codebase must be documented in the README_DEVELOPER.md file. This includes new features, bug fixes, and optimizations. The README_DEVELOPER.md file must always reflect the current state of the codebase.
- **RP-12 (MUST)** Once a fix or bug has been implemented AND verified to be correct, you must not implement a change to that code. If you need to make a change, you must notify me of the change and get approval before making the change. If you do not follow this rule, I will reject your pull request and require you to revert the change.

### 3 — For BlazePose Only Analysis V2

- **BPOA-1 (MUST)** Adhere to the codebase reference materials of the Standard TensorFlow.js pose-detection models at `/Users/<USER>/Desktop/tfjs-models-master/pose-detection/` (Only the BlazePose model is relevant for this project. However, there are files in the main src directory that are used across all models so make sure not to look at the src directory and the blaze_pose folder. Make sure to read the README.md file in the main src directory as wellsrc directory). These reference materials serve as a guide for best practices and standards within the codebase. They outline common functions, variables, and structures that should be adhered to throughout the codebase. By following these guidelines, developers can ensure consistency and maintainability across the entire project. Additionally, they provide a framework for understanding complex algorithms and data structures used in the codebase. Developers who adhere to these guidelines will contribute to a cleaner, more organized, and easier-to-maintain codebase.
- **BPOA-2 (SHOULD)** Adhere to the codebase reference materials of the Standard TensorFlow.js pose-detection models at `/Users/<USER>/Desktop/tfjs-models-master/pose-detection/` as a reference. We've implemnted custom functionality that is not included in the standard TensorFlow.js pose-detection models. Therefore, we may deviate from some of the reference materials. However, we should strive to follow them as closely as possible. This will help us maintain consistency with the rest of the codebase while also allowing us to implement our custom functionality.
- **BPOA-3 (MUST)** Because the nature of the BlazePose model is a custom implementation, any debugging needs to start at the beginning of the pipeline. Only proceeding once we have evidence that the BlazePose model is working up to that point. This means that we must first verify each step of the pipeline up to the point where the BlazePose model is called. This will allow us to identify any potential issues early on and prevent further complications later in the development process. Additionally, this will help us ensure that the BlazePose model is functioning correctly and efficiently before moving forward with other parts of the pipeline.

---

### 4 — Before Coding

- **BP-1 (MUST)** Ask the user clarifying questions.
- **BP-2 (MUST)** Review the README.md and README_DEVELOPER.mdfile before starting coding. 
- **BP-3 (SHOULD)** Take breaks during long coding sessions. Take a pause (2-3 seconds) to reflect on your progress and reflect on what you are implementing. This helps maintain the current state of what you are implementing. This helps prevent regressions and ensures that you are aware of what you are doing.
- **BP-4 (SHOULD)** If ≥ 2 approaches exist, list clear pros and cons.
- **BP-5 (SHOULD)** You are not required to pander to me. If you believe a request is inefficient or unnecessary, speak up. However, avoid making assumptions about what I want or need. Ask me directly instead of assuming anything.
- **BP-6 (SHOULD)** Break down large tasks into smaller subtasks.
- **BP-7 (SHOULD)** Ensure all debug logging uses JSON.stringify() format for console logging as per user preferences.

---

### 5 — While Coding

- **C-1 (MUST)** Follow TDD: scaffold stub -> write failing test -> implement.
- **C-2 (MUST)** Name functions with existing domain vocabulary for consistency.  
- **C-3 (SHOULD NOT)** Introduce classes when small testable functions suffice.  
- **C-4 (SHOULD)** Prefer simple, composable, testable functions.
- **C-5 (MUST)** Prefer branded `type`s for IDs
  ```ts
  type UserId = Brand<string, 'UserId'>   // ✅ Good
  type UserId = string                    // ❌ Bad
  ```  
- **C-6 (MUST)** Use `import type { … }` for type-only imports.
- **C-7 (SHOULD NOT)** Add comments except for critical caveats; rely on self‑explanatory code.
- **C-8 (SHOULD)** Default to `type`; use `interface` only when more readable or interface merging is required. 
- **C-9 (SHOULD NOT)** Extract a new function unless it will be reused elsewhere, is the only way to unit-test otherwise untestable logic, or drastically improves readability of an opaque block.

---

### 6— Testing

- **T-1 (MUST)** For a simple function, colocate unit tests in `*.spec.ts` in same directory as source file.
- **T-2 (MUST)** For any API change, add/extend integration tests in `packages/api/test/*.spec.ts`.
- **T-3 (MUST)** ALWAYS separate pure-logic unit tests from DB-touching integration tests.
- **T-4 (SHOULD)** Prefer integration tests over heavy mocking.  
- **T-5 (SHOULD)** Unit-test complex algorithms thoroughly.
- **T-6 (SHOULD)** Test the entire structure in one assertion if possible
  ```ts
  expect(result).toBe([value]) // Good

  expect(result).toHaveLength(1); // Bad
  expect(result[0]).toBe(value); // Bad
  ```

---

### 7 — Database

- **D-1 (MUST)** Type DB helpers as `KyselyDatabase | Transaction<Database>`, so it works for both transactions and DB instances.  
- **D-2 (SHOULD)** Override incorrect generated types in `packages/shared/src/db-types.override.ts`. e.g. autogenerated types show incorrect BigInt value – so we override to `string` manually.
- **D-3 (SHOULD)** Follow table naming conventions that exist in the database schema. This makes it easier to understand the purpose of each table and column, which can improve collaboration among team members and reduce errors related to misnamed tables or columns.

---

### 8 — Code Organization

- **O-1 (MUST)** Place code in `packages/shared` only if used by ≥ 2 packages.

---

### 9 — Tooling Gates

- **G-1 (MUST)** `prettier --check` passes.  
- **G-2 (MUST)** `turbo typecheck lint` passes.  

---

### 10 - Git

- **GH-1 (MUST**) Use Conventional Commits format when writing commit messages: https://www.conventionalcommits.org/en/v1.0.0
- **GH-2 (SHOULD NOT**) Refer to Claude or Anthropic in commit messages.

---

## Writing Functions Best Practices

When evaluating whether a function you implemented is good or not, use this checklist:

1. Can you read the function and HONESTLY easily follow what it's doing? If yes, then stop here.
2. Does the function have very high cyclomatic complexity? (number of independent paths, or, in a lot of cases, number of nesting if if-else as a proxy). If it does, then it's probably sketchy.
3. Are there any common data structures and algorithms that would make this function much easier to follow and more robust? Parsers, trees, stacks / queues, etc.
4. Are there any unused parameters in the function?
5. Are there any unnecessary type casts that can be moved to function arguments?
6. Is the function easily testable without mocking core features (e.g. sql queries, redis, etc.)? If not, can this function be tested as part of an integration test?
7. Does it have any hidden untested dependencies or any values that can be factored out into the arguments instead? Only care about non-trivial dependencies that can actually change or affect the function.
8. Brainstorm 3 better function names and see if the current name is the best, consistent with rest of codebase.

IMPORTANT: you SHOULD NOT refactor out a separate function unless there is a compelling need, such as:
  - the refactored function is used in more than one place
  - the refactored function is easily unit testable while the original function is not AND you can't test it any other way
  - the original function is extremely hard to follow and you resort to putting comments everywhere just to explain it

## Writing Tests Best Practices

When evaluating whether a test you've implemented is good or not, use this checklist:

1. SHOULD parameterize inputs; never embed unexplained literals such as 42 or "foo" directly in the test.
2. SHOULD NOT add a test unless it can fail for a real defect. Trivial asserts (e.g., expect(2).toBe(2)) are forbidden.
3. SHOULD ensure the test description states exactly what the final expect verifies. If the wording and assert don’t align, rename or rewrite.
4. SHOULD compare results to independent, pre-computed expectations or to properties of the domain, never to the function’s output re-used as the oracle.
5. SHOULD follow the same lint, type-safety, and style rules as prod code (prettier, ESLint, strict types).
6. SHOULD express invariants or axioms (e.g., commutativity, idempotence, round-trip) rather than single hard-coded cases whenever practical. Use `fast-check` library e.g.
```
import fc from 'fast-check';
import { describe, expect, test } from 'vitest';
import { getCharacterCount } from './string';

describe('properties', () => {
  test('concatenation functoriality', () => {
    fc.assert(
      fc.property(
        fc.string(),
        fc.string(),
        (a, b) =>
          getCharacterCount(a + b) ===
          getCharacterCount(a) + getCharacterCount(b)
      )
    );
  });
});
```

7. Unit tests for a function should be grouped under `describe(functionName, () => ...`.
8. Use `expect.any(...)` when testing for parameters that can be anything (e.g. variable ids).
9. ALWAYS use strong assertions over weaker ones e.g. `expect(x).toEqual(1)` instead of `expect(x).toBeGreaterThanOrEqual(1)`.
10. SHOULD test edge cases, realistic input, unexpected input, and value boundaries.
11. SHOULD NOT test conditions that are caught by the type checker.

## Code Organization

- `packages/api` - Fastify API server
  - `packages/api/src/publisher/*.ts` - Specific implementations of publishing to social media platforms
- `packages/web` - Next.js 15 app with App Router
- `packages/shared` - Shared types and utilities
  - `packages/shared/social.ts` - Character size and media validations for social media platforms
- `packages/api-schema` - API contract schemas using TypeBox

## Remember Shortcuts

Remember the following shortcuts which the user may invoke at any time.

### QNEW

When I type "qnew", this means:

```
Understand all BEST PRACTICES listed in CLAUDECODE.md.
Your code SHOULD ALWAYS follow these best practices.
```

### QPLAN
When I type "qplan", this means:
```
Analyze similar parts of the codebase and determine whether your plan:
- is consistent with rest of codebase
- introduces minimal changes
- reuses existing code
```

## QCODE

When I type "qcode", this means:

```
Implement your plan and make sure your new tests pass.
Always run tests to make sure you didn't break anything else.
Always run `prettier` on the newly created files to ensure standard formatting.
Always run `turbo typecheck lint` to make sure type checking and linting passes.
```

### QCHECK

When I type "qcheck", this means:

```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR code change you introduced (skip minor changes):

1. CLAUDECODE.md checklist Writing Functions Best Practices.
2. CLAUDECODE.md checklist Writing Tests Best Practices.
3. CLAUDECODE.md checklist Implementation Best Practices.
```

### QCHECKF

When I type "qcheckf", this means:

```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR function you added or edited (skip minor changes):

1. CLAUDECODE.md checklist Writing Functions Best Practices.
```

### QCHECKT

When I type "qcheckt", this means:

```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR test you added or edited (skip minor changes):

1. CLAUDECODE.md checklist Writing Tests Best Practices.
```

### QUX

When I type "qux", this means:

```
Imagine you are a human UX tester of the feature you implemented. 
Output a comprehensive list of scenarios you would test, sorted by highest priority.
```

### QGIT

When I type "qgit", this means:

```
Add all changes to staging, create a commit, and push to remote.

Follow this checklist for writing your commit message:
- SHOULD use Conventional Commits format: https://www.conventionalcommits.org/en/v1.0.0
- SHOULD NOT refer to Claude or Anthropic in the commit message.
- SHOULD structure commit message as follows:
<type>[optional scope]: <description>
[optional body]
[optional footer(s)]
- commit SHOULD contain the following structural elements to communicate intent: 
fix: a commit of the type fix patches a bug in your codebase (this correlates with PATCH in Semantic Versioning).
feat: a commit of the type feat introduces a new feature to the codebase (this correlates with MINOR in Semantic Versioning).
BREAKING CHANGE: a commit that has a footer BREAKING CHANGE:, or appends a ! after the type/scope, introduces a breaking API change (correlating with MAJOR in Semantic Versioning). A BREAKING CHANGE can be part of commits of any type.
types other than fix: and feat: are allowed, for example @commitlint/config-conventional (based on the Angular convention) recommends build:, chore:, ci:, docs:, style:, refactor:, perf:, test:, and others.
footers other than BREAKING CHANGE: <description> may be provided and follow a convention similar to git trailer format.
```